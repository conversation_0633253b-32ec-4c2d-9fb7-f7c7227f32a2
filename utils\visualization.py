
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.io as pio
import numpy as np
import tkinter as tk
from tkinter import filedialog, simpledialog, messagebox
import segyio
import math
import logging
import tempfile
import os
from utils.general_utils import find_traces_near_polyline
from utils.processing import calculate_woss # calculate_woss is already in utils.processing

# --- Ported from backup/processing.py ---
def add_output_to_subplot(fig, output, trace_data_item, descriptors, time_vector, row, col, plot_settings, hfc_p95, spec_decrease_p95, global_vmin=None, global_vmax=None, use_shared_colorbar=False, is_last_subplot=False):
    """Helper function to add a specific output to a subplot.

    This function handles adding different types of outputs (seismic amplitude, spectrograms, etc.)
    to a subplot in a figure. It applies the time and frequency limits from plot_settings.

    Args:
        fig: Plotly figure object to add the subplot to
        output: String indicating which output to add
        trace_data_item: Dictionary containing trace data
        descriptors: Dictionary containing spectral descriptors
        time_vector: Time vector for the y-axis
        row: Row index for the subplot
        col: Column index for the subplot
        plot_settings: Dictionary of plot settings
        hfc_p95: 95th percentile of HFC for normalization
        spec_decrease_p95: 95th percentile of spectral decrease for normalization
        global_vmin: Global minimum value for colormap (used for shared colormaps)
        global_vmax: Global maximum value for colormap (used for shared colormaps)
        use_shared_colorbar: If True, only show colorbar on the last subplot
        is_last_subplot: If True, this is the last subplot in the row (used for shared colorbar)
    """
    # Get time and frequency limits from plot settings
    # No need to create local variables since the code already uses plot_settings directly
    if output == "Seismic Amplitude" or output == "Input Signal":
        fig.add_trace(go.Scatter(x=trace_data_item['trace_sample'], y=time_vector, mode='lines'), row=row, col=col)
        # Use the cmap_min/cmap_max values from Step 2 if available
        min_val = plot_settings.get('input_signal_cmap_min', plot_settings.get('input_signal_min', -1))
        max_val = plot_settings.get('input_signal_cmap_max', plot_settings.get('input_signal_max', 1))
        fig.update_xaxes(title_text='Amplitude', row=row, col=col, range=[min_val, max_val])
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])

    elif output == "Time-Frequency Magnitude Spectrogram" or output == "Magnitude Spectrogram" or output == "Time-Frequency Magnitude Spectrogram":
        # Check if we're in batched mode
        batched_mode = plot_settings.get('batched_mode', False)

        # Check if mag is available in descriptors and we're not in batched mode
        if not batched_mode and 'mag' in descriptors and isinstance(descriptors['mag'], np.ndarray):
            mag = descriptors['mag']
            freqst = descriptors['freqst']

            # Use frequency limits from Step 2
            freq_min = plot_settings.get('Frequency', [0, 100])[0]
            freq_max = plot_settings.get('Frequency', [0, 100])[1]

            # Find the index corresponding to the max frequency
            freq_limit_index = np.argmin(np.abs(freqst - freq_max)) if len(freqst) > 0 else len(freqst)

            # Get colormap settings from Step 2
            key_base = "magnitude_spectrogram"
            cmap_name = plot_settings.get(f"{key_base}_cmap_name", 'rainbow')

            # Use global min/max values if provided, otherwise use local settings
            if global_vmin is not None and global_vmax is not None:
                cmap_min = global_vmin
                cmap_max = global_vmax
                logging.info(f"Using global min/max for Magnitude Spectrogram: {cmap_min}, {cmap_max}")
            else:
                cmap_min = plot_settings.get(f"{key_base}_cmap_min", 0)
                cmap_max = plot_settings.get(f"{key_base}_cmap_max", 1)

            # Use the colormap from settings if available
            colorscale = plot_settings.get('colormap', plot_settings.get('magnitude_spectrogram_colormap', cmap_name))

            # Determine whether to show colorbar based on shared colorbar setting
            show_colorbar = not use_shared_colorbar or (use_shared_colorbar and is_last_subplot)
            # Remove shared colorbar for Magnitude Spectrogram in comparative mode
            if use_shared_colorbar:
                show_colorbar = False

            # Create heatmap trace with or without colorbar
            heatmap_args = {
                'z': mag[:freq_limit_index, :].T,
                'x': freqst[:freq_limit_index],
                'y': time_vector,
                'colorscale': colorscale,
                'zmin': cmap_min,
                'zmax': cmap_max,
            }

            # Only add colorbar if we're showing it
            if show_colorbar:
                heatmap_args['colorbar'] = dict(
                    title=dict(
                        text='Time-Frequency<br>Magnitude<br>Spectrogram',  # Wrapped colorbar title
                        side='right',
                        font=dict(size=12)  # Move titlefont into title dictionary as font
                    ),
                    x=1.15,  # Fixed position for shared colorbar
                    y=0.5,
                    len=0.5,  # Make the colorbar slightly longer for better visibility
                    yanchor='middle',
                    xanchor='left',
                    tickfont=dict(size=10)
                )

            fig.add_trace(go.Heatmap(**heatmap_args), row=row, col=col)
            # Use frequency range from Step 2 for x-axis
            fig.update_xaxes(title_text='Frequency (Hz)', row=row, col=col, range=[freq_min, freq_max])
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
        else:
            # If mag is not available or we're in batched mode, display a message
            fig.add_annotation(
                text="Time-Frequency Magnitude Spectrogram<br>not available in batched processing mode",
                x=0.5, y=0.5,
                xref=f"x{col}", yref=f"y{col}",
                showarrow=False,
                font=dict(size=12, color="red")
            )
            # Add an empty trace to ensure the subplot is created
            fig.add_trace(go.Scatter(x=[None], y=[None], mode='lines', visible=False), row=row, col=col)
            # Use frequency range from Step 2 for x-axis
            freq_min = plot_settings.get('Frequency', [0, 100])[0]
            freq_max = plot_settings.get('Frequency', [0, 100])[1]
            fig.update_xaxes(title_text='Frequency (Hz)', row=row, col=col, range=[freq_min, freq_max])
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Magnitude * Voice Spectrogram" or output == "Magnitude * Voice" or output == "Magnitude*Voice":
        # Check if we're in batched mode
        batched_mode = plot_settings.get('batched_mode', False)

        # Check if mag_voice is available in descriptors and we're not in batched mode
        if not batched_mode and 'mag_voice' in descriptors and isinstance(descriptors['mag_voice'], np.ndarray):
            mag_voice = descriptors['mag_voice']
            freqst = descriptors['freqst']

            # Use frequency limits from Step 2
            freq_min = plot_settings.get('Frequency', [0, 100])[0]
            freq_max = plot_settings.get('Frequency', [0, 100])[1]

            # Find the index corresponding to the max frequency
            freq_limit_index = np.argmin(np.abs(freqst - freq_max)) if len(freqst) > 0 else len(freqst)

            # Get colormap settings from Step 2
            key_base = "magnitude__voice"
            cmap_name = plot_settings.get(f"{key_base}_cmap_name", 'rainbow')

            # Use global min/max values if provided, otherwise use local settings
            if global_vmin is not None and global_vmax is not None:
                cmap_min = global_vmin
                cmap_max = global_vmax
                logging.info(f"Using global min/max for Magnitude * Voice: {cmap_min}, {cmap_max}")
            else:
                cmap_min = plot_settings.get(f"{key_base}_cmap_min", -1)
                cmap_max = plot_settings.get(f"{key_base}_cmap_max", 1)

            # Use the colormap from settings if available
            colorscale = plot_settings.get('colormap_mag_voice', plot_settings.get('magnitude_voice_colormap', cmap_name))

            # Determine whether to show colorbar based on shared colorbar setting
            show_colorbar = not use_shared_colorbar or (use_shared_colorbar and is_last_subplot)
            # Remove shared colorbar for Magnitude * Voice Spectrogram in comparative mode
            if use_shared_colorbar:
                show_colorbar = False

            # Create heatmap trace with or without colorbar
            heatmap_args = {
                'z': mag_voice[:freq_limit_index, :].T,
                'x': freqst[:freq_limit_index],
                'y': time_vector,
                'colorscale': colorscale,
                'zmin': cmap_min,
                'zmax': cmap_max,
            }

            # Only add colorbar if we're showing it
            if show_colorbar:
                heatmap_args['colorbar'] = dict(
                    title=dict(
                        text='Magnitude<br>* Voice<br>Spectrogram',  # Wrapped colorbar title
                        side='right',
                        font=dict(size=12)  # Move titlefont into title dictionary as font
                    ),
                    x=1.20,  # Fixed position for shared colorbar for Magnitude * Voice (slightly to the right of Magnitude Spectrogram)
                    y=0.5,
                    len=0.5,  # Make the colorbar slightly longer for better visibility
                    yanchor='middle',
                    xanchor='left',
                    tickfont=dict(size=10)
                )

            fig.add_trace(go.Heatmap(**heatmap_args), row=row, col=col)
            # Use frequency range from Step 2 for x-axis
            fig.update_xaxes(title_text='Frequency (Hz)', row=row, col=col, range=[freq_min, freq_max])
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
        else:
            # If mag_voice is not available or we're in batched mode, display a message
            fig.add_annotation(
                text="Magnitude * Voice Spectrogram<br>not available in batched processing mode",
                x=0.5, y=0.5,
                xref=f"x{col}", yref=f"y{col}",
                showarrow=False,
                font=dict(size=12, color="red")
            )
            # Add an empty trace to ensure the subplot is created
            fig.add_trace(go.Scatter(x=[None], y=[None], mode='lines', visible=False), row=row, col=col)
            # Use frequency range from Step 2 for x-axis
            freq_min = plot_settings.get('Frequency', [0, 100])[0]
            freq_max = plot_settings.get('Frequency', [0, 100])[1]
            fig.update_xaxes(title_text='Frequency (Hz)', row=row, col=col, range=[freq_min, freq_max])
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Normalized dominant frequencies":
        peak_freq = descriptors['peak_freq']
        spec_centroid = descriptors['spec_centroid']
        fdom = descriptors['fdom']
        fig.add_trace(go.Scatter(x=peak_freq, y=time_vector, mode='lines', name='Peak Frequency'), row=row, col=col)
        fig.add_trace(go.Scatter(x=spec_centroid, y=time_vector, mode='lines', name='Spectral Centroid'), row=row, col=col)
        fig.add_trace(go.Scatter(x=fdom, y=time_vector, mode='lines', name='Dominant Frequency'), row=row, col=col)

        # Use frequency range from Step 2 for x-axis
        # First check if 'Frequency' key exists (which is the tuple created from freq_min and freq_max)
        if 'Frequency' in plot_settings:
            freq_range = plot_settings['Frequency']
        else:
            # If 'Frequency' doesn't exist, try to use freq_min and freq_max directly
            freq_min = plot_settings.get('freq_min', 0.0)
            freq_max = plot_settings.get('freq_max', 100.0)
            freq_range = [freq_min, freq_max]

        fig.update_xaxes(title_text='Frequency (Hz)', row=row, col=col, range=freq_range)
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Magnitude * Voice Slope Attribute" or output == "Mag*Voice Slope":
        mag_voice_slope = descriptors['mag_voice_slope']
        fig.add_trace(go.Scatter(x=mag_voice_slope, y=time_vector, mode='lines'), row=row, col=col)

        # Enhanced logging to debug the issue
        logging.info(f"Processing Mag*Voice Slope in add_output_to_subplot")
        logging.info(f"Available keys in plot_settings: {list(plot_settings.keys())}")

        # Try multiple possible key formats for Mag*Voice Slope limits
        if 'Mag*Voice Slope' in plot_settings and isinstance(plot_settings['Mag*Voice Slope'], list) and len(plot_settings['Mag*Voice Slope']) == 2:
            # Use the range passed as a list under 'Mag*Voice Slope' key
            min_val, max_val = plot_settings['Mag*Voice Slope']
            logging.info(f"Using Mag*Voice Slope limits from 'Mag*Voice Slope' key: {[min_val, max_val]}")
        elif 'mag_voice_slope' in plot_settings and isinstance(plot_settings['mag_voice_slope'], list) and len(plot_settings['mag_voice_slope']) == 2:
            # Use the range passed as a list under 'mag_voice_slope' key
            min_val, max_val = plot_settings['mag_voice_slope']
            logging.info(f"Using Mag*Voice Slope limits from 'mag_voice_slope' key: {[min_val, max_val]}")
        elif 'mag_voice_slope_cmap_min' in plot_settings and 'mag_voice_slope_cmap_max' in plot_settings:
            # Use individual min/max values
            min_val = plot_settings['mag_voice_slope_cmap_min']
            max_val = plot_settings['mag_voice_slope_cmap_max']
            logging.info(f"Using Mag*Voice Slope limits from individual min/max keys: {[min_val, max_val]}")
        else:
            # Calculate p5 and p95 from the current data if available
            try:
                p5 = np.percentile(mag_voice_slope, 5)
                p95 = np.percentile(mag_voice_slope, 95)
                # Use symmetric limits based on the larger absolute value
                abs_max = max(abs(p5), abs(p95))
                min_val = -abs_max
                max_val = abs_max
                logging.info(f"Using Mag*Voice Slope limits calculated from current data: {[min_val, max_val]}")
            except Exception as e:
                # Final fallback to reasonable defaults
                min_val = -10
                max_val = 10
                logging.info(f"Using default Mag*Voice Slope limits due to error: {e}")

        # Apply the limits to the plot
        fig.update_xaxes(title_text='Slope', row=row, col=col, range=[min_val, max_val])
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Spectral Slope":
        # Check if spec_slope exists in descriptors
        if 'spec_slope' in descriptors and isinstance(descriptors['spec_slope'], np.ndarray):
            spec_slope = descriptors['spec_slope']
            fig.add_trace(go.Scatter(x=spec_slope, y=time_vector, mode='lines'), row=row, col=col)

            # First check if the limits are passed as a list in 'Spectral Slope' key
            # This is the key set in app.py for individual plots
            if 'Spectral Slope' in plot_settings and isinstance(plot_settings['Spectral Slope'], list) and len(plot_settings['Spectral Slope']) == 2:
                # Use the range passed from app.py
                min_val, max_val = plot_settings['Spectral Slope']
            else:
                # Fallback to individual cmap_min/cmap_max values
                min_val = plot_settings.get('spectral_slope_cmap_min', plot_settings.get('spectral_slope_min', -1))
                max_val = plot_settings.get('spectral_slope_cmap_max', plot_settings.get('spectral_slope_max', 1))

            # Apply the limits to the plot
            fig.update_xaxes(title_text='Slope', row=row, col=col, range=[min_val, max_val])
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
        else:
            # If spec_slope is not available, display a message
            fig.add_annotation(
                text="Spectral Slope data not available",
                x=0.5, y=0.5,
                xref=f"x{col}", yref=f"y{col}",
                showarrow=False,
                font=dict(size=12, color="red")
            )
            # Add an empty trace to ensure the subplot is created
            fig.add_trace(go.Scatter(x=[None], y=[None], mode='lines', visible=False), row=row, col=col)
            fig.update_xaxes(title_text='Slope', row=row, col=col)
            fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Normalized Spectral Decrease" or output == "Spectral Decrease":
        # Get Spectral Decrease percentile cutoff value for normalization
        spec_decrease_pc = plot_settings.get('spec_decrease_pc')
        if spec_decrease_pc is None:
            # Fallback to old naming for backward compatibility
            spec_decrease_pc = plot_settings.get('spec_decrease_p95', 1.0)

        # Simple normalization: norm_spec_decrease = spec_decrease / spec_decrease_pc
        if spec_decrease_pc > 0:
            spec_decrease_normalized = descriptors['spec_decrease'] / spec_decrease_pc
            logging.info(f"Spectral Decrease normalized using cutoff value: {spec_decrease_pc}")
        else:
            spec_decrease_normalized = descriptors['spec_decrease']
            logging.warning(f"Invalid Spectral Decrease cutoff value ({spec_decrease_pc}), using raw values")

        fig.add_trace(go.Scatter(x=spec_decrease_normalized, y=time_vector, mode='lines'), row=row, col=col)
        x_range = None
        # For Option 1 (Well Marker Analysis), standardize x-axis range to [0, 2]
        if plot_settings.get('is_well_marker_mode'):
            x_range = [0, 2]
        fig.update_xaxes(title_text='Normalized Decrease', row=row, col=col, range=x_range)
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Normalized High Frequency Content" or output == "HFC":
        # Get HFC percentile cutoff value for normalization
        hfc_pc = plot_settings.get('hfc_pc')
        if hfc_pc is None:
            # Fallback to old naming for backward compatibility
            hfc_pc = plot_settings.get('hfc_p95', 1.0)

        # Simple normalization: norm_HFC = HFC / hfc_pc
        if hfc_pc > 0:
            hfc_normalized = descriptors['hfc'] / hfc_pc
            logging.info(f"HFC normalized using cutoff value: {hfc_pc}")
        else:
            hfc_normalized = descriptors['hfc']
            logging.warning(f"Invalid HFC cutoff value ({hfc_pc}), using raw values")

        fig.add_trace(go.Scatter(x=hfc_normalized, y=time_vector, mode='lines'), row=row, col=col)
        x_range = None
        # For Option 1 (Well Marker Analysis), standardize x-axis range to [0, 2]
        if plot_settings.get('is_well_marker_mode'):
            x_range = [0, 2]
        fig.update_xaxes(title_text='Normalized Energy', row=row, col=col, range=x_range)
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Spectral bandwidth" or output == "Spectral Bandwidth":
        spec_bandwidth = descriptors['spec_bandwidth']
        fig.add_trace(go.Scatter(x=spec_bandwidth, y=time_vector, mode='lines', line=dict(color='green')), row=row, col=col)
        # Use the cmap_min/cmap_max values from Step 2 if available
        min_val = plot_settings.get('spectral_bandwidth_cmap_min', plot_settings.get('spectral_bandwidth_min', 0))
        max_val = plot_settings.get('spectral_bandwidth_cmap_max', plot_settings.get('spectral_bandwidth_max', 50))
        fig.update_xaxes(title_text='Bandwidth (Hz)', row=row, col=col, range=[min_val, max_val])
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "Spectral roll-off" or output == "Spectral Rolloff":
        spec_rolloff = descriptors['spec_rolloff']
        fig.add_trace(go.Scatter(x=spec_rolloff, y=time_vector, mode='lines', line=dict(color='orange')), row=row, col=col)
        # Use the cmap_min/cmap_max values from Step 2 if available
        min_val = plot_settings.get('spectral_rolloff_cmap_min', plot_settings.get('spectral_rolloff_min', 0))
        max_val = plot_settings.get('spectral_rolloff_cmap_max', plot_settings.get('spectral_rolloff_max', 50))
        fig.update_xaxes(title_text='Rolloff (Hz)', row=row, col=col, range=[min_val, max_val])
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])
    elif output == "WOSS (Weighted Optimum Spectral Shape)" or output == "WOSS":
        # Use the calculate_woss function from processing module for consistency
        from utils.processing import calculate_woss

        # Create descriptor dictionary for WOSS calculation
        descriptor_dict = {
            'hfc': descriptors['hfc'],
            'norm_fdom': descriptors['norm_fdom'],
            'mag_voice_slope': descriptors['mag_voice_slope']
        }

        # Get HFC percentile cutoff value for WOSS calculation
        hfc_pc = plot_settings.get('hfc_pc')
        if hfc_pc is None:
            # Fallback to old naming for backward compatibility
            hfc_pc = plot_settings.get('hfc_p95', 1.0)

        # Create WOSS parameters
        woss_params = {
            'hfc_p95': hfc_pc,  # WOSS function still expects 'hfc_p95' parameter name
            'epsilon': plot_settings.get('epsilon', 1e-10),
            'fdom_exponent': plot_settings.get('fdom_exponent', 2.0)
        }

        # Calculate WOSS using the standardized function
        woss = calculate_woss(descriptor_dict, woss_params)

        fig.add_trace(go.Scatter(x=woss, y=time_vector, mode='lines', line=dict(color='purple')), row=row, col=col)
        # For Option 1 (Well Marker Analysis): Use WOSS min/max values from plot_settings
        min_val = plot_settings.get('woss_cmap_min', plot_settings.get('woss_min', -3))
        max_val = plot_settings.get('woss_cmap_max', plot_settings.get('woss_max', 3))
        fig.update_xaxes(title_text='WOSS Value', row=row, col=col, range=[min_val, max_val])
        fig.update_yaxes(title_text='Time (s)', row=row, col=col, range=plot_settings['Time (Y-axis)'][::-1])

    # Add marker if applicable
    if trace_data_item.get('marker_value') is not None:
        fig.add_hline(y=trace_data_item['marker_value'], line=dict(color='red', dash='dash'), row=row, col=col)
# --- End Ported Function ---


def plot_basemap_with_wells(header_loader, well_df=None, segy_path=None, detail_level='medium'):
    """
    Create a basemap visualization of the seismic survey with well locations.
    Optimized for memory usage with configurable detail levels.

    This function generates a Plotly figure showing the seismic survey geometry
    with inline/crossline grid lines and well marker positions. It also displays
    survey statistics such as trace count and inline/crossline ranges.

    Args:
        header_loader: SegyHeaderLoader object containing survey geometry
        well_df: DataFrame containing well marker information (optional)
        segy_path: Path to SEG-Y file for additional metadata (optional)
        detail_level: Level of detail for the basemap ('low', 'medium', 'high')

    Returns:
        plotly.graph_objects.Figure: Basemap figure
    """
    # Get inline/crossline ranges
    il_xl_range = header_loader.get_inline_crossline_range()

    # Create figure with appropriate size
    fig = go.Figure()

    # Determine detail level parameters
    if detail_level == 'low':
        max_points = 1000  # Show fewer points for better performance
        show_grid = False
    elif detail_level == 'medium':
        max_points = 5000
        show_grid = True
    else:  # high
        max_points = 20000
        show_grid = True

    # Subsample points if needed to improve performance
    num_points = len(header_loader.x_coords)
    if num_points > max_points:
        # Calculate stride to get approximately max_points
        stride = max(1, num_points // max_points)
        x_coords = header_loader.x_coords[::stride]
        y_coords = header_loader.y_coords[::stride]
        inlines = header_loader.inlines[::stride]
        crosslines = header_loader.crosslines[::stride]
        print(f"Subsampling points for display: {num_points} → {len(x_coords)}")
    else:
        x_coords = header_loader.x_coords
        y_coords = header_loader.y_coords
        inlines = header_loader.inlines
        crosslines = header_loader.crosslines

    # Add seismic survey points
    fig.add_trace(go.Scatter(
        x=x_coords,
        y=y_coords,
        mode='markers',
        marker=dict(
            size=3,
            color='blue',
            opacity=0.5
        ),
        name='Seismic Traces'
    ))

    # Add well locations if provided
    if well_df is not None and not well_df.empty:
        # Group by well to get unique well locations
        well_groups = well_df.groupby('Well')
        well_x = []
        well_y = []
        well_names = []

        for well_name, group in well_groups:
            # Use the first occurrence of each well
            well_x.append(group['X'].iloc[0])
            well_y.append(group['Y'].iloc[0])
            well_names.append(well_name)

        fig.add_trace(go.Scatter(
            x=well_x,
            y=well_y,
            mode='markers+text',
            marker=dict(
                size=8,
                color='red',
                symbol='diamond'
            ),
            text=well_names,
            textposition="top center",
            name='Wells'
        ))

    # Add grid lines for inlines/crosslines if requested
    if show_grid and num_points > 0:
        # Determine grid spacing based on range
        il_range = il_xl_range['inline_max'] - il_xl_range['inline_min']
        xl_range = il_xl_range['xline_max'] - il_xl_range['xline_min']

        # Calculate appropriate step sizes
        il_step = max(1, il_range // 10)
        xl_step = max(1, xl_range // 10)

        # Create grid lines for major inlines
        for il in range(il_xl_range['inline_min'], il_xl_range['inline_max'] + 1, il_step):
            # Find points with this inline
            mask = (inlines == il)
            if np.any(mask):
                # Sort by crossline for proper line drawing
                idx = np.argsort(crosslines[mask])
                fig.add_trace(go.Scatter(
                    x=x_coords[mask][idx],
                    y=y_coords[mask][idx],
                    mode='lines',
                    line=dict(color='rgba(0,0,255,0.2)', width=1),
                    name=f'IL {il}',
                    showlegend=False
                ))

        # Create grid lines for major crosslines
        for xl in range(il_xl_range['xline_min'], il_xl_range['xline_max'] + 1, xl_step):
            # Find points with this crossline
            mask = (crosslines == xl)
            if np.any(mask):
                # Sort by inline for proper line drawing
                idx = np.argsort(inlines[mask])
                fig.add_trace(go.Scatter(
                    x=x_coords[mask][idx],
                    y=y_coords[mask][idx],
                    mode='lines',
                    line=dict(color='rgba(0,255,0,0.2)', width=1),
                    name=f'XL {xl}',
                    showlegend=False
                ))

    # Add survey statistics as annotations
    stats_text = [
        f"Inlines: {il_xl_range['inline_min']} - {il_xl_range['inline_max']}",
        f"Crosslines: {il_xl_range['xline_min']} - {il_xl_range['xline_max']}",
        f"Total Traces: {len(header_loader.unique_indices)}"
    ]

    # Add sampling rate if SEG-Y path is provided
    if segy_path:
        try:
            with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
                dt_ms = segyfile.bin[segyio.BinField.Interval] / 1000  # Convert to milliseconds
                num_samples = len(segyfile.samples)
                stats_text.append(f"Sampling: {dt_ms} ms")
                stats_text.append(f"Samples/trace: {num_samples}")
        except Exception as e:
            print(f"Could not read additional SEG-Y info: {e}")

    # Add stats as annotation
    fig.add_annotation(
        x=0.02,
        y=0.98,
        xref="paper",
        yref="paper",
        text="<br>".join(stats_text),
        showarrow=False,
        font=dict(size=10),
        align="left",
        bgcolor="rgba(255,255,255,0.8)",
        bordercolor="black",
        borderwidth=1,
        borderpad=4
    )

    # Update layout
    fig.update_layout(
        title="Seismic Survey Basemap",
        xaxis_title="X Coordinate",
        yaxis_title="Y Coordinate",
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="right",
            x=0.99
        ),
        autosize=True,
        hovermode="closest",
        margin=dict(l=20, r=20, t=40, b=20)
    )

    # Set aspect ratio to equal for proper spatial representation
    fig.update_yaxes(
        scaleanchor="x",
        scaleratio=1,
    )

    return fig

def create_polyline_selection_ui(root=None, header_loader=None, well_df_basemap=None, segy_path=None):
    """
    Create a GUI for selecting traces along a polyline.

    This function creates a Tkinter window with a Plotly basemap where users can
    define a polyline by clicking points. The function then finds traces near
    the polyline and returns their indices.

    Args:
        root: Tkinter root window (optional)
        header_loader: SegyHeaderLoader object containing trace coordinates
        well_df_basemap: DataFrame containing well marker information (optional)
        segy_path: Path to SEG-Y file for additional metadata (optional)

    Returns:
        list: Indices of traces near the defined polyline
    """
    # Create a new Tkinter window if root is not provided
    if root is None:
        root = tk.Tk()
        root.title("Polyline Trace Selection")
        root.geometry("1200x800")

    # Create a frame for the UI
    frame = tk.Frame(root)
    frame.pack(fill=tk.BOTH, expand=True)

    # Create a frame for the basemap
    basemap_frame = tk.Frame(frame)
    basemap_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

    # Create a frame for the controls
    control_frame = tk.Frame(frame)
    control_frame.pack(side=tk.RIGHT, fill=tk.Y)

    # Create a label for instructions
    tk.Label(control_frame, text="Instructions:", font=("Arial", 12, "bold")).pack(pady=5)
    tk.Label(control_frame, text="1. Click on the map to add vertices").pack(anchor=tk.W)
    tk.Label(control_frame, text="2. Define the polyline path").pack(anchor=tk.W)
    tk.Label(control_frame, text="3. Set the maximum distance").pack(anchor=tk.W)
    tk.Label(control_frame, text="4. Click 'Find Traces' to select").pack(anchor=tk.W)

    # Create a separator
    tk.Frame(control_frame, height=2, bd=1, relief=tk.SUNKEN).pack(fill=tk.X, pady=10)

    # Create a text widget to display vertices
    tk.Label(control_frame, text="Polyline Vertices:", font=("Arial", 10, "bold")).pack(pady=5)
    vertices_list = tk.Text(control_frame, width=30, height=15)
    vertices_list.pack(fill=tk.BOTH, expand=True)

    # Create a frame for the distance input
    distance_frame = tk.Frame(control_frame)
    distance_frame.pack(pady=10)

    tk.Label(distance_frame, text="Max Distance:").pack(side=tk.LEFT)
    distance_entry = tk.Entry(distance_frame, width=10)
    distance_entry.insert(0, "100")  # Default value
    distance_entry.pack(side=tk.LEFT, padx=5)

    # Create a variable to store the polyline vertices
    polyline_vertices = []

    # Create a temporary file to save the Plotly figure
    with tempfile.NamedTemporaryFile(delete=False, suffix='.html') as f:
        basemap_file = f.name

    # Function to update the basemap with the current polyline
    def update_basemap():
        # Create a basemap with the current polyline
        fig = plot_basemap_with_wells(header_loader, well_df_basemap, segy_path, detail_level='medium')

        # Add the polyline if there are at least 2 vertices
        if len(polyline_vertices) >= 2:
            x_vals = [v[0] for v in polyline_vertices]
            y_vals = [v[1] for v in polyline_vertices]

            fig.add_trace(go.Scatter(
                x=x_vals,
                y=y_vals,
                mode='lines+markers',
                line=dict(color='red', width=3),
                marker=dict(size=8, color='red'),
                name='Polyline'
            ))

        # Save the figure to the temporary file
        fig.write_html(basemap_file)

        # Update the browser widget
        browser.load_url(f"file://{basemap_file}")

    # Function to handle clicks on the basemap
    def on_basemap_click(event):
        # Extract coordinates from the click event
        # This is a placeholder - actual implementation would depend on how you integrate with Plotly
        x, y = event.x, event.y

        # Add the vertex to the polyline
        polyline_vertices.append((x, y))

        # Update the vertices list
        vertices_list.delete(1.0, tk.END)
        for i, (x, y) in enumerate(polyline_vertices):
            vertices_list.insert(tk.END, f"{i+1}. X: {x:.2f}, Y: {y:.2f}\n")

        # Update the basemap
        update_basemap()

    # Create a button to clear the polyline
    def clear_polyline():
        polyline_vertices.clear()
        vertices_list.delete(1.0, tk.END)
        update_basemap()

    tk.Button(control_frame, text="Clear Polyline", command=clear_polyline).pack(pady=5)

    # Create a button to find traces near the polyline
    def find_traces():
        if len(polyline_vertices) < 2:
            messagebox.showerror("Error", "Please define at least 2 vertices for the polyline.")
            return

        try:
            max_distance = float(distance_entry.get())
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number for maximum distance.")
            return

        # Find traces near the polyline
        selected_indices = find_traces_near_polyline(header_loader, polyline_vertices, max_distance)

        # Show a message with the number of traces found
        messagebox.showinfo("Traces Found", f"Found {len(selected_indices)} traces near the polyline.")

        # Close the window and return the selected indices
        root.selected_indices = selected_indices
        root.destroy()

    tk.Button(control_frame, text="Find Traces", command=find_traces).pack(pady=10)

    # Create a button to cancel
    def cancel():
        root.selected_indices = []
        root.destroy()

    tk.Button(control_frame, text="Cancel", command=cancel).pack(pady=5)

    # Create a browser widget to display the basemap
    # This is a placeholder - you would need to use a proper browser widget
    browser_frame = tk.Frame(basemap_frame)
    browser_frame.pack(fill=tk.BOTH, expand=True)

    # Placeholder for a browser widget
    # In a real implementation, you would use a proper browser widget like tkinterweb
    browser = tk.Label(browser_frame, text="Basemap would be displayed here")
    browser.pack(fill=tk.BOTH, expand=True)

    # Update the basemap initially
    update_basemap()

    # Run the Tkinter event loop
    root.mainloop()

    # Return the selected indices
    return getattr(root, 'selected_indices', [])

def plot_trace_with_descriptors(trace_data, time_vector, descriptors, plot_settings, title=None):
    """
    Create a comprehensive visualization of a seismic trace with its spectral descriptors.

    This function generates a multi-panel Plotly figure showing the original seismic trace,
    its spectral decomposition, and various spectral attributes. The layout and content
    are configurable through the plot_settings dictionary.

    Args:
        trace_data: Numpy array containing the seismic trace amplitude data
        time_vector: Numpy array containing the time values for the trace
        descriptors: Dictionary containing spectral descriptors calculated for the trace
        plot_settings: Dictionary containing display settings and parameters
        title: Optional title for the plot

    Returns:
        plotly.graph_objects.Figure: Figure containing the trace visualization
    """
    # Determine which plots to show based on plot_settings
    show_trace = plot_settings.get('show_trace', True)
    show_spectrogram = plot_settings.get('show_spectrogram', True)
    show_descriptors = plot_settings.get('show_descriptors', True)

    # Count the number of subplots needed
    num_plots = sum([show_trace, show_spectrogram, show_descriptors])
    if num_plots == 0:
        num_plots = 1  # At least show the trace
        show_trace = True

    # Determine the number of descriptor plots if enabled
    descriptor_keys = []
    if show_descriptors:
        # Check which descriptors to display
        for key in ['hfc', 'norm_fdom', 'mag_voice_slope', 'WOSS']:
            if plot_settings.get(f'show_{key}', True) and key in descriptors:
                descriptor_keys.append(key)

    # Calculate total number of rows
    total_rows = 0
    if show_trace:
        total_rows += 1
    if show_spectrogram:
        total_rows += 1
    if descriptor_keys:
        total_rows += len(descriptor_keys)

    # Create subplot grid
    fig = make_subplots(
        rows=total_rows,
        cols=1,
        shared_xaxes=True,
        vertical_spacing=0.02,
        subplot_titles=[]
    )

    # Current row counter
    current_row = 1

    # Add seismic trace if enabled
    if show_trace:
        fig.add_trace(
            go.Scatter(
                x=time_vector,
                y=trace_data,
                mode='lines',
                name='Seismic Trace',
                line=dict(color='black', width=1)
            ),
            row=current_row,
            col=1
        )
        current_row += 1

    # Add spectrogram if enabled
    if show_spectrogram and 'spectrogram' in descriptors:
        # Get spectrogram data
        spec_data = descriptors['spectrogram']
        freq_vector = descriptors.get('freq_vector', np.arange(spec_data.shape[1]))

        # Create heatmap for spectrogram
        fig.add_trace(
            go.Heatmap(
                z=spec_data,
                x=time_vector,
                y=freq_vector,
                colorscale=plot_settings.get('spec_colormap', 'Viridis'),
                showscale=False,
                name='Spectrogram'
            ),
            row=current_row,
            col=1
        )
        current_row += 1

    # Add descriptor plots if enabled
    for key in descriptor_keys:
        if key in descriptors:
            # Get descriptor data
            desc_data = descriptors[key]

            # Skip if data is not a 1D array matching time_vector
            if not isinstance(desc_data, np.ndarray) or desc_data.size != len(time_vector):
                continue

            # Get display name and color settings
            display_name = {
                'hfc': 'High Frequency Content',
                'norm_fdom': 'Normalized Dominant Frequency',
                'mag_voice_slope': 'Magnitude Voice Slope',
                'WOSS': 'Weighted Overlap Spectral Slope'
            }.get(key, key)

            # Get color based on settings or use defaults
            color = {
                'hfc': 'red',
                'norm_fdom': 'blue',
                'mag_voice_slope': 'green',
                'WOSS': 'purple'
            }.get(key, 'black')

            # Add the descriptor trace
            fig.add_trace(
                go.Scatter(
                    x=time_vector,
                    y=desc_data,
                    mode='lines',
                    name=display_name,
                    line=dict(color=color, width=1.5)
                ),
                row=current_row,
                col=1
            )
            current_row += 1

    # Update layout
    fig.update_layout(
        title=title or "Seismic Trace Analysis",
        height=200 * total_rows,  # Adjust height based on number of plots
        width=800,
        margin=dict(l=50, r=20, t=50, b=50),
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # Update y-axis titles
    if show_trace:
        fig.update_yaxes(title_text="Amplitude", row=1, col=1)

    if show_spectrogram:
        spec_row = 2 if show_trace else 1
        fig.update_yaxes(title_text="Frequency (Hz)", row=spec_row, col=1)

    # Update x-axis title only on the bottom plot
    fig.update_xaxes(title_text="Time (s)", row=total_rows, col=1)

    # Return the figure instead of showing it
    return fig

def plot_spectral_descriptors(trace_data, time_vector, descriptors, plot_settings, trace_idx=None, well_marker_name=None):
    """
    Plot spectral descriptors for a single trace.

    Args:
        trace_data: The seismic trace data
        time_vector: Time vector for the trace
        descriptors: Dictionary containing spectral descriptors
        plot_settings: Dictionary containing plot settings
        trace_idx: Trace index (optional)
        well_marker_name: Well marker name (optional)

    Returns:
        plotly.graph_objects.Figure: Figure containing the spectral descriptor visualization
    """
    # Import pandas for NaN checking
    import pandas as pd
    
    # Validate and clean parameters to prevent "nan" titles
    def is_valid_value(value):
        """Check if a value is valid (not None, NaN, or empty string)"""
        if value is None:
            return False
        if isinstance(value, (int, float)) and (pd.isna(value) or not pd.notna(value)):
            return False
        if isinstance(value, str) and value.strip() in ["", "nan", "None", "Unknown", "Unknown - Unknown"]:
            return False
        return True
    
    # Clean trace_idx
    if not is_valid_value(trace_idx):
        trace_idx = 0  # Default to 0 instead of None
        logging.warning("Invalid trace_idx detected, using default value 0")
    
    # Clean well_marker_name
    if not is_valid_value(well_marker_name):
        well_marker_name = None  # Set to None so fallback logic applies
        logging.warning("Invalid well_marker_name detected, will use fallback")
    
    # Add detailed logging for debugging
    logging.info("plot_spectral_descriptors function called")
    logging.info(f"trace_data shape: {trace_data.shape if hasattr(trace_data, 'shape') else 'not a numpy array'}")
    logging.info(f"time_vector length: {len(time_vector)}")
    logging.info(f"descriptor keys: {list(descriptors.keys())}")
    logging.info(f"plot_settings keys: {list(plot_settings.keys())}")
    logging.info(f"cleaned trace_idx: {trace_idx}")
    logging.info(f"cleaned well_marker_name: {well_marker_name}")

    # Get selected outputs from plot_settings
    selected_outputs = plot_settings.get('selected_outputs', [
        "Input Signal", "Magnitude Spectrogram", "Normalized Dominant Frequency",
        "Mag*Voice Slope", "HFC", "WOSS"
    ])
    logging.info(f"selected_outputs: {selected_outputs}")

    # Create subplot titles
    subplot_titles = [output for output in selected_outputs]
    logging.info(f"subplot_titles: {subplot_titles}")

    # Create subplots
    try:
        logging.info(f"Creating subplots with {len(selected_outputs)} columns")
        fig = make_subplots(rows=1, cols=len(selected_outputs), subplot_titles=subplot_titles)
        logging.info("Successfully created subplots")
    except Exception as e:
        logging.error(f"Error creating subplots: {e}", exc_info=True)
        # Create a simple figure with an error message
        fig = go.Figure()
        fig.add_annotation(
            text=f"Error creating plots:<br>{str(e)}",
            x=0.5, y=0.5,
            xref="paper", yref="paper",
            showarrow=False,
            font=dict(size=14, color="red")
        )
        # Return the error figure early
        return fig

    # Get normalization values using robust helper functions
    from utils.processing import get_robust_hfc_normalization_value, get_robust_spec_decrease_normalization_value

    # Get robust HFC normalization value
    hfc_p95, hfc_source = get_robust_hfc_normalization_value(plot_settings=plot_settings)

    # Get robust Spectral Decrease normalization value
    spec_decrease_p95, spec_decrease_source = get_robust_spec_decrease_normalization_value(plot_settings=plot_settings)

    logging.info(f"hfc_p95: {hfc_p95} (source: {hfc_source}), spec_decrease_p95: {spec_decrease_p95} (source: {spec_decrease_source})")

    # Set frequency range in plot_settings if not already set
    if 'Frequency' not in plot_settings:
        freq_min = plot_settings.get('freq_min', 0)
        freq_max = plot_settings.get('freq_max', 100)
        plot_settings['Frequency'] = [freq_min, freq_max]
        logging.info(f"Added Frequency to plot_settings: {plot_settings['Frequency']}")

    # Add each output to its subplot
    for col_idx, output in enumerate(selected_outputs, 1):
        try:
            logging.info(f"Processing output {output} for column {col_idx}")

            # Check if required keys exist in descriptors for this output
            if output == "Magnitude Spectrogram" and ('mag' not in descriptors or 'freqst' not in descriptors):
                logging.warning(f"Missing required keys for {output}: 'mag' or 'freqst'")
            elif output == "Magnitude * Voice" and ('mag_voice' not in descriptors or 'freqst' not in descriptors):
                logging.warning(f"Missing required keys for {output}: 'mag_voice' or 'freqst'")
            elif output == "Normalized dominant frequencies" and ('peak_freq' not in descriptors or 'spec_centroid' not in descriptors or 'fdom' not in descriptors):
                logging.warning(f"Missing required keys for {output}: 'peak_freq', 'spec_centroid', or 'fdom'")
            elif output == "Mag*Voice Slope" and 'mag_voice_slope' not in descriptors:
                logging.warning(f"Missing required key for {output}: 'mag_voice_slope'")
            elif output == "HFC" and 'hfc' not in descriptors:
                logging.warning(f"Missing required key for {output}: 'hfc'")
            elif output == "WOSS" and 'WOSS' not in descriptors:
                logging.warning(f"Missing required key for {output}: 'WOSS'")

            # Use the add_output_to_subplot function to add each output
            logging.info(f"Calling add_output_to_subplot for {output}")
            add_output_to_subplot(
                fig=fig,
                output=output,
                trace_data_item={'trace_sample': trace_data},
                descriptors=descriptors,
                time_vector=time_vector,
                row=1,
                col=col_idx,
                plot_settings=plot_settings,
                hfc_p95=hfc_p95,  # Still needed for WOSS calculation
                spec_decrease_p95=spec_decrease_p95  # Kept for backward compatibility
            )
            logging.info(f"Successfully added {output} to subplot")

        except Exception as e:
            logging.error(f"Error adding output {output} to subplot: {e}", exc_info=True)
            # Add an error message to the subplot instead of failing completely
            fig.add_annotation(
                text=f"Error plotting {output}:<br>{str(e)}",
                x=0.5, y=0.5,
                xref=f"x{col_idx}", yref=f"y{col_idx}",
                showarrow=False,
                font=dict(size=12, color="red")
            )

    try:
        # Update y-axes for all subplots
        for i in range(1, len(selected_outputs) + 1):
            try:
                # Apply time range from plot_settings['Time (Y-axis)']
                time_range = plot_settings.get('Time (Y-axis)', [0, len(time_vector) * plot_settings.get('dt', 0.004)])[::-1]
                fig.update_yaxes(title_text='Time (s)' if i == 1 else '', row=1, col=i, range=time_range)
                logging.info(f"Updated y-axis for subplot {i}")
            except Exception as axis_e:
                logging.error(f"Error updating y-axis for subplot {i}: {axis_e}", exc_info=True)

        # Count the number of heatmap outputs that need colorbars
        heatmap_outputs = sum(1 for o in selected_outputs if o in ["Magnitude Spectrogram", "Magnitude * Voice"])
        logging.info(f"Number of heatmap outputs: {heatmap_outputs}")

        # Set figure title and layout - prioritize well marker name for geological context
        # Additional validation to prevent "nan" in titles
        title_text = "Spectral Descriptors"  # Default fallback
        
        if well_marker_name and is_valid_value(well_marker_name):
            if isinstance(well_marker_name, dict):
                # Extract name from dictionary, with a fallback
                dict_name = well_marker_name.get('name')
                if is_valid_value(dict_name):
                    title_text = str(dict_name)
                else:
                    title_text = f"Trace {trace_idx}"
            else:
                # Validate the string name before using it
                str_name = str(well_marker_name).strip()
                if is_valid_value(str_name):
                    title_text = str_name
                else:
                    title_text = f"Trace {trace_idx}"
        elif is_valid_value(trace_idx):
            # Fallback to trace index if no well marker name, but validate trace_idx
            title_text = f"Trace {trace_idx}"
            logging.warning(f"No valid well marker name provided for trace {trace_idx}, using generic title")
        
        logging.info(f"Figure title: {title_text}")

        # Update layout
        fig.update_layout(
            title=title_text,
            height=600,
            width=max(800, 300 * len(subplot_titles) + 80 * heatmap_outputs),  # Width based on number of columns
            showlegend=False,
            margin=dict(r=70 + 80 * heatmap_outputs)  # Right margin for colorbars
        )
        logging.info("Successfully updated figure layout")

    except Exception as layout_e:
        logging.error(f"Error updating figure layout: {layout_e}", exc_info=True)
        # Add a general error message to the figure
        try:
            fig.add_annotation(
                text=f"Error finalizing plot:<br>{str(layout_e)}",
                x=0.5, y=0.5,
                xref="paper", yref="paper",
                showarrow=False,
                font=dict(size=14, color="red")
            )
        except:
            # If we can't even add an annotation, just log it
            logging.error("Could not add error annotation to figure")

    logging.info("Returning figure from plot_spectral_descriptors")
    return fig

def plot_multi_trace_comparison(traces_data, time_vector, trace_labels=None, title=None):
    """
    Create a plot comparing multiple seismic traces.

    This function generates a Plotly figure showing multiple seismic traces overlaid
    or stacked for comparison. Useful for comparing traces from different locations
    or processing methods.

    Args:
        traces_data: List of numpy arrays containing seismic trace amplitude data
        time_vector: Numpy array containing the time values for the traces
        trace_labels: Optional list of labels for each trace
        title: Optional title for the plot

    Returns:
        plotly.graph_objects.Figure: Figure containing the trace comparison
    """
    # Create figure
    fig = go.Figure()

    # Ensure trace_labels is defined
    if trace_labels is None:
        trace_labels = [f"Trace {i+1}" for i in range(len(traces_data))]
    elif len(trace_labels) < len(traces_data):
        # Extend labels if needed
        trace_labels.extend([f"Trace {i+1}" for i in range(len(trace_labels), len(traces_data))])

    # Add each trace to the figure
    colors = ['blue', 'red', 'green', 'purple', 'orange', 'brown', 'pink', 'gray', 'olive', 'cyan']

    for i, trace_data in enumerate(traces_data):
        color = colors[i % len(colors)]

        fig.add_trace(
            go.Scatter(
                x=time_vector,
                y=trace_data,
                mode='lines',
                name=trace_labels[i],
                line=dict(color=color, width=1.5)
            )
        )

    # Update layout
    fig.update_layout(
        title=title or "Seismic Trace Comparison",
        xaxis_title="Time (s)",
        yaxis_title="Amplitude",
        height=600,
        width=800,
        margin=dict(l=50, r=20, t=50, b=50),
        showlegend=True,
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="right",
            x=0.99
        ),
        # Add click event handling for trace points
        # Note: This part would rely on Plotly's FigureWidget in an interactive environment
        hovermode="closest"
    )

    # Return the figure instead of showing it
    return fig

def plot_descriptor_section(descriptors, time_vector, descriptor_key, plot_settings, title=None):
    """
    Create a visualization of a spectral descriptor across multiple traces.

    This function generates a Plotly figure showing a spectral attribute (e.g., HFC, WOSS)
    as a 2D section across multiple traces. Useful for visualizing spatial patterns
    in spectral attributes.

    Args:
        descriptors: List of dictionaries containing spectral descriptors for each trace
        time_vector: Numpy array containing the time values for the traces
        descriptor_key: Key identifying which descriptor to plot (e.g., 'hfc', 'WOSS')
        plot_settings: Dictionary containing display settings and parameters
        title: Optional title for the plot

    Returns:
        plotly.graph_objects.Figure: Figure containing the descriptor section
    """
    # Extract the descriptor data from each trace's descriptors
    data_list = []
    for desc in descriptors:
        if descriptor_key in desc and isinstance(desc[descriptor_key], np.ndarray):
            # Ensure the descriptor has the same length as time_vector
            if len(desc[descriptor_key]) == len(time_vector):
                data_list.append(desc[descriptor_key])
            else:
                # Resample if lengths don't match
                data_list.append(np.zeros_like(time_vector))
        else:
            # Use zeros if descriptor is missing
            data_list.append(np.zeros_like(time_vector))

    # Stack the data into a 2D array
    if data_list:
        section_data = np.vstack(data_list)
    else:
        # Create empty section if no data
        section_data = np.zeros((1, len(time_vector)))

    # Get colormap settings
    colormap = plot_settings.get(f'{descriptor_key}_cmap', 'Viridis')
    cmin = plot_settings.get(f'{descriptor_key}_cmap_min', np.min(section_data))
    cmax = plot_settings.get(f'{descriptor_key}_cmap_max', np.max(section_data))

    # Create trace indices for x-axis
    trace_indices = np.arange(section_data.shape[0])

    # Create figure
    fig = go.Figure()

    # Add heatmap for the section
    fig.add_trace(
        go.Heatmap(
            z=section_data,
            x=trace_indices,
            y=time_vector,
            colorscale=colormap,
            zmin=cmin,
            zmax=cmax,
            colorbar=dict(
                title=descriptor_key.upper(),
                thickness=20,
                len=0.9,
                y=0.5,
                yanchor="middle"
            )
        )
    )

    # Update layout
    fig.update_layout(
        title=title or f"{descriptor_key.upper()} Section",
        xaxis_title="Trace Index",
        yaxis_title="Time (s)",
        height=600,
        width=800,
        margin=dict(l=50, r=20, t=50, b=50),
        yaxis=dict(autorange="reversed")  # Reverse y-axis to match seismic convention
    )

    # Return the figure
    return fig

def plot_woss_comparison(descriptors, time_vector, plot_settings, title=None):
    """
    Create a visualization comparing WOSS with its component attributes.

    This function generates a Plotly figure showing WOSS alongside the attributes
    used in its calculation (HFC, normalized dominant frequency, and magnitude voice slope).
    Useful for understanding how WOSS relates to its components.

    Args:
        descriptors: List of dictionaries containing spectral descriptors
        time_vector: Numpy array containing the time values
        plot_settings: Dictionary containing display settings and parameters
        title: Optional title for the plot

    Returns:
        plotly.graph_objects.Figure: Figure containing the WOSS comparison
    """
    # Create subplot grid with 4 rows (WOSS + 3 components)
    fig = make_subplots(
        rows=4,
        cols=1,
        shared_xaxes=True,
        vertical_spacing=0.02,
        subplot_titles=["WOSS", "High Frequency Content", "Normalized Dominant Frequency", "Magnitude Voice Slope"]
    )

    # Initialize list to store WOSS data
    woss_list = []

    # Process each trace's descriptors
    for i, descriptor in enumerate(descriptors):
        # Calculate WOSS if not already present
        if 'WOSS' not in descriptor and all(k in descriptor for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
            descriptor['WOSS'] = calculate_woss(descriptor, plot_settings)

        # Add WOSS trace
        if 'WOSS' in descriptor and isinstance(descriptor['WOSS'], np.ndarray):
            woss_data = descriptor['WOSS']
            if len(woss_data) == len(time_vector):
                woss_list.append(descriptor['WOSS'])
            else:
                # Handle length mismatch
                try:
                    # Try to resample
                    woss = np.interp(
                        np.linspace(0, 1, len(time_vector)),
                        np.linspace(0, 1, len(woss_data)),
                        woss_data
                    )
                    woss_list.append(woss)
                except:
                    woss_list.append(np.zeros_like(time_vector))
        else:
            woss_list.append(np.zeros_like(time_vector))

    # Add each component to the plot
    components = ['WOSS', 'hfc', 'norm_fdom', 'mag_voice_slope']
    colors = ['purple', 'red', 'blue', 'green']

    for row, (key, color) in enumerate(zip(components, colors), 1):
        # Initialize list to store component data
        data_list = []

        # Extract data for this component
        for i, descriptor in enumerate(descriptors):
            if key in descriptor and isinstance(descriptor[key], np.ndarray):
                # Ensure data has the same length as time_vector
                if len(descriptor[key]) == len(time_vector):
                    data_list.append(descriptor[key])
                else:
                    # Try to resample or use zeros
                    try:
                        data = np.interp(
                            np.linspace(0, 1, len(time_vector)),
                            np.linspace(0, 1, len(descriptor[key])),
                            descriptor[key]
                        )
                        data_list.append(data)
                    except:
                        data_list.append(np.zeros_like(time_vector))
            else:
                data_list.append(np.zeros_like(time_vector))

        # Stack data for this component
        if data_list:
            component_data = np.vstack(data_list)

            # Calculate mean across traces
            mean_data = np.mean(component_data, axis=0)

            # Add mean trace to the plot
            fig.add_trace(
                go.Scatter(
                    x=time_vector,
                    y=mean_data,
                    mode='lines',
                    name=f"Mean {key}",
                    line=dict(color=color, width=2)
                ),
                row=row,
                col=1
            )

            # Add individual traces with lower opacity
            for i, data in enumerate(component_data):
                fig.add_trace(
                    go.Scatter(
                        x=time_vector,
                        y=data,
                        mode='lines',
                        name=f"Trace {i+1} {key}",
                        line=dict(color=color, width=0.5, opacity=0.3),
                        showlegend=False
                    ),
                    row=row,
                    col=1
                )

    # Update layout
    fig.update_layout(
        title=title or "WOSS and Component Attributes Comparison",
        height=800,
        width=800,
        margin=dict(l=50, r=20, t=50, b=50),
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # Update x-axis title only on the bottom plot
    fig.update_xaxes(title_text="Time (s)", row=4, col=1)

    # Return the figure
    return fig

def plot_descriptor_histogram(descriptors, descriptor_key, plot_settings, title=None):
    """
    Create a histogram visualization of a spectral descriptor's distribution.

    This function generates a Plotly figure showing the distribution of values
    for a specific spectral attribute across multiple traces. Useful for understanding
    the statistical properties of the attribute.

    Args:
        descriptors: List of dictionaries containing spectral descriptors
        descriptor_key: Key identifying which descriptor to plot (e.g., 'hfc', 'WOSS')
        plot_settings: Dictionary containing display settings and parameters
        title: Optional title for the plot

    Returns:
        plotly.graph_objects.Figure: Figure containing the descriptor histogram
    """
    # Extract the descriptor data from each trace's descriptors
    all_values = []

    for desc in descriptors:
        if descriptor_key in desc and isinstance(desc[descriptor_key], np.ndarray):
            # Flatten and add all values
            all_values.extend(desc[descriptor_key].flatten())

    # Create figure
    fig = go.Figure()

    if all_values:
        # Add histogram
        fig.add_trace(
            go.Histogram(
                x=all_values,
                nbinsx=50,
                marker_color=plot_settings.get(f'{descriptor_key}_color', 'blue'),
                opacity=0.7
            )
        )

        # Calculate statistics
        mean_val = np.mean(all_values)
        median_val = np.median(all_values)
        std_val = np.std(all_values)
        min_val = np.min(all_values)
        max_val = np.max(all_values)

        # Add vertical lines for statistics
        fig.add_vline(x=mean_val, line_dash="solid", line_color="red",
                     annotation_text=f"Mean: {mean_val:.3f}", annotation_position="top right")
        fig.add_vline(x=median_val, line_dash="dash", line_color="green",
                     annotation_text=f"Median: {median_val:.3f}", annotation_position="top left")

        # Add statistics as annotations
        stats_text = (
            f"Mean: {mean_val:.3f}<br>"
            f"Median: {median_val:.3f}<br>"
            f"Std Dev: {std_val:.3f}<br>"
            f"Min: {min_val:.3f}<br>"
            f"Max: {max_val:.3f}"
        )

        fig.add_annotation(
            x=0.95,
            y=0.95,
            xref="paper",
            yref="paper",
            text=stats_text,
            showarrow=False,
            font=dict(size=10),
            align="right",
            bgcolor="rgba(255,255,255,0.8)",
            bordercolor="black",
            borderwidth=1,
            borderpad=4
        )
    else:
        # Add a message if no data is available
        fig.add_annotation(
            x=0.5,
            y=0.5,
            xref="paper",
            yref="paper",
            text="No data available for this descriptor",
            showarrow=False,
            font=dict(size=14)
        )

    # Get display name for the descriptor
    display_name = {
        'hfc': 'High Frequency Content',
        'norm_fdom': 'Normalized Dominant Frequency',
        'mag_voice_slope': 'Magnitude Voice Slope',
        'WOSS': 'Weighted Overlap Spectral Slope'
    }.get(descriptor_key, descriptor_key)

    # Update layout
    fig.update_layout(
        title=title or f"{display_name} Distribution",
        xaxis_title=display_name,
        yaxis_title="Count",
        height=500,
        width=700,
        margin=dict(l=50, r=20, t=50, b=50),
        bargap=0.05
    )

    # Return the figure
    return fig

def plot_multi_trace_section(trace_data_list, time_vector, descriptors_list, plot_settings, output_type="WOSS", title=None, x_axis_values=None, x_axis_title="Trace Index"):
    """
    Plot a section view of multiple traces for a specific output type.

    Args:
        trace_data_list: List of trace data arrays
        time_vector: Time vector for the traces
        descriptors_list: List of descriptor dictionaries
        plot_settings: Dictionary containing plot settings
        output_type: Type of output to plot (default: "WOSS")
        title: Optional title for the plot
        x_axis_values: Optional array of values to use for the x-axis (e.g., inline or crossline numbers)
        x_axis_title: Optional title for the x-axis (default: "Trace Index")

    Returns:
        Plotly figure object
    """
    # Add detailed logging to help diagnose issues
    logging.info(f"plot_multi_trace_section called with output_type: {output_type}")
    logging.info(f"Number of traces: {len(trace_data_list) if trace_data_list else 'None'}")
    logging.info(f"Number of descriptors: {len(descriptors_list) if descriptors_list else 'None'}")
    logging.info(f"Time vector length: {len(time_vector) if time_vector is not None else 'None'}")
    logging.info(f"X-axis values provided: {x_axis_values is not None}")
    if x_axis_values is not None:
        logging.info(f"X-axis values length: {len(x_axis_values)}")
    logging.info(f"Plot settings keys: {list(plot_settings.keys())}")

    # Validate inputs
    if not trace_data_list or not descriptors_list or not time_vector:
        logging.error("Missing required inputs for plot_multi_trace_section")
        return None

    # Map output_type to internal descriptor keys
    output_key_map = {
        "Input Signal": "data",
        "Magnitude Spectrogram": "tf_map",
        "Magnitude * Voice": "mag_voice",
        "Normalized dominant frequencies": "norm_fdom",
        "Normalized Dominant Frequency": "norm_fdom",
        "Spectral Slope": "spec_slope",
        "Mag*Voice Slope": "mag_voice_slope",
        "Spectral Decrease": "spec_decrease",
        "HFC": "hfc",
        "Spectral Bandwidth": "spec_bandwidth",
        "Spectral Rolloff": "spec_rolloff",
        "WOSS": "WOSS"
    }

    # Get the internal key for the output type
    key = output_key_map.get(output_type, output_type)
    logging.info(f"Using key '{key}' for output type '{output_type}'")

    # Create figure
    fig = go.Figure()

    # Handle different output types
    if output_type == "Input Signal":
        # For seismic amplitude, create a heatmap from the trace data
        section_data = np.vstack([trace for trace in trace_data_list])

        # Get colormap settings
        colormap = plot_settings.get('input_signal_colormap', 'RdBu')
        cmin = plot_settings.get('input_signal_cmap_min', -1)
        cmax = plot_settings.get('input_signal_cmap_max', 1)

        # Create x-axis values if not provided
        if x_axis_values is None:
            x_axis_values = np.arange(section_data.shape[0])

        # Add heatmap
        fig.add_trace(
            go.Heatmap(
                z=section_data,
                x=x_axis_values,
                y=time_vector,
                colorscale=colormap,
                zmin=cmin,
                zmax=cmax,
                colorbar=dict(
                    title="Amplitude",
                    thickness=20,
                    len=0.9,
                    y=0.5,
                    yanchor="middle"
                )
            )
        )
    else:
        # For spectral attributes, extract data from descriptors
        data_list = []
        valid_descriptors = []

        # Filter out None or empty descriptors
        for desc in descriptors_list:
            if desc is not None and isinstance(desc, dict) and len(desc) > 0:
                valid_descriptors.append(desc)
            else:
                logging.warning("Found None or empty descriptor in descriptors_list")

        # Log information about the valid descriptors
        logging.info(f"Found {len(valid_descriptors)} valid descriptors out of {len(descriptors_list)} total")

        if valid_descriptors:
            # Log keys in the first valid descriptor for debugging with error handling
            first_desc = valid_descriptors[0]
            try:
                if isinstance(first_desc, dict):
                    logging.info(f"First descriptor keys: {list(first_desc.keys())}")
                else:
                    logging.warning(f"First valid descriptor is not a dictionary: {type(first_desc)}")
            except Exception as e:
                logging.error(f"Error accessing keys of first descriptor in visualization: {e}")

        # Extract data for the selected output type
        for descriptor in valid_descriptors:
            # Special handling for Magnitude Spectrogram which could be in 'tf_map' or 'mag'
            if output_type == "Magnitude Spectrogram" and "mag" in descriptor and isinstance(descriptor["mag"], np.ndarray):
                # Use 'mag' if available for backward compatibility
                spec_data = descriptor["mag"]
                logging.info(f"Using 'mag' for Magnitude Spectrogram, shape: {spec_data.shape}")
            elif key in descriptor and isinstance(descriptor[key], np.ndarray):
                spec_data = descriptor[key]
                logging.info(f"Using '{key}' for {output_type}, shape: {spec_data.shape}")
            else:
                # Create zeros array of the same length as time_vector
                logging.info(f"Key '{key}' not found or not an array in descriptor, using zeros")
                data_list.append(np.zeros_like(time_vector))
                continue

            # Ensure data has the same length as time_vector
            if len(spec_data) == len(time_vector):
                data_list.append(spec_data)
            else:
                # Try to resample if lengths don't match
                try:
                    resampled_data = np.interp(
                        np.linspace(0, 1, len(time_vector)),
                        np.linspace(0, 1, len(spec_data)),
                        spec_data
                    )
                    data_list.append(resampled_data)
                    logging.info(f"Resampled data from length {len(spec_data)} to {len(time_vector)}")
                except Exception as e:
                    logging.warning(f"Failed to resample data: {e}")
                    data_list.append(np.zeros_like(time_vector))

        # Stack the data into a 2D array
        if data_list:
            section_data = np.vstack(data_list)
            logging.info(f"Created section data with shape: {section_data.shape}")
        else:
            # Create empty section if no data
            logging.warning("No valid data found for section plot, creating empty section")
            section_data = np.zeros((1, len(time_vector)))

        # Get colormap settings for the output type
        colormap_key = key.lower().replace('*', '_').replace(' ', '_')
        colormap = plot_settings.get(f'{colormap_key}_colormap', 'Viridis')

        # For Options 2 and 3 (Inline/Crossline Analysis): Use colormap limits from plot_settings
        # Try multiple possible key formats for colormap limits, prioritizing Step 2 configured limits
        cmin = None
        cmax = None

        # First priority: Try the colormap_key with cmap_min/cmap_max suffix (from Step 2 Configure Display)
        if f'{colormap_key}_cmap_min' in plot_settings and f'{colormap_key}_cmap_max' in plot_settings:
            cmin = plot_settings[f'{colormap_key}_cmap_min']
            cmax = plot_settings[f'{colormap_key}_cmap_max']
            logging.info(f"Using limits from Step 2 Configure Display '{colormap_key}_cmap_min/max' keys: [{cmin}, {cmax}]")
        # Second priority: Try the exact output_type as a key
        elif output_type in plot_settings and isinstance(plot_settings[output_type], list) and len(plot_settings[output_type]) == 2:
            cmin, cmax = plot_settings[output_type]
            logging.info(f"Using limits from '{output_type}' key: [{cmin}, {cmax}]")
        # Third priority: Try the key
        elif key in plot_settings and isinstance(plot_settings[key], list) and len(plot_settings[key]) == 2:
            cmin, cmax = plot_settings[key]
            logging.info(f"Using limits from '{key}' key: [{cmin}, {cmax}]")
        # Finally, calculate from the data
        else:
            # Calculate reasonable limits from the data
            if np.any(section_data):
                # Use percentiles to avoid outliers
                cmin = np.percentile(section_data, 5)
                cmax = np.percentile(section_data, 95)
                logging.info(f"Calculated limits from data: [{cmin}, {cmax}]")
            else:
                # Default limits if no data
                cmin = -1
                cmax = 1
                logging.info(f"Using default limits: [{cmin}, {cmax}]")

        # Create x-axis values if not provided
        if x_axis_values is None:
            x_axis_values = np.arange(section_data.shape[0])
            logging.info(f"Created default x-axis values with length {len(x_axis_values)}")

        # Add heatmap
        fig.add_trace(
            go.Heatmap(
                z=section_data,
                x=x_axis_values,
                y=time_vector,
                colorscale=colormap,
                zmin=cmin,
                zmax=cmax,
                colorbar=dict(
                    title=output_type,
                    thickness=20,
                    len=0.9,
                    y=0.5,
                    yanchor="middle"
                )
            )
        )

    # Update layout
    fig.update_layout(
        title=title or f"{output_type} Section",
        xaxis_title=x_axis_title,
        yaxis_title="Time (s)",
        height=600,
        width=900,
        margin=dict(l=50, r=50, t=50, b=50),
        yaxis=dict(autorange="reversed")  # Reverse y-axis to match seismic convention
    )

    logging.info("Successfully created section plot")
    return fig

def plot_descriptor_crossplot(descriptors, x_key, y_key, plot_settings, title=None):
    """
    Create a crossplot visualization comparing two spectral descriptors.

    This function generates a Plotly figure showing the relationship between
    two spectral attributes as a scatter plot. Useful for identifying correlations
    or patterns between different attributes.

    Args:
        descriptors: List of dictionaries containing spectral descriptors
        x_key: Key identifying which descriptor to plot on x-axis
        y_key: Key identifying which descriptor to plot on y-axis
        plot_settings: Dictionary containing display settings and parameters
        title: Optional title for the plot

    Returns:
        plotly.graph_objects.Figure: Figure containing the descriptor crossplot
    """
    # Extract the descriptor data from each trace's descriptors
    x_values = []
    y_values = []

    for desc in descriptors:
        if x_key in desc and y_key in desc:
            x_data = desc[x_key]
            y_data = desc[y_key]

            # Ensure both arrays have the same shape
            if isinstance(x_data, np.ndarray) and isinstance(y_data, np.ndarray) and x_data.shape == y_data.shape:
                # Flatten and add all values
                x_values.extend(x_data.flatten())
                y_values.extend(y_data.flatten())

    # Create figure
    fig = go.Figure()

    if x_values and y_values:
        # Add scatter plot
        fig.add_trace(
            go.Scatter(
                x=x_values,
                y=y_values,
                mode='markers',
                marker=dict(
                    size=5,
                    opacity=0.5,
                    color=plot_settings.get('crossplot_color', 'blue')
                )
            )
        )

        # Calculate correlation coefficient
        correlation = np.corrcoef(x_values, y_values)[0, 1]

        # Add correlation as annotation
        fig.add_annotation(
            x=0.95,
            y=0.95,
            xref="paper",
            yref="paper",
            text=f"Correlation: {correlation:.3f}",
            showarrow=False,
            font=dict(size=12),
            align="right",
            bgcolor="rgba(255,255,255,0.8)",
            bordercolor="black",
            borderwidth=1,
            borderpad=4
        )

        # Add trend line if correlation is significant
        if abs(correlation) > 0.3:
            # Calculate linear regression
            z = np.polyfit(x_values, y_values, 1)
            p = np.poly1d(z)

            # Add trend line
            x_range = np.linspace(min(x_values), max(x_values), 100)
            fig.add_trace(
                go.Scatter(
                    x=x_range,
                    y=p(x_range),
                    mode='lines',
                    line=dict(color='red', width=2, dash='dash'),
                    name=f"Trend: y = {z[0]:.3f}x + {z[1]:.3f}"
                )
            )
    else:
        # Add a message if no data is available
        fig.add_annotation(
            x=0.5,
            y=0.5,
            xref="paper",
            yref="paper",
            text="Insufficient data for crossplot",
            showarrow=False,
            font=dict(size=14)
        )

    # Get display names for the descriptors
    x_display_name = {
        'hfc': 'High Frequency Content',
        'norm_fdom': 'Normalized Dominant Frequency',
        'mag_voice_slope': 'Magnitude Voice Slope',
        'WOSS': 'Weighted Overlap Spectral Slope'
    }.get(x_key, x_key)

    y_display_name = {
        'hfc': 'High Frequency Content',
        'norm_fdom': 'Normalized Dominant Frequency',
        'mag_voice_slope': 'Magnitude Voice Slope',
        'WOSS': 'Weighted Overlap Spectral Slope'
    }.get(y_key, y_key)

    # Update layout
    fig.update_layout(
        title=title or f"{x_display_name} vs {y_display_name}",
        xaxis_title=x_display_name,
        yaxis_title=y_display_name,
        height=600,
        width=700,
        margin=dict(l=50, r=20, t=50, b=50),
        showlegend=True
    )

    # Return the figure
    return fig


def plot_section_2d(section_data, time_vector, plot_settings, output_type="WOSS", title=None, x_axis_values=None, x_axis_title="Trace Index"):
    """
    Plot a 2D section view from pre-calculated section data.

    This function is specifically designed for plotting 2D section data that has already been
    calculated and stored as a 2D numpy array, such as from inline/crossline analysis.

    Args:
        section_data: 2D numpy array with shape (traces, time_samples) as returned by GPU functions.
                     Will be transposed to (time_samples, traces) for proper seismic visualization.
        time_vector: Time vector for the traces
        plot_settings: Dictionary containing plot settings
        output_type: Type of output to plot (default: "WOSS")
        title: Optional title for the plot
        x_axis_values: Optional array of values to use for the x-axis (e.g., inline or crossline numbers)
        x_axis_title: Optional title for the x-axis (default: "Trace Index")

    Returns:
        Plotly figure object
    """
    # Add detailed logging to help diagnose issues
    logging.info(f"plot_section_2d called with output_type: {output_type}")
    logging.info(f"Section data shape: {section_data.shape if section_data is not None else 'None'}")
    logging.info(f"Time vector length: {len(time_vector) if time_vector is not None else 'None'}")
    logging.info(f"X-axis values provided: {x_axis_values is not None}")
    if x_axis_values is not None:
        logging.info(f"X-axis values length: {len(x_axis_values)}")
    logging.info(f"Plot settings keys: {list(plot_settings.keys())}")

    # Validate inputs
    if section_data is None or time_vector is None:
        logging.error("Missing required inputs for plot_section_2d")
        return None

    # Ensure section_data is 2D
    if section_data.ndim != 2:
        logging.error(f"Section data must be 2D, got {section_data.ndim}D")
        return None

    # Data orientation fix: GPU functions return data as (traces, time_samples)
    # For proper seismic section visualization, we need (time_samples, traces)
    # This matches the reference tkinter implementation which transposes the data

    # Check if data dimensions match expected format
    if section_data.shape[1] == len(time_vector):
        # Data is (traces, time_samples) - transpose for visualization
        plot_data = section_data.T
        logging.info(f"Data orientation: (traces={section_data.shape[0]}, time_samples={section_data.shape[1]}) - transposing to (time_samples, traces) for visualization")
    elif section_data.shape[0] == len(time_vector):
        # Data is already (time_samples, traces) - use as is
        plot_data = section_data
        logging.info(f"Data orientation: (time_samples={section_data.shape[0]}, traces={section_data.shape[1]}) - using as is")
    else:
        # Try to interpolate to match time vector length
        logging.warning(f"Data shape {section_data.shape} doesn't match time vector length {len(time_vector)}")
        # Assume data is (traces, time_samples) and interpolate, then transpose
        from scipy import interpolate
        try:
            new_data = np.zeros((section_data.shape[0], len(time_vector)))
            for i in range(section_data.shape[0]):
                f = interpolate.interp1d(np.linspace(0, 1, section_data.shape[1]),
                                       section_data[i], kind='linear', fill_value='extrapolate')
                new_data[i] = f(np.linspace(0, 1, len(time_vector)))
            # Transpose interpolated data for visualization
            plot_data = new_data.T
            logging.info(f"Interpolated data to shape: {new_data.shape}, then transposed to {plot_data.shape} for visualization")
        except Exception as e:
            logging.error(f"Failed to interpolate data: {e}")
            return None

    # Create x-axis values if not provided
    # Note: plot_data is now (time_samples, traces), so traces are in dimension 1
    if x_axis_values is None:
        x_axis_values = np.arange(plot_data.shape[1])
        logging.info(f"Created default x-axis values with length {len(x_axis_values)} for {plot_data.shape[1]} traces")

    # Get colormap settings for the output type
    colormap_key = output_type.lower().replace('*', '_').replace(' ', '_')
    colormap = plot_settings.get(f'{colormap_key}_colormap', 'Viridis')

    # Get colormap limits with priority order
    cmin = None
    cmax = None

    # First priority: Try the colormap_key with cmap_min/cmap_max suffix (from Step 2 Configure Display)
    if f'{colormap_key}_cmap_min' in plot_settings and f'{colormap_key}_cmap_max' in plot_settings:
        cmin = plot_settings[f'{colormap_key}_cmap_min']
        cmax = plot_settings[f'{colormap_key}_cmap_max']
        logging.info(f"Using limits from Step 2 Configure Display '{colormap_key}_cmap_min/max' keys: [{cmin}, {cmax}]")
    # Second priority: Try the exact output_type as a key
    elif output_type in plot_settings and isinstance(plot_settings[output_type], list) and len(plot_settings[output_type]) == 2:
        cmin, cmax = plot_settings[output_type]
        logging.info(f"Using limits from '{output_type}' key: [{cmin}, {cmax}]")
    # Finally, calculate from the data
    else:
        # Calculate reasonable limits from the data
        if np.any(plot_data):
            # Use percentiles to avoid outliers
            cmin = np.percentile(plot_data, 5)
            cmax = np.percentile(plot_data, 95)
            logging.info(f"Calculated limits from data: [{cmin}, {cmax}]")
        else:
            # Default limits if no data
            cmin = -1
            cmax = 1
            logging.info(f"Using default limits: [{cmin}, {cmax}]")

    # Create figure
    fig = go.Figure()

    # Add heatmap
    fig.add_trace(
        go.Heatmap(
            z=plot_data,
            x=x_axis_values,
            y=time_vector,
            colorscale=colormap,
            zmin=cmin,
            zmax=cmax,
            colorbar=dict(
                title=output_type,
                thickness=20,
                len=0.9,
                y=0.5,
                yanchor="middle"
            )
        )
    )

    # Update layout
    fig.update_layout(
        title=title or f"{output_type} Section",
        xaxis_title=x_axis_title,
        yaxis_title="Time (s)",
        height=600,
        width=900,
        margin=dict(l=50, r=50, t=50, b=50),
        yaxis=dict(autorange="reversed")  # Reverse y-axis to match seismic convention
    )

    logging.info("Successfully created 2D section plot")
    return fig


def plot_inline_crossline_section(traces_data, time_vector, trace_positions,
                                 section_type='inline', section_number=None,
                                 plot_settings=None, descriptors_list=None,
                                 selected_descriptor=None):
    """
    Create a seismic section visualization for inline or crossline data.

    This function generates a Plotly figure showing either:
    1. Seismic amplitude data as a section
    2. Spectral descriptors as a section

    Args:
        traces_data: 2D numpy array (n_traces x n_samples) containing seismic trace data
        time_vector: Numpy array containing the time values for the traces
        trace_positions: Numpy array containing the position (inline/crossline numbers) for each trace
        section_type: 'inline' or 'crossline' - type of section being displayed
        section_number: The inline or crossline number being displayed
        plot_settings: Dictionary containing display settings
        descriptors_list: Optional list of dictionaries containing spectral descriptors for each trace
        selected_descriptor: Optional string specifying which descriptor to plot (e.g., 'hfc', 'WOSS')

    Returns:
        plotly.graph_objects.Figure: Figure containing the section visualization
    """
    if plot_settings is None:
        plot_settings = {}

    # Create figure
    fig = go.Figure()

    # Determine what to plot
    if selected_descriptor and descriptors_list:
        # Plot spectral descriptor as section
        plot_descriptor_as_section(fig, descriptors_list, selected_descriptor,
                                 time_vector, trace_positions, section_type,
                                 section_number, plot_settings)
    else:
        # Plot seismic amplitude as section
        plot_amplitude_section(fig, traces_data, time_vector, trace_positions,
                              section_type, section_number, plot_settings)

    return fig


def plot_amplitude_section(fig, traces_data, time_vector, trace_positions,
                          section_type, section_number, plot_settings):
    """
    Add seismic amplitude section to the figure.

    Args:
        fig: Plotly figure object
        traces_data: 2D numpy array (n_traces x n_samples)
        time_vector: Time vector
        trace_positions: Position values for each trace
        section_type: 'inline' or 'crossline'
        section_number: Section number
        plot_settings: Display settings
    """
    # Get colormap settings
    colormap = plot_settings.get('amplitude_cmap', 'RdBu')
    cmin = plot_settings.get('input_signal_cmap_min', -np.percentile(np.abs(traces_data), 95))
    cmax = plot_settings.get('input_signal_cmap_max', np.percentile(np.abs(traces_data), 95))

    # Create position labels
    if section_type == 'inline':
        x_label = 'Crossline'
        title = f'Inline {section_number} - Seismic Amplitude Section'
    else:
        x_label = 'Inline'
        title = f'Crossline {section_number} - Seismic Amplitude Section'

    # Add heatmap
    fig.add_trace(
        go.Heatmap(
            z=traces_data.T,  # Transpose to have time on y-axis
            x=trace_positions,
            y=time_vector,
            colorscale=colormap,
            zmin=cmin,
            zmax=cmax,
            colorbar=dict(
                title='Amplitude',
                thickness=20,
                len=0.9,
                y=0.5,
                yanchor="middle"
            )
        )
    )

    # Update layout
    fig.update_layout(
        title=title,
        xaxis_title=x_label,
        yaxis_title='Time (s)',
        height=600,
        width=max(800, min(1200, len(trace_positions) * 2)),  # Dynamic width based on traces
        margin=dict(l=50, r=80, t=50, b=50),
        yaxis=dict(autorange='reversed')  # Reverse y-axis for seismic convention
    )

    # Apply time range if specified
    if 'Time (Y-axis)' in plot_settings:
        fig.update_yaxes(range=plot_settings['Time (Y-axis)'][::-1])


def plot_descriptor_as_section(fig, descriptors_list, descriptor_key, time_vector,
                              trace_positions, section_type, section_number, plot_settings):
    """
    Add spectral descriptor section to the figure.

    Args:
        fig: Plotly figure object
        descriptors_list: List of descriptor dictionaries
        descriptor_key: Key of descriptor to plot (e.g., 'hfc', 'WOSS')
        time_vector: Time vector
        trace_positions: Position values for each trace
        section_type: 'inline' or 'crossline'
        section_number: Section number
        plot_settings: Display settings
    """
    # Extract descriptor data
    data_list = []
    for desc in descriptors_list:
        if descriptor_key in desc and isinstance(desc[descriptor_key], np.ndarray):
            if len(desc[descriptor_key]) == len(time_vector):
                data_list.append(desc[descriptor_key])
            else:
                # Handle length mismatch by resampling
                try:
                    resampled = np.interp(
                        np.linspace(0, 1, len(time_vector)),
                        np.linspace(0, 1, len(desc[descriptor_key])),
                        desc[descriptor_key]
                    )
                    data_list.append(resampled)
                except:
                    data_list.append(np.zeros_like(time_vector))
        else:
            data_list.append(np.zeros_like(time_vector))

    if not data_list:
        # No data to plot
        fig.add_annotation(
            text=f"No data available for {descriptor_key}",
            x=0.5, y=0.5,
            xref="paper", yref="paper",
            showarrow=False,
            font=dict(size=14, color="red")
        )
        return

    # Stack data into 2D array
    section_data = np.vstack(data_list)

    # Get display name for descriptor
    display_names = {
        'hfc': 'High Frequency Content',
        'spec_decrease': 'Spectral Decrease',
        'spec_slope': 'Spectral Slope',
        'norm_fdom': 'Normalized Dominant Frequency',
        'mag_voice_slope': 'Magnitude Voice Slope',
        'spec_bandwidth': 'Spectral Bandwidth',
        'spec_rolloff': 'Spectral Rolloff',
        'WOSS': 'Weighted Overlap Spectral Slope'
    }
    display_name = display_names.get(descriptor_key, descriptor_key.upper())

    # Get colormap settings
    colormap = plot_settings.get(f'{descriptor_key}_cmap', 'Viridis')

    # Get min/max from plot_settings or calculate from data
    cmin = plot_settings.get(f'{descriptor_key}_cmap_min')
    cmax = plot_settings.get(f'{descriptor_key}_cmap_max')

    if cmin is None or cmax is None:
        # Calculate from data
        valid_data = section_data[np.isfinite(section_data)]
        if len(valid_data) > 0:
            if descriptor_key in ['WOSS', 'mag_voice_slope']:
                # Use symmetric range for these descriptors
                abs_max = np.percentile(np.abs(valid_data), 95)
                cmin = -abs_max
                cmax = abs_max
            else:
                cmin = np.percentile(valid_data, 5)
                cmax = np.percentile(valid_data, 95)
        else:
            cmin, cmax = 0, 1

    # Create position labels
    if section_type == 'inline':
        x_label = 'Crossline'
        title = f'Inline {section_number} - {display_name}'
    else:
        x_label = 'Inline'
        title = f'Crossline {section_number} - {display_name}'

    # Add heatmap
    fig.add_trace(
        go.Heatmap(
            z=section_data.T,  # Transpose to have time on y-axis
            x=trace_positions,
            y=time_vector,
            colorscale=colormap,
            zmin=cmin,
            zmax=cmax,
            colorbar=dict(
                title=display_name,
                thickness=20,
                len=0.9,
                y=0.5,
                yanchor="middle"
            )
        )
    )

    # Update layout
    fig.update_layout(
        title=title,
        xaxis_title=x_label,
        yaxis_title='Time (s)',
        height=600,
        width=max(800, min(1200, len(trace_positions) * 2)),  # Dynamic width
        margin=dict(l=50, r=80, t=50, b=50),
        yaxis=dict(autorange='reversed')  # Reverse y-axis
    )

    # Apply time range if specified
    if 'Time (Y-axis)' in plot_settings:
        fig.update_yaxes(range=plot_settings['Time (Y-axis)'][::-1])


def create_inline_crossline_comparison(inline_data, crossline_data, inline_positions,
                                      crossline_positions, inline_number, crossline_number,
                                      time_vector, plot_settings=None, descriptor_key=None):
    """
    Create a side-by-side comparison of inline and crossline sections.

    Args:
        inline_data: 2D array of inline trace data or descriptor values
        crossline_data: 2D array of crossline trace data or descriptor values
        inline_positions: Position values for inline traces
        crossline_positions: Position values for crossline traces
        inline_number: Inline number
        crossline_number: Crossline number
        time_vector: Time vector
        plot_settings: Display settings
        descriptor_key: If provided, indicates this is descriptor data (not amplitude)

    Returns:
        plotly.graph_objects.Figure: Figure with side-by-side sections
    """
    if plot_settings is None:
        plot_settings = {}

    # Create subplots
    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=[f'Inline {inline_number}', f'Crossline {crossline_number}'],
        horizontal_spacing=0.1
    )

    # Get display settings
    if descriptor_key:
        # Descriptor data
        display_names = {
            'hfc': 'HFC', 'norm_fdom': 'Norm. Dom. Freq',
            'mag_voice_slope': 'Mag Voice Slope', 'WOSS': 'WOSS'
        }
        display_name = display_names.get(descriptor_key, descriptor_key.upper())
        colormap = plot_settings.get(f'{descriptor_key}_cmap', 'Viridis')

        # Get color range
        all_data = np.concatenate([inline_data.flatten(), crossline_data.flatten()])
        valid_data = all_data[np.isfinite(all_data)]
        if len(valid_data) > 0:
            if descriptor_key in ['WOSS', 'mag_voice_slope']:
                abs_max = np.percentile(np.abs(valid_data), 95)
                cmin, cmax = -abs_max, abs_max
            else:
                cmin = np.percentile(valid_data, 5)
                cmax = np.percentile(valid_data, 95)
        else:
            cmin, cmax = 0, 1
    else:
        # Amplitude data
        display_name = 'Amplitude'
        colormap = plot_settings.get('amplitude_cmap', 'RdBu')
        all_data = np.concatenate([inline_data.flatten(), crossline_data.flatten()])
        abs_max = np.percentile(np.abs(all_data), 95)
        cmin, cmax = -abs_max, abs_max

    # Add inline section
    fig.add_trace(
        go.Heatmap(
            z=inline_data.T,
            x=inline_positions,
            y=time_vector,
            colorscale=colormap,
            zmin=cmin,
            zmax=cmax,
            showscale=False
        ),
        row=1, col=1
    )

    # Add crossline section
    fig.add_trace(
        go.Heatmap(
            z=crossline_data.T,
            x=crossline_positions,
            y=time_vector,
            colorscale=colormap,
            zmin=cmin,
            zmax=cmax,
            colorbar=dict(
                title=display_name,
                thickness=20,
                len=0.9,
                y=0.5,
                yanchor="middle"
            )
        ),
        row=1, col=2
    )

    # Update axes
    fig.update_xaxes(title_text='Crossline', row=1, col=1)
    fig.update_xaxes(title_text='Inline', row=1, col=2)
    fig.update_yaxes(title_text='Time (s)', row=1, col=1, autorange='reversed')
    fig.update_yaxes(title_text='', row=1, col=2, autorange='reversed')

    # Apply time range if specified
    if 'Time (Y-axis)' in plot_settings:
        time_range = plot_settings['Time (Y-axis)'][::-1]
        fig.update_yaxes(range=time_range, row=1, col=1)
        fig.update_yaxes(range=time_range, row=1, col=2)

    # Update layout
    fig.update_layout(
        title=f'{display_name} Sections Comparison',
        height=600,
        width=1200,
        margin=dict(l=50, r=80, t=80, b=50)
    )

    return fig


def plot_inline_crossline_section(traces_data, time_vector, trace_positions,
                                 section_type='inline', section_number=None,
                                 plot_settings=None, descriptors_list=None,
                                 selected_descriptor=None):
    """
    Create a seismic section visualization for inline or crossline data.

    This function generates a Plotly figure showing either:
    1. Seismic amplitude data as a section
    2. Spectral descriptors as a section

    Args:
        traces_data: 2D numpy array (n_traces x n_samples) containing seismic trace data
        time_vector: Numpy array containing the time values for the traces
        trace_positions: Numpy array containing the position (inline/crossline numbers) for each trace
        section_type: 'inline' or 'crossline' - type of section being displayed
        section_number: The inline or crossline number being displayed
        plot_settings: Dictionary containing display settings
        descriptors_list: Optional list of dictionaries containing spectral descriptors for each trace
        selected_descriptor: Optional string specifying which descriptor to plot (e.g., 'hfc', 'WOSS')

    Returns:
        plotly.graph_objects.Figure: Figure containing the section visualization
    """
    if plot_settings is None:
        plot_settings = {}

    # Create figure
    fig = go.Figure()

    # Determine what to plot
    if selected_descriptor and descriptors_list:
        # Plot spectral descriptor as section
        plot_descriptor_as_section(fig, descriptors_list, selected_descriptor,
                                 time_vector, trace_positions, section_type,
                                 section_number, plot_settings)
    else:
        # Plot seismic amplitude as section
        plot_amplitude_section(fig, traces_data, time_vector, trace_positions,
                              section_type, section_number, plot_settings)

    return fig


def plot_amplitude_section(fig, traces_data, time_vector, trace_positions,
                          section_type, section_number, plot_settings):
    """
    Add seismic amplitude section to the figure.

    Args:
        fig: Plotly figure object
        traces_data: 2D numpy array (n_traces x n_samples)
        time_vector: Time vector
        trace_positions: Position values for each trace
        section_type: 'inline' or 'crossline'
        section_number: Section number
        plot_settings: Display settings
    """
    # Get colormap settings
    colormap = plot_settings.get('amplitude_cmap', 'RdBu')
    cmin = plot_settings.get('input_signal_cmap_min', -np.percentile(np.abs(traces_data), 95))
    cmax = plot_settings.get('input_signal_cmap_max', np.percentile(np.abs(traces_data), 95))

    # Create position labels
    if section_type == 'inline':
        x_label = 'Crossline'
        title = f'Inline {section_number} - Seismic Amplitude Section'
    else:
        x_label = 'Inline'
        title = f'Crossline {section_number} - Seismic Amplitude Section'

    # Add heatmap
    fig.add_trace(
        go.Heatmap(
            z=traces_data.T,  # Transpose to have time on y-axis
            x=trace_positions,
            y=time_vector,
            colorscale=colormap,
            zmin=cmin,
            zmax=cmax,
            colorbar=dict(
                title='Amplitude',
                thickness=20,
                len=0.9,
                y=0.5,
                yanchor="middle"
            )
        )
    )

    # Update layout
    fig.update_layout(
        title=title,
        xaxis_title=x_label,
        yaxis_title='Time (s)',
        height=600,
        width=max(800, min(1200, len(trace_positions) * 2)),  # Dynamic width based on traces
        margin=dict(l=50, r=80, t=50, b=50),
        yaxis=dict(autorange='reversed')  # Reverse y-axis for seismic convention
    )

    # Apply time range if specified
    if 'Time (Y-axis)' in plot_settings:
        fig.update_yaxes(range=plot_settings['Time (Y-axis)'][::-1])


def plot_descriptor_as_section(fig, descriptors_list, descriptor_key, time_vector,
                              trace_positions, section_type, section_number, plot_settings):
    """
    Add spectral descriptor section to the figure.

    Args:
        fig: Plotly figure object
        descriptors_list: List of descriptor dictionaries
        descriptor_key: Key of descriptor to plot (e.g., 'hfc', 'WOSS')
        time_vector: Time vector
        trace_positions: Position values for each trace
        section_type: 'inline' or 'crossline'
        section_number: Section number
        plot_settings: Display settings
    """
    # Extract descriptor data
    data_list = []
    for desc in descriptors_list:
        if descriptor_key in desc and isinstance(desc[descriptor_key], np.ndarray):
            if len(desc[descriptor_key]) == len(time_vector):
                data_list.append(desc[descriptor_key])
            else:
                # Handle length mismatch by resampling
                try:
                    resampled = np.interp(
                        np.linspace(0, 1, len(time_vector)),
                        np.linspace(0, 1, len(desc[descriptor_key])),
                        desc[descriptor_key]
                    )
                    data_list.append(resampled)
                except:
                    data_list.append(np.zeros_like(time_vector))
        else:
            data_list.append(np.zeros_like(time_vector))

    if not data_list:
        # No data to plot
        fig.add_annotation(
            text=f"No data available for {descriptor_key}",
            x=0.5, y=0.5,
            xref="paper", yref="paper",
            showarrow=False,
            font=dict(size=14, color="red")
        )
        return

    # Stack data into 2D array
    section_data = np.vstack(data_list)

    # Get display name for descriptor
    display_names = {
        'hfc': 'High Frequency Content',
        'spec_decrease': 'Spectral Decrease',
        'spec_slope': 'Spectral Slope',
        'norm_fdom': 'Normalized Dominant Frequency',
        'mag_voice_slope': 'Magnitude Voice Slope',
        'spec_bandwidth': 'Spectral Bandwidth',
        'spec_rolloff': 'Spectral Rolloff',
        'WOSS': 'Weighted Overlap Spectral Slope'
    }
    display_name = display_names.get(descriptor_key, descriptor_key.upper())

    # Get colormap settings
    colormap = plot_settings.get(f'{descriptor_key}_cmap', 'Viridis')

    # Get min/max from plot_settings or calculate from data
    cmin = plot_settings.get(f'{descriptor_key}_cmap_min')
    cmax = plot_settings.get(f'{descriptor_key}_cmap_max')

    if cmin is None or cmax is None:
        # Calculate from data
        valid_data = section_data[np.isfinite(section_data)]
        if len(valid_data) > 0:
            if descriptor_key in ['WOSS', 'mag_voice_slope']:
                # Use symmetric range for these descriptors
                abs_max = np.percentile(np.abs(valid_data), 95)
                cmin = -abs_max
                cmax = abs_max
            else:
                cmin = np.percentile(valid_data, 5)
                cmax = np.percentile(valid_data, 95)
        else:
            cmin, cmax = 0, 1

    # Create position labels
    if section_type == 'inline':
        x_label = 'Crossline'
        title = f'Inline {section_number} - {display_name}'
    else:
        x_label = 'Inline'
        title = f'Crossline {section_number} - {display_name}'

    # Add heatmap
    fig.add_trace(
        go.Heatmap(
            z=section_data.T,  # Transpose to have time on y-axis
            x=trace_positions,
            y=time_vector,
            colorscale=colormap,
            zmin=cmin,
            zmax=cmax,
            colorbar=dict(
                title=display_name,
                thickness=20,
                len=0.9,
                y=0.5,
                yanchor="middle"
            )
        )
    )

    # Update layout
    fig.update_layout(
        title=title,
        xaxis_title=x_label,
        yaxis_title='Time (s)',
        height=600,
        width=max(800, min(1200, len(trace_positions) * 2)),  # Dynamic width
        margin=dict(l=50, r=80, t=50, b=50),
        yaxis=dict(autorange='reversed')  # Reverse y-axis
    )

    # Apply time range if specified
    if 'Time (Y-axis)' in plot_settings:
        fig.update_yaxes(range=plot_settings['Time (Y-axis)'][::-1])


def create_inline_crossline_comparison(inline_data, crossline_data, inline_positions,
                                      crossline_positions, inline_number, crossline_number,
                                      time_vector, plot_settings=None, descriptor_key=None):
    """
    Create a side-by-side comparison of inline and crossline sections.

    Args:
        inline_data: 2D array of inline trace data or descriptor values
        crossline_data: 2D array of crossline trace data or descriptor values
        inline_positions: Position values for inline traces
        crossline_positions: Position values for crossline traces
        inline_number: Inline number
        crossline_number: Crossline number
        time_vector: Time vector
        plot_settings: Display settings
        descriptor_key: If provided, indicates this is descriptor data (not amplitude)

    Returns:
        plotly.graph_objects.Figure: Figure with side-by-side sections
    """
    if plot_settings is None:
        plot_settings = {}

    # Create subplots
    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=[f'Inline {inline_number}', f'Crossline {crossline_number}'],
        horizontal_spacing=0.1
    )

    # Get display settings
    if descriptor_key:
        # Descriptor data
        display_names = {
            'hfc': 'HFC', 'norm_fdom': 'Norm. Dom. Freq',
            'mag_voice_slope': 'Mag Voice Slope', 'WOSS': 'WOSS'
        }
        display_name = display_names.get(descriptor_key, descriptor_key.upper())
        colormap = plot_settings.get(f'{descriptor_key}_cmap', 'Viridis')

        # Get color range
        all_data = np.concatenate([inline_data.flatten(), crossline_data.flatten()])
        valid_data = all_data[np.isfinite(all_data)]
        if len(valid_data) > 0:
            if descriptor_key in ['WOSS', 'mag_voice_slope']:
                abs_max = np.percentile(np.abs(valid_data), 95)
                cmin, cmax = -abs_max, abs_max
            else:
                cmin = np.percentile(valid_data, 5)
                cmax = np.percentile(valid_data, 95)
        else:
            cmin, cmax = 0, 1
    else:
        # Amplitude data
        display_name = 'Amplitude'
        colormap = plot_settings.get('amplitude_cmap', 'RdBu')
        all_data = np.concatenate([inline_data.flatten(), crossline_data.flatten()])
        abs_max = np.percentile(np.abs(all_data), 95)
        cmin, cmax = -abs_max, abs_max

    # Add inline section
    fig.add_trace(
        go.Heatmap(
            z=inline_data.T,
            x=inline_positions,
            y=time_vector,
            colorscale=colormap,
            zmin=cmin,
            zmax=cmax,
            showscale=False
        ),
        row=1, col=1
    )

    # Add crossline section
    fig.add_trace(
        go.Heatmap(
            z=crossline_data.T,
            x=crossline_positions,
            y=time_vector,
            colorscale=colormap,
            zmin=cmin,
            zmax=cmax,
            colorbar=dict(
                title=display_name,
                thickness=20,
                len=0.9,
                y=0.5,
                yanchor="middle"
            )
        ),
        row=1, col=2
    )

    # Update axes
    fig.update_xaxes(title_text='Crossline', row=1, col=1)
    fig.update_xaxes(title_text='Inline', row=1, col=2)
    fig.update_yaxes(title_text='Time (s)', row=1, col=1, autorange='reversed')
    fig.update_yaxes(title_text='', row=1, col=2, autorange='reversed')

    # Apply time range if specified
    if 'Time (Y-axis)' in plot_settings:
        time_range = plot_settings['Time (Y-axis)'][::-1]
        fig.update_yaxes(range=time_range, row=1, col=1)
        fig.update_yaxes(range=time_range, row=1, col=2)

    # Update layout
    fig.update_layout(
        title=f'{display_name} Sections Comparison',
        height=600,
        width=1200,
        margin=dict(l=50, r=80, t=80, b=50)
    )

    return fig
