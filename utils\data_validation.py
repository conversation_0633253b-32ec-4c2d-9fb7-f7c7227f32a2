"""
Data Validation Module for WOSS Seismic Analysis Tool.

This module provides comprehensive validation functions for ensuring data integrity
and readiness before proceeding to analysis. It includes validation for trace data
loading, area selection completeness, and data quality checks.
"""

import streamlit as st
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional, Any
from utils.data_utils import load_trace_sample
import time

class DataValidationStatus:
    """Class to represent data validation status."""
    
    def __init__(self):
        self.is_valid = False
        self.validation_errors = []
        self.validation_warnings = []
        self.loading_progress = 0.0
        self.total_traces = 0
        self.loaded_traces = 0
        self.failed_traces = 0
        self.validation_details = {}
    
    def add_error(self, error_msg: str):
        """Add a validation error."""
        self.validation_errors.append(error_msg)
        self.is_valid = False
    
    def add_warning(self, warning_msg: str):
        """Add a validation warning."""
        self.validation_warnings.append(warning_msg)
    
    def update_progress(self, loaded: int, total: int):
        """Update loading progress."""
        self.loaded_traces = loaded
        self.total_traces = total
        self.loading_progress = (loaded / total * 100) if total > 0 else 0.0
    
    def set_valid(self):
        """Mark validation as successful."""
        if not self.validation_errors:
            self.is_valid = True

def validate_basic_data_requirements() -> DataValidationStatus:
    """
    Validate that basic data requirements are met.
    
    Returns:
        DataValidationStatus: Validation status with details
    """
    status = DataValidationStatus()
    
    # Check if SEG-Y file is loaded
    if not st.session_state.get('header_loader'):
        status.add_error("SEG-Y file not loaded. Please load data in Step 1.")
        return status
    
    # Check if display parameters are configured
    if not st.session_state.get('display_params_configured'):
        status.add_error("Display parameters not configured. Please configure in Step 2.")
        return status
    
    # Check if area is selected
    if not st.session_state.get('area_selected'):
        status.add_error("No area selected. Please select an area for analysis.")
        return status
    
    # Check if selected indices exist
    if not st.session_state.get('selected_indices'):
        status.add_error("No trace indices selected.")
        return status
    
    status.set_valid()
    return status

def validate_trace_data_integrity(trace_indices: List[int], 
                                segy_path: str,
                                sample_size: int = 10) -> DataValidationStatus:
    """
    Validate the integrity of trace data for selected indices.
    
    Args:
        trace_indices: List of trace indices to validate
        segy_path: Path to SEG-Y file
        sample_size: Number of traces to sample for validation
        
    Returns:
        DataValidationStatus: Validation status with details
    """
    status = DataValidationStatus()
    
    if not trace_indices:
        status.add_error("No trace indices provided for validation.")
        return status
    
    # Sample traces for validation (don't validate all for performance)
    sample_indices = trace_indices[:sample_size] if len(trace_indices) > sample_size else trace_indices
    
    valid_traces = 0
    invalid_traces = 0
    
    for i, trace_idx in enumerate(sample_indices):
        try:
            trace_data = load_trace_sample(segy_path, trace_idx)
            
            if trace_data is None:
                invalid_traces += 1
                status.add_warning(f"Trace {trace_idx}: Failed to load")
                continue
            
            # Check if trace has valid data
            if not isinstance(trace_data, np.ndarray):
                invalid_traces += 1
                status.add_warning(f"Trace {trace_idx}: Invalid data type")
                continue
            
            if trace_data.size == 0:
                invalid_traces += 1
                status.add_warning(f"Trace {trace_idx}: Empty trace")
                continue
            
            # Check for all-zero or all-NaN traces
            if np.all(trace_data == 0):
                status.add_warning(f"Trace {trace_idx}: All-zero trace")
            elif np.all(np.isnan(trace_data)):
                invalid_traces += 1
                status.add_warning(f"Trace {trace_idx}: All-NaN trace")
                continue
            
            valid_traces += 1
            
        except Exception as e:
            invalid_traces += 1
            status.add_warning(f"Trace {trace_idx}: Error loading - {str(e)}")
    
    # Update status
    status.validation_details = {
        'total_sampled': len(sample_indices),
        'valid_traces': valid_traces,
        'invalid_traces': invalid_traces,
        'total_selected': len(trace_indices)
    }
    
    # Determine if validation passes
    if invalid_traces == 0:
        status.set_valid()
    elif invalid_traces / len(sample_indices) < 0.1:  # Allow up to 10% invalid traces
        status.add_warning(f"Some traces have issues ({invalid_traces}/{len(sample_indices)}), but proceeding.")
        status.set_valid()
    else:
        status.add_error(f"Too many invalid traces ({invalid_traces}/{len(sample_indices)}). Data quality insufficient.")
    
    return status

def load_and_validate_traces_with_progress(trace_indices: List[int], 
                                         segy_path: str,
                                         progress_container=None) -> DataValidationStatus:
    """
    Load and validate all traces with progress tracking.
    
    Args:
        trace_indices: List of trace indices to load and validate
        segy_path: Path to SEG-Y file
        progress_container: Streamlit container for progress display
        
    Returns:
        DataValidationStatus: Validation status with loaded data
    """
    status = DataValidationStatus()
    
    if not trace_indices:
        status.add_error("No trace indices provided for loading.")
        return status
    
    loaded_trace_data = []
    failed_indices = []
    
    total_traces = len(trace_indices)
    
    # Create progress bar if container provided
    if progress_container:
        progress_bar = progress_container.progress(0)
        status_text = progress_container.empty()
    
    for i, trace_idx in enumerate(trace_indices):
        try:
            # Update progress
            progress = (i + 1) / total_traces
            status.update_progress(i + 1, total_traces)
            
            if progress_container:
                progress_bar.progress(progress)
                status_text.text(f"Loading trace {i + 1} of {total_traces} (Index: {trace_idx})")
            
            # Load trace data
            trace_data = load_trace_sample(segy_path, trace_idx)
            
            if trace_data is not None and isinstance(trace_data, np.ndarray) and trace_data.size > 0:
                loaded_trace_data.append({
                    'trace_sample': trace_data,
                    'trace_idx': trace_idx
                })
            else:
                failed_indices.append(trace_idx)
                status.failed_traces += 1
                
        except Exception as e:
            failed_indices.append(trace_idx)
            status.failed_traces += 1
            logging.warning(f"Failed to load trace {trace_idx}: {e}")
    
    # Store results in session state
    st.session_state.loaded_trace_data = loaded_trace_data
    
    # Update validation details
    status.validation_details = {
        'total_requested': total_traces,
        'successfully_loaded': len(loaded_trace_data),
        'failed_to_load': len(failed_indices),
        'failed_indices': failed_indices[:10]  # Store first 10 failed indices
    }
    
    # Determine validation result
    success_rate = len(loaded_trace_data) / total_traces if total_traces > 0 else 0
    
    if success_rate >= 0.95:  # 95% success rate required
        status.set_valid()
        if progress_container:
            status_text.success(f"✅ Successfully loaded {len(loaded_trace_data)} of {total_traces} traces")
    elif success_rate >= 0.8:  # 80-95% success rate - warning but proceed
        status.add_warning(f"Loaded {len(loaded_trace_data)} of {total_traces} traces ({success_rate:.1%})")
        status.set_valid()
        if progress_container:
            status_text.warning(f"⚠️ Loaded {len(loaded_trace_data)} of {total_traces} traces ({success_rate:.1%})")
    else:
        status.add_error(f"Failed to load sufficient traces ({success_rate:.1%} success rate)")
        if progress_container:
            status_text.error(f"❌ Only loaded {len(loaded_trace_data)} of {total_traces} traces ({success_rate:.1%})")
    
    return status

def validate_area_selection_completeness() -> DataValidationStatus:
    """
    Validate that area selection is complete and valid.
    
    Returns:
        DataValidationStatus: Validation status
    """
    status = DataValidationStatus()
    
    # Check basic area selection
    if not st.session_state.get('area_selected'):
        status.add_error("Area selection not completed.")
        return status
    
    # Check selection mode
    selection_mode = st.session_state.get('selection_mode')
    if not selection_mode:
        status.add_error("Selection mode not specified.")
        return status
    
    # Validate based on selection mode
    if selection_mode == "Single inline (all crosslines)":
        if st.session_state.get('selected_inline') is None:
            status.add_error("Inline number not selected.")
            return status
            
    elif selection_mode == "Single crossline (all inlines)":
        if st.session_state.get('selected_crossline') is None:
            status.add_error("Crossline number not selected.")
            return status
            
    elif selection_mode == "By inline/crossline section (AOI)":
        aoi_bounds = st.session_state.get('aoi_bounds')
        if not aoi_bounds:
            status.add_error("AOI bounds not defined.")
            return status
        required_keys = ['inline_min', 'inline_max', 'xline_min', 'xline_max']
        for key in required_keys:
            if key not in aoi_bounds:
                status.add_error(f"AOI bounds missing {key}.")
                return status

        # Additional AOI-specific validation
        from utils.aoi_validation import validate_aoi_selection
        aoi_validation = validate_aoi_selection()
        if not aoi_validation['valid']:
            status.add_error(f"AOI validation failed: {aoi_validation['error']}")
            return status
                
    elif selection_mode == "By Polyline File Import":
        if not st.session_state.get('polyline_file_info'):
            status.add_error("Polyline file not uploaded.")
            return status
        if st.session_state.get('polyline_tolerance') is None:
            status.add_error("Polyline tolerance not set.")
            return status
            
    elif selection_mode == "By well markers":
        if not st.session_state.get('selected_well_marker_pairs'):
            status.add_error("Well marker pairs not selected.")
            return status
    
    # Check if indices are available
    selected_indices = st.session_state.get('selected_indices', [])
    if not selected_indices:
        status.add_error("No trace indices found for selected area.")
        return status
    
    status.validation_details = {
        'selection_mode': selection_mode,
        'trace_count': len(selected_indices)
    }
    
    status.set_valid()
    return status

def get_loading_state_key(selection_mode: str) -> str:
    """
    Get the session state key for tracking loading state based on selection mode.

    Args:
        selection_mode: The current selection mode

    Returns:
        str: Session state key for loading state
    """
    mode_mapping = {
        "Single inline (all crosslines)": "traces_loaded_inline",
        "Single crossline (all inlines)": "traces_loaded_crossline",
        "By Polyline File Import": "traces_loaded_polyline",
        "By inline/crossline section (AOI)": "traces_loaded_aoi",
        "By well markers": "traces_loaded_wells"
    }
    return mode_mapping.get(selection_mode, "traces_loaded_generic")

def reset_loading_state(selection_mode: str = None):
    """
    Reset loading state for the current or specified selection mode.

    Args:
        selection_mode: Selection mode to reset, or None for current mode
    """
    if selection_mode is None:
        selection_mode = st.session_state.get('selection_mode')

    if selection_mode:
        loading_key = get_loading_state_key(selection_mode)
        st.session_state[loading_key] = False

        # Also reset related validation states
        st.session_state.data_validation_status = None
        st.session_state.data_loading_in_progress = False

        logging.info(f"Reset loading state for {selection_mode}")

def is_data_loading_required() -> bool:
    """
    Check if data loading is required for the current selection.

    Returns:
        bool: True if data loading is required
    """
    selection_mode = st.session_state.get('selection_mode')
    if not selection_mode:
        return True

    loading_key = get_loading_state_key(selection_mode)
    return not st.session_state.get(loading_key, False)

def validate_and_load_data_for_analysis() -> DataValidationStatus:
    """
    Comprehensive validation and loading function for analysis readiness.

    Returns:
        DataValidationStatus: Complete validation status
    """
    # Step 1: Basic requirements validation
    basic_status = validate_basic_data_requirements()
    if not basic_status.is_valid:
        return basic_status

    # Step 2: Area selection completeness
    area_status = validate_area_selection_completeness()
    if not area_status.is_valid:
        return area_status

    # Step 3: Check if data loading is required
    if not is_data_loading_required():
        # Data already loaded, just validate integrity
        trace_indices = st.session_state.get('selected_indices', [])
        segy_path = st.session_state.header_loader.source_file_path

        integrity_status = validate_trace_data_integrity(trace_indices, segy_path)
        return integrity_status

    # Step 4: Data loading required - return status indicating loading needed
    status = DataValidationStatus()
    status.add_warning("Data loading required before analysis can proceed.")
    status.validation_details = {
        'loading_required': True,
        'selection_mode': st.session_state.get('selection_mode'),
        'trace_count': len(st.session_state.get('selected_indices', []))
    }

    return status

def create_validation_ui_components(container, validation_status: DataValidationStatus):
    """
    Create UI components to display validation status and loading progress.

    Args:
        container: Streamlit container for UI elements
        validation_status: Current validation status
    """
    if validation_status.is_valid:
        container.success("✅ Data validation passed - Ready for analysis!")

        if validation_status.validation_details:
            details = validation_status.validation_details
            if 'total_selected' in details:
                container.info(f"📊 {details['total_selected']} traces selected and validated")

    elif validation_status.validation_errors:
        container.error("❌ Data validation failed:")
        for error in validation_status.validation_errors:
            container.error(f"• {error}")

    if validation_status.validation_warnings:
        container.warning("⚠️ Validation warnings:")
        for warning in validation_status.validation_warnings:
            container.warning(f"• {warning}")

    # Show loading progress if applicable
    if validation_status.loading_progress > 0:
        container.progress(validation_status.loading_progress / 100)
        container.text(f"Loading progress: {validation_status.loaded_traces}/{validation_status.total_traces} traces")

def should_enable_proceed_button() -> Tuple[bool, str]:
    """
    Determine if the 'Proceed to Analysis' button should be enabled.

    Returns:
        Tuple[bool, str]: (should_enable, reason_message)
    """
    # Check if validation is in progress
    if st.session_state.get('data_loading_in_progress', False):
        return False, "Data loading in progress..."

    # Get current validation status
    validation_status = st.session_state.get('data_validation_status')

    if validation_status is None:
        return False, "Data validation not performed"

    if not validation_status.is_valid:
        if validation_status.validation_errors:
            return False, f"Validation failed: {validation_status.validation_errors[0]}"
        else:
            return False, "Data validation incomplete"

    return True, "Ready for analysis"

def perform_comprehensive_validation(progress_container=None) -> DataValidationStatus:
    """
    Perform comprehensive validation including data loading with progress tracking.

    Args:
        progress_container: Streamlit container for progress display

    Returns:
        DataValidationStatus: Complete validation result
    """
    # Mark loading as in progress
    st.session_state.data_loading_in_progress = True

    try:
        # Step 1: Basic validation
        if progress_container:
            progress_container.info("🔍 Validating basic requirements...")

        basic_status = validate_basic_data_requirements()
        if not basic_status.is_valid:
            return basic_status

        # Step 2: Area selection validation
        if progress_container:
            progress_container.info("📍 Validating area selection...")

        area_status = validate_area_selection_completeness()
        if not area_status.is_valid:
            return area_status

        # Step 3: Load and validate trace data
        if progress_container:
            progress_container.info("📊 Loading and validating trace data...")

        trace_indices = st.session_state.get('selected_indices', [])
        segy_path = st.session_state.header_loader.source_file_path

        # Load traces with progress tracking
        loading_status = load_and_validate_traces_with_progress(
            trace_indices, segy_path, progress_container
        )

        if loading_status.is_valid and st.session_state.get('loaded_trace_data'):
            # Additional quality validation
            if progress_container:
                progress_container.info("🔍 Performing data quality checks...")

            # Validate trace data quality
            quality_status = validate_trace_data_quality(st.session_state.loaded_trace_data)

            # Check trace consistency
            consistency_status = check_trace_consistency(st.session_state.loaded_trace_data)

            # Validate data ranges
            range_status = validate_seismic_data_range(st.session_state.loaded_trace_data)

            # Combine validation results
            combined_status = DataValidationStatus()
            combined_status.is_valid = (loading_status.is_valid and
                                      quality_status.is_valid and
                                      consistency_status.is_valid and
                                      range_status.is_valid)

            # Combine all errors and warnings
            combined_status.validation_errors = (loading_status.validation_errors +
                                               quality_status.validation_errors +
                                               consistency_status.validation_errors +
                                               range_status.validation_errors)

            combined_status.validation_warnings = (loading_status.validation_warnings +
                                                 quality_status.validation_warnings +
                                                 consistency_status.validation_warnings +
                                                 range_status.validation_warnings)

            # Combine validation details
            combined_status.validation_details = {
                'loading': loading_status.validation_details,
                'quality': quality_status.validation_details,
                'consistency': consistency_status.validation_details,
                'range': range_status.validation_details
            }

            # Copy progress information
            combined_status.loading_progress = loading_status.loading_progress
            combined_status.total_traces = loading_status.total_traces
            combined_status.loaded_traces = loading_status.loaded_traces
            combined_status.failed_traces = loading_status.failed_traces

            if combined_status.is_valid:
                # Mark loading as complete for current mode
                selection_mode = st.session_state.get('selection_mode')
                if selection_mode:
                    loading_key = get_loading_state_key(selection_mode)
                    st.session_state[loading_key] = True

                if progress_container:
                    progress_container.success("✅ All data validation checks passed!")

            return combined_status

        return loading_status

    finally:
        # Always clear loading flag
        st.session_state.data_loading_in_progress = False

def validate_trace_data_quality(loaded_trace_data: List[Dict]) -> DataValidationStatus:
    """
    Validate the quality of loaded trace data.

    Args:
        loaded_trace_data: List of loaded trace data dictionaries

    Returns:
        DataValidationStatus: Quality validation results
    """
    status = DataValidationStatus()

    if not loaded_trace_data:
        status.add_error("No trace data loaded.")
        return status

    total_traces = len(loaded_trace_data)
    valid_traces = 0
    empty_traces = 0
    zero_traces = 0
    nan_traces = 0
    short_traces = 0

    trace_lengths = []

    for i, trace_data in enumerate(loaded_trace_data):
        try:
            trace_sample = trace_data.get('trace_sample')

            if trace_sample is None:
                status.add_warning(f"Trace {i}: No trace sample data")
                continue

            if not isinstance(trace_sample, np.ndarray):
                status.add_warning(f"Trace {i}: Invalid data type {type(trace_sample)}")
                continue

            if trace_sample.size == 0:
                empty_traces += 1
                continue

            trace_lengths.append(len(trace_sample))

            # Check for all-zero traces
            if np.all(trace_sample == 0):
                zero_traces += 1
                status.add_warning(f"Trace {i}: All-zero trace")
                continue

            # Check for all-NaN traces
            if np.all(np.isnan(trace_sample)):
                nan_traces += 1
                continue

            # Check for very short traces (less than 100 samples)
            if len(trace_sample) < 100:
                short_traces += 1
                status.add_warning(f"Trace {i}: Very short trace ({len(trace_sample)} samples)")

            valid_traces += 1

        except Exception as e:
            status.add_warning(f"Trace {i}: Error during validation - {str(e)}")

    # Calculate statistics
    if trace_lengths:
        min_length = min(trace_lengths)
        max_length = max(trace_lengths)
        avg_length = sum(trace_lengths) / len(trace_lengths)

        # Check for inconsistent trace lengths
        if max_length != min_length:
            status.add_warning(f"Inconsistent trace lengths: {min_length} to {max_length} samples")
    else:
        min_length = max_length = avg_length = 0

    # Store validation details
    status.validation_details = {
        'total_traces': total_traces,
        'valid_traces': valid_traces,
        'empty_traces': empty_traces,
        'zero_traces': zero_traces,
        'nan_traces': nan_traces,
        'short_traces': short_traces,
        'min_trace_length': min_length,
        'max_trace_length': max_length,
        'avg_trace_length': avg_length
    }

    # Determine validation result
    valid_ratio = valid_traces / total_traces if total_traces > 0 else 0

    if valid_ratio >= 0.95:  # 95% valid traces required
        status.set_valid()
    elif valid_ratio >= 0.8:  # 80-95% - warning but proceed
        status.add_warning(f"Only {valid_ratio:.1%} of traces are fully valid")
        status.set_valid()
    else:
        status.add_error(f"Insufficient valid traces ({valid_ratio:.1%})")

    return status

def check_trace_consistency(loaded_trace_data: List[Dict]) -> DataValidationStatus:
    """
    Check consistency of trace data (lengths, sampling, etc.).

    Args:
        loaded_trace_data: List of loaded trace data dictionaries

    Returns:
        DataValidationStatus: Consistency validation results
    """
    status = DataValidationStatus()

    if not loaded_trace_data:
        status.add_error("No trace data to check consistency.")
        return status

    trace_lengths = []
    trace_dtypes = []

    for trace_data in loaded_trace_data:
        trace_sample = trace_data.get('trace_sample')

        if trace_sample is not None and isinstance(trace_sample, np.ndarray):
            trace_lengths.append(len(trace_sample))
            trace_dtypes.append(str(trace_sample.dtype))

    if not trace_lengths:
        status.add_error("No valid traces found for consistency check.")
        return status

    # Check length consistency
    unique_lengths = set(trace_lengths)
    if len(unique_lengths) > 1:
        status.add_warning(f"Inconsistent trace lengths: {sorted(unique_lengths)}")

    # Check dtype consistency
    unique_dtypes = set(trace_dtypes)
    if len(unique_dtypes) > 1:
        status.add_warning(f"Inconsistent data types: {unique_dtypes}")

    # Store consistency details
    status.validation_details = {
        'unique_lengths': sorted(unique_lengths),
        'unique_dtypes': list(unique_dtypes),
        'most_common_length': max(set(trace_lengths), key=trace_lengths.count),
        'length_consistency': len(unique_lengths) == 1,
        'dtype_consistency': len(unique_dtypes) == 1
    }

    # Determine if consistency is acceptable
    if len(unique_lengths) == 1 and len(unique_dtypes) == 1:
        status.set_valid()
    elif len(unique_lengths) <= 2 and len(unique_dtypes) == 1:
        # Minor length variations acceptable if same dtype
        status.add_warning("Minor trace length variations detected")
        status.set_valid()
    else:
        status.add_error("Significant trace inconsistencies detected")

    return status

def validate_seismic_data_range(loaded_trace_data: List[Dict]) -> DataValidationStatus:
    """
    Validate that seismic data values are within reasonable ranges.

    Args:
        loaded_trace_data: List of loaded trace data dictionaries

    Returns:
        DataValidationStatus: Range validation results
    """
    status = DataValidationStatus()

    if not loaded_trace_data:
        status.add_error("No trace data for range validation.")
        return status

    all_values = []
    trace_ranges = []

    for trace_data in loaded_trace_data:
        trace_sample = trace_data.get('trace_sample')

        if trace_sample is not None and isinstance(trace_sample, np.ndarray) and trace_sample.size > 0:
            # Remove NaN values for statistics
            valid_values = trace_sample[~np.isnan(trace_sample)]

            if len(valid_values) > 0:
                all_values.extend(valid_values)
                trace_ranges.append((np.min(valid_values), np.max(valid_values)))

    if not all_values:
        status.add_error("No valid seismic values found for range validation.")
        return status

    # Calculate overall statistics
    all_values = np.array(all_values)
    min_val = np.min(all_values)
    max_val = np.max(all_values)
    mean_val = np.mean(all_values)
    std_val = np.std(all_values)

    # Check for reasonable seismic data ranges
    # Typical seismic data should not have extreme values
    reasonable_range = abs(max_val - min_val) < 1e10  # Very large range check
    reasonable_std = std_val < 1e6  # Very large standard deviation check

    # Store range details
    status.validation_details = {
        'min_value': float(min_val),
        'max_value': float(max_val),
        'mean_value': float(mean_val),
        'std_value': float(std_val),
        'value_range': float(max_val - min_val),
        'reasonable_range': reasonable_range,
        'reasonable_std': reasonable_std,
        'total_samples': len(all_values)
    }

    # Validation logic
    if reasonable_range and reasonable_std:
        status.set_valid()
    else:
        if not reasonable_range:
            status.add_warning(f"Very large data range: {min_val:.2e} to {max_val:.2e}")
        if not reasonable_std:
            status.add_warning(f"Very large standard deviation: {std_val:.2e}")

        # Still mark as valid unless values are completely unreasonable
        if abs(max_val) < 1e15 and abs(min_val) < 1e15:
            status.set_valid()
        else:
            status.add_error("Seismic data values are unreasonably large")

    return status
