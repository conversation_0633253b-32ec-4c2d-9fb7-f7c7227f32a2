import streamlit as st
import numpy as np
import pandas as pd
import os

def generate_aoi_export_filename(base_name, extension):
    """
    Generate export filename with AOI parameters.
    
    Args:
        base_name (str): Base name for the file
        extension (str): File extension without the dot
        
    Returns:
        str: Formatted filename with AOI parameters
    """
    if (hasattr(st.session_state, 'aoi_inline_min') and 
        st.session_state.aoi_inline_min is not None):
        
        aoi_params = (f"_IL{st.session_state.aoi_inline_min}-{st.session_state.aoi_inline_max}"
                      f"_XL{st.session_state.aoi_xline_min}-{st.session_state.aoi_xline_max}")
        return f"{base_name}{aoi_params}.{extension}"
    else:
        return f"{base_name}.{extension}"

def sort_trace_indices_by_header_loader(header_loader, indices):
    """Return indices sorted by inline then crossline using SegyHeaderLoader.
    
    Args:
        header_loader: SegyHeaderLoader object containing inlines, crosslines, unique_indices
        indices: Array or list of trace indices to sort
        
    Returns:
        numpy.ndarray: Trace indices sorted by inline then crossline
    """
    indices = np.array(indices)
    if len(indices) == 0:
        return indices
        
    # Get inline and crossline values for the selected indices
    mask = np.isin(header_loader.unique_indices, indices)
    inl = header_loader.inlines[mask]
    xl = header_loader.crosslines[mask]
    
    # Sort by inline then crossline
    order = np.lexsort((xl, inl))
    return indices[order]


def sort_trace_indices(headers_df):
    """Sort trace indices first by inline then by crossline.

    Args:
        headers_df (pandas.DataFrame): DataFrame with at least the columns
            'inline', 'crossline', and 'trace_idx'. Each row represents a
            unique trace.

    Returns:
        list[int]: Trace indices sorted lexicographically (inline, crossline).
    """
    if headers_df.empty:
        return []

    # Ensure required columns exist
    missing_cols = {col for col in ("inline", "crossline", "trace_idx") if col not in headers_df.columns}
    if missing_cols:
        raise ValueError(f"headers_df is missing required columns: {missing_cols}")

    return (
        headers_df.sort_values(["inline", "crossline"], kind="mergesort")
        ["trace_idx"].to_list()
    )


def select_export_attributes(exportable_attrs, first_descriptor, sample_length):
    """
    Filters feasible attributes for export, matching logic from original implementation.
    Returns a list of exportable attribute names.
    
    Args:
        exportable_attrs: List of desired attribute names to export
        first_descriptor: Dictionary containing spectral descriptors for the first trace
        sample_length: Expected length of each attribute array
        
    Returns:
        list: List of attribute names that can be exported
    """
    # Filter exportable attributes
    valid_attrs = [
        key for key in exportable_attrs
        if key in first_descriptor and isinstance(first_descriptor[key], np.ndarray) and len(first_descriptor[key]) == sample_length
    ]
    
    # Optionally add WOSS if possible
    if (
        'hfc' in first_descriptor and
        'norm_fdom' in first_descriptor and
        'mag_voice_slope' in first_descriptor and
        'WOSS' not in valid_attrs and
        'WOSS' in exportable_attrs
    ):
        valid_attrs.append('WOSS')
    
    return valid_attrs

def export_analysis_results_numpy(results_dict, output_dir, format_type='numpy', base_filename='spectral_analysis'):
    """
    Export spectral analysis results to NumPy format files.
    
    Args:
        results_dict: Dictionary containing attribute names as keys and NumPy arrays as values
        output_dir: Directory path for output files
        format_type: Export format ('numpy', 'hdf5', or 'npz')
        base_filename: Base name for output files
        
    Returns:
        list: List of created file paths
    """
    created_files = []
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        if format_type == 'numpy':
            # Export each attribute as separate .npy files
            for attr_name, attr_data in results_dict.items():
                if isinstance(attr_data, np.ndarray):
                    output_path = os.path.join(output_dir, f"{attr_name}.npy")
                    np.save(output_path, attr_data)
                    created_files.append(output_path)
                    print(f"Exported {attr_name} to {output_path} with shape {attr_data.shape}")
        
        elif format_type == 'npz':
            # Export all attributes in single compressed NumPy file
            output_path = os.path.join(output_dir, f"{base_filename}.npz")
            np.savez_compressed(output_path, **results_dict)
            created_files.append(output_path)
            print(f"Exported all attributes to compressed NPZ file: {output_path}")
            
        elif format_type == 'hdf5':
            # Export all attributes in single HDF5 file
            try:
                import h5py
                output_path = os.path.join(output_dir, f"{base_filename}.h5")
                with h5py.File(output_path, 'w') as f:
                    for attr_name, attr_data in results_dict.items():
                        if isinstance(attr_data, np.ndarray):
                            f.create_dataset(attr_name, data=attr_data, compression='gzip', compression_opts=9)
                created_files.append(output_path)
                print(f"Exported all attributes to HDF5 file: {output_path}")
            except ImportError:
                raise ImportError("h5py is required for HDF5 export. Install with: pip install h5py")
        
        else:
            raise ValueError(f"Unsupported format_type: {format_type}")
            
        return created_files
        
    except Exception as e:
        raise RuntimeError(f"Error exporting analysis results: {e}")

def export_seismic_data_numpy(data_array, output_path, data_type='3D', metadata=None):
    """
    Export seismic data array to NumPy format with optional metadata.
    
    Args:
        data_array: NumPy array containing seismic data
        output_path: Path for output .npy file
        data_type: '2D' or '3D' data type specification
        metadata: Optional dictionary containing metadata to save alongside
        
    Returns:
        str: Path to created file
    """
    try:
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Save the main data array
        np.save(output_path, data_array)
        
        # Save metadata if provided
        if metadata is not None:
            metadata_path = output_path.replace('.npy', '_metadata.npz')
            np.savez(metadata_path, **metadata)
            print(f"Saved metadata to {metadata_path}")
        
        print(f"Exported {data_type} seismic data to {output_path} with shape {data_array.shape}")
        return output_path
        
    except Exception as e:
        raise RuntimeError(f"Error exporting seismic data to NumPy format: {e}")

def batch_export_attributes_numpy(trace_indices, descriptors, selected_attrs, output_dir, format_type='numpy'):
    """
    Export multiple spectral attributes for selected traces to NumPy format.
    
    Args:
        trace_indices: List of trace indices to export
        descriptors: List of descriptor dictionaries (one per trace)
        selected_attrs: List of attribute names to export
        output_dir: Directory for output files
        format_type: Export format ('numpy', 'npz', 'hdf5')
        
    Returns:
        list: List of created file paths
    """
    try:
        if not trace_indices or not descriptors or not selected_attrs:
            raise ValueError("Empty trace_indices, descriptors, or selected_attrs provided")
        
        # Organize data by attribute
        results_dict = {}
        
        for attr in selected_attrs:
            attr_data_list = []
            
            for trace_idx in trace_indices:
                # Find descriptor for this trace
                descriptor = descriptors[trace_idx] if trace_idx < len(descriptors) else None
                
                if descriptor and attr in descriptor:
                    attr_data_list.append(descriptor[attr])
                else:
                    # Handle missing data with zeros or skip
                    print(f"Warning: Missing {attr} data for trace {trace_idx}")
                    
            if attr_data_list:
                # Stack all trace data for this attribute
                results_dict[attr] = np.array(attr_data_list)
        
        # Export using the general function
        return export_analysis_results_numpy(results_dict, output_dir, format_type)
        
    except Exception as e:
        raise RuntimeError(f"Error in batch export: {e}")

def create_export_summary_numpy(created_files, output_dir, data_format_type, analysis_params=None):
    """
    Create a summary file describing the exported NumPy data.
    
    Args:
        created_files: List of file paths that were created
        output_dir: Output directory
        data_format_type: Original data format ("SEG-Y" or "NumPy")
        analysis_params: Optional dictionary of analysis parameters
        
    Returns:
        str: Path to summary file
    """
    try:
        summary_path = os.path.join(output_dir, 'export_summary.txt')
        
        with open(summary_path, 'w') as f:
            f.write("WOSS Seismic Analysis Tool - Export Summary\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"Original Data Format: {data_format_type}\n")
            f.write(f"Export Format: NumPy (.npy)\n")
            f.write(f"Export Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("Exported Files:\n")
            f.write("-" * 20 + "\n")
            
            for file_path in created_files:
                filename = os.path.basename(file_path)
                
                if file_path.endswith('.npy'):
                    # Get array info for .npy files
                    try:
                        arr = np.load(file_path)
                        f.write(f"{filename}: shape {arr.shape}, dtype {arr.dtype}\n")
                    except:
                        f.write(f"{filename}: NumPy array file\n")
                else:
                    f.write(f"{filename}: {os.path.splitext(filename)[1]} file\n")
            
            if analysis_params:
                f.write("\nAnalysis Parameters:\n")
                f.write("-" * 20 + "\n")
                for key, value in analysis_params.items():
                    f.write(f"{key}: {value}\n")
            
            f.write("\nData Format Notes:\n")
            f.write("-" * 20 + "\n")
            f.write("- NumPy arrays can be loaded with: np.load('filename.npy')\n")
            f.write("- Arrays are saved in float32 format for GPU compatibility\n")
            f.write("- Coordinate information is preserved in synthetic coordinate system\n")
            
        return summary_path
        
    except Exception as e:
        print(f"Warning: Could not create export summary: {e}")
        return None
