# -*- coding: utf-8 -*-
"""
GPU utility functions for the WOSS Seismic Analysis Tool.

This module provides utility functions for checking GPU availability
and handling GPU-related operations.
"""

import logging
import numpy as np

def check_gpu_availability():
    """
    Check if GPU is available and working.

    This function uses the centralized GPU manager to check GPU availability.

    Returns:
        bool: True if GPU is available and working, False otherwise
    """
    from utils.gpu_manager import is_gpu_available
    return is_gpu_available()

def get_gpu_info():
    """
    Get information about the available GPU(s).

    Returns:
        dict: Dictionary containing GPU information, or None if GPU is not available
    """
    from utils.gpu_manager import get_gpu_info as gpu_manager_get_gpu_info
    return gpu_manager_get_gpu_info()

def log_gpu_info():
    """
    Log information about the available GPU(s).
    """
    # Only log GPU info if GPU is available, don't generate warnings
    if check_gpu_availability():
        gpu_info = get_gpu_info()
        if gpu_info:
            logging.info(f"Found {gpu_info['device_count']} GPU device(s):")
            for i, device in enumerate(gpu_info['devices']):
                logging.info(f"  Device {i}: {device['name']} (Compute {device['compute_capability']})")
                logging.info(f"    Memory: {device['free_memory_mb']:.2f}MB free / {device['total_memory_mb']:.2f}MB total")
        else:
            logging.info("GPU is available but could not retrieve detailed information.")
    else:
        # Don't log anything if GPU is not available - this is expected
        pass

def process_traces_gpu(traces_array, dt, batch_size, descriptor_settings,
                      outputs_to_calculate, progress_callback=None):
    """
    Centralized GPU processing function for trace descriptors.

    Args:
        traces_array: 2D numpy array of traces (traces x samples)
        dt: Sampling interval
        batch_size: GPU batch size
        descriptor_settings: Dictionary of descriptor parameters
        outputs_to_calculate: List of outputs to calculate
        progress_callback: Optional callback for progress updates

    Returns:
        Dictionary of calculated descriptors
    """
    try:
        # Import GPU functions
        from utils.dlogst_spec_descriptor_gpu import (
            dlogst_spec_descriptor_gpu_2d_chunked,
            dlogst_spec_descriptor_gpu_2d_chunked_mag
        )
        from utils.processing import calculate_woss

        # Filter params using whitelist approach for GPU functions
        # Valid parameters for dlogst_spec_descriptor_gpu functions:
        valid_gpu_params = {
            'fmax', 'shape', 'kmax', 'int_val', 'b1', 'b2',
            'p_bandwidth', 'roll_percent', 'batch_size', 'use_band_limited'
        }

        filtered_params = {
            k: v for k, v in descriptor_settings.items()
            if k in valid_gpu_params
        }

        # Ensure required components for WOSS
        if "WOSS" in outputs_to_calculate:
            required_for_woss = ["hfc", "norm_fdom", "mag_voice_slope"]
            for comp in required_for_woss:
                if comp not in outputs_to_calculate:
                    outputs_to_calculate.append(comp)

        # Call appropriate GPU function
        if any(out in ['mag', 'mag_voice'] for out in outputs_to_calculate):
            # Use magnitude version if needed
            all_descriptors = dlogst_spec_descriptor_gpu_2d_chunked_mag(
                traces_array,
                dt,
                batch_size=batch_size,
                **filtered_params
            )
        else:
            # Use standard version
            all_descriptors = dlogst_spec_descriptor_gpu_2d_chunked(
                traces_array,
                dt,
                batch_size=batch_size,
                **filtered_params
            )

        # Filter to requested outputs
        calculated_descriptors = {
            key: all_descriptors[key]
            for key in outputs_to_calculate
            if key in all_descriptors
        }

        # Calculate WOSS if requested
        if "WOSS" in outputs_to_calculate and all(
            comp in calculated_descriptors
            for comp in ["hfc", "norm_fdom", "mag_voice_slope"]
        ):
            # Create WOSS parameters from descriptor_settings
            woss_params = {
                'hfc_p95': descriptor_settings.get('hfc_p95', 1.0),
                'epsilon': descriptor_settings.get('epsilon', 1e-4),
                'fdom_exponent': descriptor_settings.get('fdom_exponent', 2.0)
            }

            # Calculate WOSS for each trace
            woss_array = np.zeros_like(calculated_descriptors["hfc"])
            for i in range(traces_array.shape[0]):
                trace_components = {
                    'hfc': calculated_descriptors["hfc"][i],
                    'norm_fdom': calculated_descriptors["norm_fdom"][i],
                    'mag_voice_slope': calculated_descriptors["mag_voice_slope"][i]
                }
                woss_array[i] = calculate_woss(trace_components, woss_params)

            calculated_descriptors["WOSS"] = woss_array

        return calculated_descriptors

    except Exception as e:
        logging.error(f"GPU processing error: {e}", exc_info=True)
        raise
