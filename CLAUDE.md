# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Running the Application
```bash
# Activate virtual environment (Windows)
& 'C:\Users\<USER>\codellm\Scripts\activate.ps1'

# Run the Streamlit application
streamlit run app.py
```

### Testing
```bash
# Run basic polyline functionality tests
python tests/test_polyline_basic.py
```

### Environment Setup
- Python 3.10.9 with virtual environment at `C:\Users\<USER>\codellm`
- Streamlit 1.45.0 installed in virtual environment
- GPU acceleration requires CUDA-compatible GPU and CuPy (`pip install cupy-cuda11x` or `cupy-cuda12x`)

## Application Architecture

### 5-Step Workflow Pipeline
The WOSS Seismic Analysis Tool follows a structured 5-step workflow:

1. **Load Data** (`pages/load_data_page.py`) - Import SEG-Y files and well marker data
2. **Configure Display** (`pages/configure_display_page.py`) - Set visualization and processing parameters
3. **Select Area** (`pages/select_area_page.py`) - Choose analysis area (inline/crossline, polyline, AOI, well markers)
4. **Analyze Data** (`pages/analyze_data_page.py`) - Perform GPU-accelerated spectral analysis
5. **Export Results** (`pages/export_results_page.py`) - View results and export processed data

### Core Architecture Components

**Main Router (`app.py`)**
- Central application entry point and workflow orchestrator
- Manages sidebar navigation and step transitions
- Handles GPU availability detection with lazy loading
- Implements prerequisite validation for each workflow step
- Session state management and error handling

**Common Modules (`common/`)**
- `constants.py` - Application constants, output types, descriptor limits
- `session_state.py` - Centralized session state initialization and reset
- `ui_elements.py` - Reusable UI components and widgets
- `validation_ui.py` - Data validation UI components

**Utility Modules (`utils/`)**
- `data_utils.py` - SEG-Y loading, well data import, format conversion
- `dlogst_spec_descriptor_gpu.py` - GPU-accelerated spectral descriptor computation (float32 optimized)
- `dlogst_spec_descriptor_cpu.py` - CPU fallback for spectral analysis
- `gpu_manager.py` - GPU availability detection and management
- `processing.py` - Core spectral analysis algorithms
- `visualization.py` - Advanced plotting and data visualization
- `export_utils.py` - Data export in multiple formats

### GPU Processing Architecture
- **Primary**: CuPy-based GPU acceleration for spectral descriptor calculations
- **Fallback**: CPU implementations when GPU unavailable
- **Lazy Loading**: GPU functions imported only when needed
- **Error Handling**: Graceful degradation with informative error messages

### Spectral Descriptors Computed
- Spectral Slope, Bandwidth, Rolloff
- High Frequency Content (HFC)
- Magnitude*Voice Slope
- Spectral Decrease
- **WOSS (Weighted Optimum Spectral Shape)** - Primary analysis output

## Key Implementation Patterns

### Session State Management
The application uses comprehensive session state management through `common/session_state.py` with state persistence across workflow steps and proper cleanup via `reset_state()`.

### Modular Page Structure
Each page module follows a consistent pattern:
- `render()` function as main entry point
- Import utilities from `utils/` modules
- Use common UI elements from `common/`
- Proper error handling and user feedback

### GPU Function Loading
```python
# GPU functions are lazily loaded in app.py
def get_gpu_functions():
    # Only imports GPU functions when actually needed
    # Falls back to error functions if GPU unavailable
```

### Analysis Mode Support
- **Inline/Crossline**: Specific trace range analysis
- **Polyline**: Custom path analysis with file import
- **AOI (Area of Interest)**: Polygon-based selection with batch processing
- **Well Markers**: Analysis around well locations with multi-selection

## Configuration Files

### Streamlit Configuration (`.streamlit/config.toml`)
```toml
[client]
showSidebarNavigation = false    # Custom navigation via app.py

[server]
maxUploadSize = 8000            # 8GB upload limit for large SEG-Y files
```

## Data Flow Architecture

### Input Processing
1. SEG-Y file upload with configurable header bytes (inline: 189, crossline: 193)
2. Optional well marker data (Excel format)
3. Coordinate scaling options (byte-based or custom scalar)

### Analysis Pipeline
1. Data validation and header loading
2. Area selection and trace filtering
3. GPU-accelerated spectral descriptor computation
4. Results visualization and statistical analysis
5. Export to multiple formats (SEG-Y, NumPy, reports)

### Session State Variables
Key session state variables for workflow management:
- `current_step` - Current workflow step
- `segy_file_info` - Loaded SEG-Y file information
- `display_params_configured` - Display configuration status
- `area_selected` - Area selection completion status
- `analysis_complete` - Analysis completion status
- `GPU_AVAILABLE` - GPU availability flag

## File Organization

### Active Pipeline Files
- Core application and page modules (fully documented in `codebase_readme.md`)
- Configuration files (`.streamlit/config.toml`)
- Test files (`tests/test_polyline_basic.py`)

### Excluded Directories (`.gitignore`)
- `archive/` - Legacy implementations
- `backup_tk/` - Backup files
- `initial_app/` - Reference implementation
- `docs/` - Documentation and planning files

### Documentation Files (Root Level)
Implementation documentation files provide valuable context for development history and planned improvements.

## Development Guidelines

### Working with SEG-Y Data
- Use `SegyHeaderLoader` from `utils/data_utils.py` for header loading
- Implement proper temporary file handling for uploads
- Support both 2D and 3D seismic data formats

### GPU Development
- Always check `GPU_AVAILABLE` flag before using GPU functions
- Implement CPU fallbacks for all GPU operations
- Use float32 precision for optimal GPU performance
- Handle CuPy import errors gracefully

### UI Development
- Use common UI elements from `common/ui_elements.py`
- Implement proper session state management
- Provide clear user feedback and error messages
- Follow the established 5-step workflow pattern

### Testing
- Run `python tests/test_polyline_basic.py` for polyline functionality verification
- Test GPU/CPU fallback scenarios
- Validate workflow step transitions and prerequisite checks