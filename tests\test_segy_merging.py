"""
Test script for SEG-Y file merging functionality.

This script tests the enhanced SEG-Y merging feature for AOI exports.
"""

import os
import sys
import tempfile
import shutil
import numpy as np
import segyio
import logging

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.data_utils import merge_segy_batch_files

def create_test_segy_file(file_path, num_traces=10, num_samples=100):
    """Create a test SEG-Y file with specified number of traces and samples."""
    
    # Create a spec for the test file
    spec = segyio.spec()
    spec.format = 1  # IBM float
    spec.samples = range(num_samples)
    spec.tracecount = num_traces
    
    # Create the file
    with segyio.create(file_path, spec) as dst:
        # Set binary header
        dst.bin[segyio.BinField.Samples] = num_samples
        dst.bin[segyio.BinField.Interval] = 4000  # 4ms sample interval
        
        # Write traces with synthetic data
        for i in range(num_traces):
            # Create synthetic trace data (sine wave with different frequencies)
            t = np.arange(num_samples) * 0.004  # Time in seconds
            frequency = 10 + i * 2  # Different frequency for each trace
            trace_data = np.sin(2 * np.pi * frequency * t)
            
            # Set trace header
            dst.header[i] = {
                segyio.TraceField.INLINE_3D: 100 + i,
                segyio.TraceField.CROSSLINE_3D: 200 + i,
                segyio.TraceField.TraceNumber: i + 1
            }
            
            # Write trace data
            dst.trace[i] = trace_data

def test_merge_single_file():
    """Test merging with a single file (should just copy)."""
    print("Testing single file merge...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create a test file
        test_file = os.path.join(temp_dir, "test_single.sgy")
        create_test_segy_file(test_file, num_traces=5)
        
        # Merge (should just copy)
        output_file = os.path.join(temp_dir, "merged_single.sgy")
        result = merge_segy_batch_files([test_file], output_file)
        
        # Verify result
        assert os.path.exists(output_file), "Output file should exist"
        assert "copied" in result.lower(), f"Expected copy operation, got: {result}"
        
        # Verify file contents
        with segyio.open(test_file, 'r', ignore_geometry=True) as src:
            with segyio.open(output_file, 'r', ignore_geometry=True) as dst:
                assert src.tracecount == dst.tracecount, "Trace counts should match"
                assert len(src.samples) == len(dst.samples), "Sample counts should match"
        
        print("✓ Single file merge test passed")

def test_merge_multiple_files():
    """Test merging multiple files."""
    print("Testing multiple file merge...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create multiple test files
        batch_files = []
        total_traces = 0
        
        for i in range(3):
            test_file = os.path.join(temp_dir, f"batch_{i}.sgy")
            num_traces = 5 + i * 2  # Different number of traces per file
            create_test_segy_file(test_file, num_traces=num_traces)
            batch_files.append(test_file)
            total_traces += num_traces
        
        # Merge files
        output_file = os.path.join(temp_dir, "merged_multiple.sgy")
        result = merge_segy_batch_files(batch_files, output_file)
        
        # Verify result
        assert os.path.exists(output_file), "Output file should exist"
        assert "successfully merged" in result.lower(), f"Expected successful merge, got: {result}"
        
        # Verify merged file contents
        with segyio.open(output_file, 'r', ignore_geometry=True) as merged:
            assert merged.tracecount == total_traces, f"Expected {total_traces} traces, got {merged.tracecount}"
            
            # Verify trace data integrity
            trace_idx = 0
            for batch_file in batch_files:
                with segyio.open(batch_file, 'r', ignore_geometry=True) as batch:
                    for i in range(batch.tracecount):
                        # Compare trace data
                        original_trace = batch.trace[i]
                        merged_trace = merged.trace[trace_idx]
                        np.testing.assert_array_almost_equal(
                            original_trace, merged_trace, 
                            err_msg=f"Trace {trace_idx} data mismatch"
                        )
                        trace_idx += 1
        
        print("✓ Multiple file merge test passed")

def test_merge_error_handling():
    """Test error handling in merge function."""
    print("Testing error handling...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Test with empty file list
        result = merge_segy_batch_files([], "dummy_output.sgy")
        assert "no batch files" in result.lower(), f"Expected error message, got: {result}"
        
        # Test with non-existent files
        non_existent_files = [os.path.join(temp_dir, "non_existent.sgy")]
        output_file = os.path.join(temp_dir, "output.sgy")
        result = merge_segy_batch_files(non_existent_files, output_file)
        assert "error" in result.lower(), f"Expected error message, got: {result}"
        
        print("✓ Error handling test passed")

def main():
    """Run all tests."""
    print("Starting SEG-Y merging tests...")
    
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    try:
        test_merge_single_file()
        test_merge_multiple_files()
        test_merge_error_handling()
        
        print("\n🎉 All tests passed successfully!")
        print("SEG-Y merging functionality is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
