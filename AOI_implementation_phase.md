# WOSS Streamlit Application - AOI Implementation Phase Guide

## Overview
This document provides direct code-based implementation steps for building the WOSS Streamlit application with focus on AOI (Area of Interest) functionality. Each phase includes specific code examples and objective checklists.

## Phase 1: Foundation and Data Loading

### Objective Checklist
- [ ] Complete SEG-Y file upload and header processing
- [ ] Implement well data loading with Excel support
- [ ] Add basemap generation capability
- [ ] Implement data validation and error handling
- [ ] Add progress indicators and user feedback

### Step 1.1: Enhance Load Data Page

**File: `pages/load_data_page.py`**

```python
import streamlit as st
import pandas as pd
import numpy as np
import tempfile
import os
from utils.data_utils import SegyHeaderLoader, load_excel_data
from utils.visualization import plot_basemap_with_wells

def render():
    """Enhanced load data page with complete functionality."""
    st.header("Step 1: Load Data")
    
    # SEG-Y File Upload Section
    st.subheader("1.1 SEG-Y File Upload")
    segy_file = st.file_uploader("Choose a SEG-Y file", type=["sgy", "segy"])
    
    if segy_file is not None:
        # Save to temporary file for processing
        with tempfile.NamedTemporaryFile(delete=False, suffix='.sgy') as tmp_file:
            tmp_file.write(segy_file.read())
            st.session_state.segy_temp_file_path = tmp_file.name
        
        # Process headers with progress indicator
        with st.spinner("Loading SEG-Y headers..."):
            try:
                header_loader = SegyHeaderLoader(st.session_state.segy_temp_file_path)
                headers_df = header_loader.load_headers()
                
                # Store in session state
                st.session_state.header_loader = header_loader
                st.session_state.headers_df = headers_df
                st.session_state.segy_file_info = {
                    'name': segy_file.name,
                    'trace_count': len(headers_df),
                    'inline_range': (headers_df['inline'].min(), headers_df['inline'].max()),
                    'crossline_range': (headers_df['crossline'].min(), headers_df['crossline'].max())
                }
                
                st.success(f"✅ Loaded {len(headers_df)} traces from {segy_file.name}")
                
                # Display basic information
                col1, col2 = st.columns(2)
                with col1:
                    st.metric("Total Traces", len(headers_df))
                    st.metric("Inline Range", f"{headers_df['inline'].min()} - {headers_df['inline'].max()}")
                with col2:
                    st.metric("Sampling Rate", f"{header_loader.dt} ms")
                    st.metric("Crossline Range", f"{headers_df['crossline'].min()} - {headers_df['crossline'].max()}")
                
            except Exception as e:
                st.error(f"❌ Error loading SEG-Y file: {str(e)}")
                return
    
    # Well Data Upload Section
    st.subheader("1.2 Well Data Upload (Optional)")
    use_wells = st.checkbox("Include Well Data", value=st.session_state.get('use_wells', False))
    st.session_state.use_wells = use_wells
    
    if use_wells:
        well_file = st.file_uploader("Choose an Excel file with well data", type=["xls", "xlsx"])
        
        if well_file is not None:
            try:
                well_df = load_excel_data(well_file)
                st.session_state.well_df = well_df
                st.session_state.well_file_info = {'name': well_file.name}
                
                st.success(f"✅ Loaded {len(well_df)} well markers")
                st.dataframe(well_df.head())
                
            except Exception as e:
                st.error(f"❌ Error loading well data: {str(e)}")
    
    # Basemap Generation
    if st.session_state.get('segy_file_info') and st.button("Generate Survey Basemap"):
        with st.spinner("Generating basemap..."):
            try:
                fig = plot_basemap_with_wells(
                    st.session_state.headers_df,
                    st.session_state.get('well_df')
                )
                st.session_state.basemap_figure = fig
                st.plotly_chart(fig, use_container_width=True)
                
            except Exception as e:
                st.error(f"❌ Error generating basemap: {str(e)}")
    
    # Navigation
    if st.session_state.get('segy_file_info'):
        if st.button("✅ Continue to Configure Display", type="primary"):
            st.session_state.current_step = "configure_display"
            st.rerun()
```

### Step 1.2: Enhance Data Utils

**File: `utils/data_utils.py`**

```python
import segyio
import pandas as pd
import numpy as np

class SegyHeaderLoader:
    """Enhanced SEG-Y header loader with comprehensive functionality."""
    
    def __init__(self, file_path):
        self.file_path = file_path
        self.dt = None
        self.trace_count = None
    
    def load_headers(self):
        """Load SEG-Y headers and return as DataFrame."""
        headers_list = []
        
        with segyio.open(self.file_path, 'r', ignore_geometry=True) as segy:
            self.dt = segy.bin[segyio.BinField.Interval] / 1000.0  # Convert to ms
            self.trace_count = len(segy.trace)
            
            for i, header in enumerate(segy.header):
                headers_list.append({
                    'trace_idx': i,
                    'inline': header[segyio.TraceField.INLINE_3D],
                    'crossline': header[segyio.TraceField.CROSSLINE_3D],
                    'x_coord': header[segyio.TraceField.SourceX],
                    'y_coord': header[segyio.TraceField.SourceY],
                    'elevation': header[segyio.TraceField.ReceiverGroupElevation]
                })
        
        return pd.DataFrame(headers_list)
    
    def get_trace_data(self, trace_indices):
        """Load specific traces by index."""
        traces = []
        with segyio.open(self.file_path, 'r', ignore_geometry=True) as segy:
            for idx in trace_indices:
                traces.append(segy.trace[idx])
        return np.array(traces)

def load_excel_data(file_buffer):
    """Load and validate well data from Excel file."""
    try:
        # Try different sheet names
        df = pd.read_excel(file_buffer, sheet_name=0)
        
        # Validate required columns
        required_cols = ['Well', 'Surface', 'X', 'Y']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Clean and validate data
        df = df.dropna(subset=required_cols)
        df['X'] = pd.to_numeric(df['X'], errors='coerce')
        df['Y'] = pd.to_numeric(df['Y'], errors='coerce')
        
        return df.dropna()
        
    except Exception as e:
        raise Exception(f"Error processing Excel file: {str(e)}")
```

---

## Phase 2: Area Selection Implementation (Critical - AOI Focus)

### Objective Checklist
- [ ] Implement AOI boundary input interface
- [ ] Add AOI data processing and validation
- [ ] Implement inline/crossline selection modes
- [ ] Add well marker selection with multi-select
- [ ] Implement polyline selection capability
- [ ] Add comprehensive validation and error handling

### Step 2.1: AOI Implementation (Critical)

**File: `pages/select_area_page.py`**

```python
import streamlit as st
import pandas as pd
import numpy as np
from utils.data_validation import validate_aoi_boundaries
from common.validation_ui import show_selection_summary

def render():
    """Enhanced area selection page with complete AOI functionality."""
    st.header("Step 3: Select Analysis Area")

    # Prerequisites check
    if not st.session_state.get('segy_file_info'):
        st.error("❌ Please load SEG-Y data first")
        return

    # Selection mode options
    selection_modes = [
        "Single inline",
        "Single crossline",
        "By inline/crossline section (AOI)",
        "By polyline",
        "By well markers"
    ]

    st.session_state.selection_mode = st.selectbox(
        "Choose analysis mode:",
        selection_modes,
        index=selection_modes.index(st.session_state.get('selection_mode', selection_modes[0]))
    )

    # Get data bounds for validation
    headers_df = st.session_state.headers_df
    actual_inline_min = headers_df['inline'].min()
    actual_inline_max = headers_df['inline'].max()
    actual_xline_min = headers_df['crossline'].min()
    actual_xline_max = headers_df['crossline'].max()

    # AOI Implementation (Critical Section)
    if st.session_state.selection_mode == "By inline/crossline section (AOI)":
        st.subheader("🎯 AOI (Area of Interest) Selection")

        # Initialize AOI boundaries if not set
        if st.session_state.aoi_inline_min is None:
            st.session_state.aoi_inline_min = actual_inline_min
        if st.session_state.aoi_inline_max is None:
            st.session_state.aoi_inline_max = actual_inline_max
        if st.session_state.aoi_xline_min is None:
            st.session_state.aoi_xline_min = actual_xline_min
        if st.session_state.aoi_xline_max is None:
            st.session_state.aoi_xline_max = actual_xline_max

        # AOI boundary input interface
        st.write("**Define AOI Boundaries:**")
        col1, col2 = st.columns(2)

        with col1:
            st.write("**Inline Range:**")
            st.session_state.aoi_inline_min = st.number_input(
                f"Inline Min (Available: {actual_inline_min}-{actual_inline_max})",
                min_value=int(actual_inline_min),
                max_value=int(actual_inline_max),
                value=int(st.session_state.aoi_inline_min),
                key="aoi_inline_min_input"
            )

            st.session_state.aoi_inline_max = st.number_input(
                f"Inline Max (Available: {actual_inline_min}-{actual_inline_max})",
                min_value=int(actual_inline_min),
                max_value=int(actual_inline_max),
                value=int(st.session_state.aoi_inline_max),
                key="aoi_inline_max_input"
            )

        with col2:
            st.write("**Crossline Range:**")
            st.session_state.aoi_xline_min = st.number_input(
                f"Crossline Min (Available: {actual_xline_min}-{actual_xline_max})",
                min_value=int(actual_xline_min),
                max_value=int(actual_xline_max),
                value=int(st.session_state.aoi_xline_min),
                key="aoi_xline_min_input"
            )

            st.session_state.aoi_xline_max = st.number_input(
                f"Crossline Max (Available: {actual_xline_min}-{actual_xline_max})",
                min_value=int(actual_xline_min),
                max_value=int(actual_xline_max),
                value=int(st.session_state.aoi_xline_max),
                key="aoi_xline_max_input"
            )

        # AOI validation and processing
        if st.button("🔍 Validate AOI Selection", type="secondary"):
            validation_result = validate_aoi_selection()
            if validation_result['valid']:
                st.success(f"✅ AOI validated: {validation_result['trace_count']} traces selected")
                show_aoi_summary()
            else:
                st.error(f"❌ AOI validation failed: {validation_result['error']}")

        # Processing option (Fixed for now)
        st.info("**Processing Option:** Full AOI")
        st.session_state.aoi_processing_option = "Full AOI"

        # Proceed button for AOI
        if st.button("✅ Proceed with AOI Selection", type="primary"):
            if process_aoi_selection():
                st.session_state.current_step = "analyze_data"
                st.rerun()

def validate_aoi_selection():
    """Validate AOI boundaries and return validation result."""
    try:
        headers_df = st.session_state.headers_df

        # Check boundary validity
        if st.session_state.aoi_inline_min > st.session_state.aoi_inline_max:
            return {'valid': False, 'error': 'Inline min cannot be greater than inline max'}

        if st.session_state.aoi_xline_min > st.session_state.aoi_xline_max:
            return {'valid': False, 'error': 'Crossline min cannot be greater than crossline max'}

        # Filter traces within AOI
        aoi_df = headers_df[
            (headers_df['inline'] >= st.session_state.aoi_inline_min) &
            (headers_df['inline'] <= st.session_state.aoi_inline_max) &
            (headers_df['crossline'] >= st.session_state.aoi_xline_min) &
            (headers_df['crossline'] <= st.session_state.aoi_xline_max)
        ]

        if aoi_df.empty:
            return {'valid': False, 'error': 'No traces found within specified AOI boundaries'}

        return {
            'valid': True,
            'trace_count': len(aoi_df),
            'aoi_df': aoi_df
        }

    except Exception as e:
        return {'valid': False, 'error': f'Validation error: {str(e)}'}

def show_aoi_summary():
    """Display AOI selection summary."""
    st.write("**AOI Summary:**")
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Inline Range",
                 f"{st.session_state.aoi_inline_min} - {st.session_state.aoi_inline_max}")
    with col2:
        st.metric("Crossline Range",
                 f"{st.session_state.aoi_xline_min} - {st.session_state.aoi_xline_max}")
    with col3:
        inline_count = st.session_state.aoi_inline_max - st.session_state.aoi_inline_min + 1
        xline_count = st.session_state.aoi_xline_max - st.session_state.aoi_xline_min + 1
        st.metric("AOI Size", f"{inline_count} × {xline_count}")

def process_aoi_selection():
    """Process AOI selection and store results."""
    try:
        validation_result = validate_aoi_selection()

        if not validation_result['valid']:
            st.error(validation_result['error'])
            return False

        # Store AOI selection data
        aoi_df = validation_result['aoi_df']
        st.session_state.selected_indices = aoi_df['trace_idx'].tolist()
        st.session_state.area_selected = True
        st.session_state.area_selected_mode = 'aoi'
        st.session_state.area_selected_details = {
            'type': 'aoi',
            'trace_count': len(aoi_df),
            'boundaries': {
                'inline_min': st.session_state.aoi_inline_min,
                'inline_max': st.session_state.aoi_inline_max,
                'xline_min': st.session_state.aoi_xline_min,
                'xline_max': st.session_state.aoi_xline_max
            }
        }

        st.success(f"✅ AOI selection complete: {len(aoi_df)} traces selected")
        return True

    except Exception as e:
        st.error(f"❌ Error processing AOI selection: {str(e)}")
        return False
```

### Step 2.2: Other Selection Modes Implementation

**Continue in `pages/select_area_page.py`**

```python
    # Single Inline Selection
    elif st.session_state.selection_mode == "Single inline":
        st.subheader("📏 Single Inline Selection")

        available_inlines = sorted(headers_df['inline'].unique())
        selected_inline = st.selectbox(
            "Select inline:",
            available_inlines,
            index=0 if st.session_state.selected_inline is None
            else available_inlines.index(st.session_state.selected_inline)
        )
        st.session_state.selected_inline = selected_inline

        if st.button("✅ Proceed with Inline Selection", type="primary"):
            inline_df = headers_df[headers_df['inline'] == selected_inline]
            st.session_state.selected_indices = inline_df['trace_idx'].tolist()
            st.session_state.area_selected = True
            st.session_state.area_selected_mode = 'inline'
            st.session_state.current_step = "analyze_data"
            st.rerun()

    # Well Marker Selection
    elif st.session_state.selection_mode == "By well markers":
        st.subheader("🎯 Well Marker Selection")

        if not st.session_state.get('well_df') is not None:
            st.error("❌ Please load well data first in Step 1")
            return

        well_df = st.session_state.well_df

        # Create well-marker pair options
        marker_options = []
        for _, row in well_df.iterrows():
            marker_options.append(f"{row['Well']} - {row['Surface']}")

        # Multi-select interface for well markers
        selected_markers = st.multiselect(
            "Select well-marker pair(s) for analysis:",
            marker_options,
            default=st.session_state.get('selected_well_markers', [])
        )
        st.session_state.selected_well_markers = selected_markers

        if selected_markers:
            st.write(f"**Selected {len(selected_markers)} well-marker pair(s):**")
            for marker in selected_markers:
                st.write(f"• {marker}")

            # Well analysis sub-options
            st.session_state.well_analysis_sub_option = st.radio(
                "Analysis approach:",
                ["Individual wells", "Grouped analysis"],
                index=0 if st.session_state.get('well_analysis_sub_option') == "Individual wells" else 1
            )

            if st.button("✅ Proceed with Well Selection", type="primary"):
                # Process selected well markers
                selected_pairs_data = []
                for marker_label in selected_markers:
                    well_name, surface_name = marker_label.split(" - ", 1)
                    pair_df = well_df[(well_df['Well'] == well_name) & (well_df['Surface'] == surface_name)]
                    if not pair_df.empty:
                        selected_pairs_data.append(pair_df.iloc[0].to_dict())

                st.session_state.selected_well_marker_pairs_data = selected_pairs_data
                st.session_state.area_selected = True
                st.session_state.area_selected_mode = 'well_markers'
                st.session_state.current_step = "analyze_data"
                st.rerun()
```

---

## Phase 3: Analysis Engine Implementation

### Objective Checklist
- [ ] Complete analyze_data_page.py implementation
- [ ] Implement GPU processing integration
- [ ] Add batch processing for large datasets
- [ ] Implement progress tracking and user feedback
- [ ] Add error handling and fallback mechanisms

### Step 3.1: Analysis Data Page Implementation

**File: `pages/analyze_data_page.py`**

```python
import streamlit as st
import numpy as np
import time
from utils.processing import calculate_stats_and_defaults
from utils.data_utils import load_trace_data_for_analysis

def render():
    """Complete analysis data page with GPU processing."""
    st.header("Step 4: Analyze Data")

    # Prerequisites validation
    if not validate_analysis_prerequisites():
        return

    # Display analysis configuration
    show_analysis_configuration()

    # Analysis execution section
    st.subheader("🚀 Execute Analysis")

    if st.button("▶️ Start Analysis", type="primary", use_container_width=True):
        execute_analysis_pipeline()

def validate_analysis_prerequisites():
    """Validate all prerequisites for analysis."""
    if not st.session_state.get('area_selected'):
        st.error("❌ Please select an analysis area first")
        if st.button("← Go to Area Selection"):
            st.session_state.current_step = "select_mode"
            st.rerun()
        return False

    if not st.session_state.get('display_params_configured'):
        st.error("❌ Please configure display parameters first")
        if st.button("← Go to Configure Display"):
            st.session_state.current_step = "configure_display"
            st.rerun()
        return False

    return True

def show_analysis_configuration():
    """Display current analysis configuration."""
    st.subheader("📋 Analysis Configuration")

    area_details = st.session_state.area_selected_details

    col1, col2 = st.columns(2)
    with col1:
        st.write("**Selected Area:**")
        if area_details['type'] == 'aoi':
            st.write(f"• AOI: IL {area_details['boundaries']['inline_min']}-{area_details['boundaries']['inline_max']}, "
                    f"XL {area_details['boundaries']['xline_min']}-{area_details['boundaries']['xline_max']}")
        elif area_details['type'] == 'inline':
            st.write(f"• Inline: {st.session_state.selected_inline}")
        elif area_details['type'] == 'well_markers':
            st.write(f"• Well markers: {len(st.session_state.selected_well_markers)} pairs")

        st.write(f"• Total traces: {area_details['trace_count']}")

    with col2:
        st.write("**Processing Settings:**")
        st.write(f"• GPU Available: {'✅' if st.session_state.GPU_AVAILABLE else '❌'}")
        st.write(f"• Batch Size: {st.session_state.get('batch_size', 'Auto')}")
        st.write(f"• Selected Outputs: {len(st.session_state.get('selected_outputs', []))}")

def execute_analysis_pipeline():
    """Execute the complete analysis pipeline."""
    try:
        # Step 1: Load trace data
        st.write("**Step 1: Loading trace data...**")
        progress_bar = st.progress(0)
        status_text = st.empty()

        trace_data = load_trace_data_for_selected_area()
        progress_bar.progress(25)
        status_text.text(f"Loaded {len(trace_data)} traces")

        # Step 2: Calculate descriptors
        st.write("**Step 2: Calculating spectral descriptors...**")

        # Get GPU functions from app.py
        from app import get_gpu_functions
        gpu_func, gpu_2d_chunked, gpu_2d_mag = get_gpu_functions()

        # Process with appropriate function based on area type
        if st.session_state.area_selected_mode == 'aoi':
            descriptors = process_aoi_analysis(trace_data, gpu_2d_chunked)
        else:
            descriptors = process_standard_analysis(trace_data, gpu_func)

        progress_bar.progress(75)
        status_text.text("Spectral descriptors calculated")

        # Step 3: Store results
        st.session_state.calculated_descriptors = descriptors
        st.session_state.loaded_trace_data = trace_data
        st.session_state.analysis_complete = True

        progress_bar.progress(100)
        status_text.text("Analysis complete!")

        st.success("✅ Analysis completed successfully!")

        # Auto-navigate to results
        time.sleep(1)
        st.session_state.current_step = "view_results"
        st.rerun()

    except Exception as e:
        st.error(f"❌ Analysis failed: {str(e)}")
        st.exception(e)

def load_trace_data_for_selected_area():
    """Load trace data based on selected area mode."""
    header_loader = st.session_state.header_loader
    selected_indices = st.session_state.selected_indices

    # Load traces in batches for memory efficiency
    batch_size = min(1000, len(selected_indices))
    all_traces = []

    for i in range(0, len(selected_indices), batch_size):
        batch_indices = selected_indices[i:i+batch_size]
        batch_traces = header_loader.get_trace_data(batch_indices)
        all_traces.extend(batch_traces)

    return np.array(all_traces)

def process_aoi_analysis(trace_data, gpu_function):
    """Process AOI analysis with GPU acceleration."""
    try:
        # Get analysis parameters
        params = st.session_state.get('analysis_params', {})

        # Execute GPU processing
        with st.spinner("Processing with GPU acceleration..."):
            descriptors = gpu_function(
                trace_data,
                dt=st.session_state.header_loader.dt,
                **params
            )

        return descriptors

    except Exception as e:
        st.warning(f"GPU processing failed: {str(e)}. Falling back to CPU...")
        # Implement CPU fallback
        from utils.dlogst_spec_descriptor_cpu import dlogst_spec_descriptor_cpu
        return dlogst_spec_descriptor_cpu(trace_data, **params)
```

---

## Phase 4: Results and Export Implementation

### Objective Checklist
- [ ] Complete `pages/analyze_data_page.py` for AOI export workflow
- [ ] Implement `generate_aoi_export_filename` in `utils/export_utils.py`
- [ ] Integrate parameterized filenames into the export process
- [ ] Add interactive visualization for AOI results
- [ ] Implement multi-format export (SEG-Y, NumPy)
- [ ] Add download management and file cleanup

### Step 4.1: Implement AOI Export Workflow in `pages/analyze_data_page.py`

**File:** `pages/analyze_data_page.py`

The primary export logic for AOI selections is handled in this file. It orchestrates the configuration, processing, and file generation.

```python
# pages/analyze_data_page.py

# ... (imports and other functions)

def render_aoi_export():
    """Handle the AOI export configuration, processing, and file generation."""
    st.header("Step 4: AOI Export Configuration")
    
    # ... (validation and setup)

    # Attribute selection
    selected_attrs_internal = [ATTR_NAME_MAP[attr_name] for attr_name in selected_attrs_display]
    
    # Grouping and Batching
    grouping_type = st.selectbox("Group Export Files By:", ["inline", "crossline"])
    batch_step = st.number_input(f"Number of {grouping_type}s per batch file", 1)
    
    if st.button("Start Export Process"):
        # ... (initiate export process)
        st.session_state.export_in_progress = True
        st.rerun()

def render_export_process():
    """Handle the actual export processing and generation of SEG-Y files."""
    # ... (batch processing logic)

    with segyio.open(segy_path, 'r', ignore_geometry=True) as src:
        spec = segyio.tools.metadata(src)
        # ... (spec setup)

        for attr in selected_attrs:
            # Generate filename using the new utility function
            from utils.export_utils import generate_aoi_export_filename
            
            # Create a more descriptive batch filename
            aoi_params = f"_IL{st.session_state.aoi_inline_min}-{st.session_state.aoi_inline_max}_XL{st.session_state.aoi_xline_min}-{st.session_state.aoi_xline_max}"
            batch_output_filename = f"batch_{grouping_type}{batch_start_group}-{batch_end_group}_{attr}{aoi_params}.sgy"
            
            # ... (file writing logic)

    # Merge batch files
    for attr in selected_attrs:
        final_output_filename = generate_aoi_export_filename(
            f"{st.session_state.segy_file_info['name']}_{attr}", "sgy"
        )
        # ... (merging logic)
```

### Step 4.2: Implement Filename Generation in `utils/export_utils.py`

**File:** `utils/export_utils.py`

This function will generate descriptive filenames that include AOI boundary parameters.

```python
# utils/export_utils.py

import streamlit as st

def generate_aoi_export_filename(base_name, extension):
    """
    Generate export filename with AOI parameters.
    
    Args:
        base_name (str): Base name for the file
        extension (str): File extension without the dot
        
    Returns:
        str: Formatted filename with AOI parameters
    """
    if (hasattr(st.session_state, 'aoi_inline_min') and
        st.session_state.aoi_inline_min is not None):
        
        aoi_params = (f"_IL{st.session_state.aoi_inline_min}-{st.session_state.aoi_inline_max}"
                      f"_XL{st.session_state.aoi_xline_min}-{st.session_state.aoi_xline_max}")
        return f"{base_name}{aoi_params}.{extension}"
    else:
        return f"{base_name}.{extension}"

def select_export_attributes(exportable_attrs, first_descriptor, sample_length):
    # ... (existing code)
```

### Step 4.3: Update Export Visualization and Download

**File:** `pages/export_results_page.py`

This page will handle the visualization of exported data and provide download links.

```python
# pages/export_results_page.py

# ... (imports)

def render_download_export():
    """Provide a download interface for the exported files."""
    st.header("Step 5: Download Exported Files")
    
    # ... (file validation)

    # Create a descriptive zip filename
    from utils.export_utils import generate_aoi_export_filename
    base_zip_filename = generate_aoi_export_filename(
        f"{st.session_state.segy_file_info['name']}_AOI_Export", "zip"
    )

    # ... (zip file creation and download button)
```

---

## Phase 5: Testing and Integration

### Objective Checklist
- [ ] Create comprehensive test suite for AOI functionality
- [ ] Implement end-to-end workflow testing
- [ ] Add performance benchmarking
- [ ] Create validation scripts
- [ ] Add error handling verification

### Step 5.1: AOI Testing Implementation

**File: `tests/test_aoi_functionality.py` (New)**

```python
import pytest
import streamlit as st
import pandas as pd
import numpy as np
import tempfile
import os
from unittest.mock import Mock, patch

# Import modules to test
from pages.select_area_page import validate_aoi_selection, process_aoi_selection
from pages.export_results_page import generate_aoi_export_filename

class TestAOIFunctionality:
    """Comprehensive test suite for AOI functionality."""

    def setup_method(self):
        """Setup test environment before each test."""
        # Initialize session state
        st.session_state.clear()

        # Create mock headers DataFrame
        self.mock_headers_df = pd.DataFrame({
            'trace_idx': range(100),
            'inline': np.repeat(range(1, 11), 10),
            'crossline': np.tile(range(1, 11), 10),
            'x_coord': np.random.rand(100) * 1000,
            'y_coord': np.random.rand(100) * 1000
        })

        st.session_state.headers_df = self.mock_headers_df

    def test_aoi_boundary_validation_valid(self):
        """Test AOI boundary validation with valid inputs."""
        # Set valid AOI boundaries
        st.session_state.aoi_inline_min = 2
        st.session_state.aoi_inline_max = 5
        st.session_state.aoi_xline_min = 3
        st.session_state.aoi_xline_max = 7

        result = validate_aoi_selection()

        assert result['valid'] == True
        assert result['trace_count'] > 0
        assert 'aoi_df' in result

    def test_aoi_boundary_validation_invalid_range(self):
        """Test AOI boundary validation with invalid range."""
        # Set invalid AOI boundaries (min > max)
        st.session_state.aoi_inline_min = 5
        st.session_state.aoi_inline_max = 2
        st.session_state.aoi_xline_min = 3
        st.session_state.aoi_xline_max = 7

        result = validate_aoi_selection()

        assert result['valid'] == False
        assert 'Inline min cannot be greater than inline max' in result['error']

    def test_aoi_boundary_validation_no_traces(self):
        """Test AOI boundary validation with no traces in range."""
        # Set AOI boundaries outside data range
        st.session_state.aoi_inline_min = 50
        st.session_state.aoi_inline_max = 60
        st.session_state.aoi_xline_min = 50
        st.session_state.aoi_xline_max = 60

        result = validate_aoi_selection()

        assert result['valid'] == False
        assert 'No traces found within specified AOI boundaries' in result['error']

    def test_aoi_export_filename_generation(self):
        """Test AOI export filename generation."""
        # Setup AOI area details
        st.session_state.area_selected_details = {
            'type': 'aoi',
            'boundaries': {
                'inline_min': 100,
                'inline_max': 200,
                'xline_min': 50,
                'xline_max': 150
            }
        }

        filename = generate_aoi_export_filename("test_export", "sgy")
        expected = "test_export_IL100-200_XL50-150.sgy"

        assert filename == expected

    def test_aoi_trace_selection_process(self):
        """Test complete AOI trace selection process."""
        # Set valid AOI boundaries
        st.session_state.aoi_inline_min = 2
        st.session_state.aoi_inline_max = 4
        st.session_state.aoi_xline_min = 3
        st.session_state.aoi_xline_max = 6

        # Mock the validation function to return success
        with patch('pages.select_area_page.validate_aoi_selection') as mock_validate:
            mock_validate.return_value = {
                'valid': True,
                'trace_count': 12,
                'aoi_df': self.mock_headers_df[
                    (self.mock_headers_df['inline'] >= 2) &
                    (self.mock_headers_df['inline'] <= 4) &
                    (self.mock_headers_df['crossline'] >= 3) &
                    (self.mock_headers_df['crossline'] <= 6)
                ]
            }

            result = process_aoi_selection()

            assert result == True
            assert st.session_state.area_selected == True
            assert st.session_state.area_selected_mode == 'aoi'
            assert len(st.session_state.selected_indices) > 0

### Step 5.2: End-to-End Testing Script

**File: `tests/test_complete_workflow.py` (New)**

```python
import pytest
import streamlit as st
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock

class TestCompleteWorkflow:
    """End-to-end workflow testing."""

    def test_complete_aoi_workflow(self):
        """Test complete AOI workflow from data loading to export."""

        # Step 1: Mock data loading
        with patch('utils.data_utils.SegyHeaderLoader') as mock_loader:
            mock_instance = Mock()
            mock_instance.load_headers.return_value = pd.DataFrame({
                'trace_idx': range(100),
                'inline': np.repeat(range(1, 11), 10),
                'crossline': np.tile(range(1, 11), 10)
            })
            mock_loader.return_value = mock_instance

            # Simulate data loading
            st.session_state.header_loader = mock_instance
            st.session_state.headers_df = mock_instance.load_headers()
            st.session_state.segy_file_info = {'name': 'test.sgy', 'trace_count': 100}

        # Step 2: Configure display (mock)
        st.session_state.display_params_configured = True
        st.session_state.analysis_params = {'param1': 'value1'}

        # Step 3: AOI selection
        st.session_state.aoi_inline_min = 2
        st.session_state.aoi_inline_max = 5
        st.session_state.aoi_xline_min = 3
        st.session_state.aoi_xline_max = 7

        # Process AOI selection
        from pages.select_area_page import process_aoi_selection
        with patch('pages.select_area_page.validate_aoi_selection') as mock_validate:
            mock_validate.return_value = {'valid': True, 'trace_count': 20, 'aoi_df': Mock()}
            result = process_aoi_selection()
            assert result == True

        # Step 4: Analysis (mock)
        st.session_state.calculated_descriptors = {
            'WOSS': np.random.rand(20),
            'HFC': np.random.rand(20)
        }
        st.session_state.analysis_complete = True

        # Step 5: Export
        from pages.export_results_page import generate_aoi_export_filename
        filename = generate_aoi_export_filename("test", "sgy")
        assert "IL2-5_XL3-7" in filename

        # Verify complete workflow state
        assert st.session_state.area_selected == True
        assert st.session_state.area_selected_mode == 'aoi'
        assert st.session_state.analysis_complete == True

### Step 5.3: Performance Testing

**File: `tests/test_performance.py` (New)**

```python
import time
import numpy as np
import pytest
from unittest.mock import Mock, patch

class TestPerformance:
    """Performance testing for large datasets."""

    def test_large_aoi_processing_performance(self):
        """Test AOI processing performance with large dataset."""
        # Create large mock dataset
        large_headers_df = pd.DataFrame({
            'trace_idx': range(10000),
            'inline': np.repeat(range(1, 101), 100),
            'crossline': np.tile(range(1, 101), 100),
            'x_coord': np.random.rand(10000) * 1000,
            'y_coord': np.random.rand(10000) * 1000
        })

        st.session_state.headers_df = large_headers_df
        st.session_state.aoi_inline_min = 10
        st.session_state.aoi_inline_max = 50
        st.session_state.aoi_xline_min = 10
        st.session_state.aoi_xline_max = 50

        # Measure AOI validation performance
        start_time = time.time()

        from pages.select_area_page import validate_aoi_selection
        result = validate_aoi_selection()

        end_time = time.time()
        processing_time = end_time - start_time

        # Performance assertions
        assert processing_time < 5.0  # Should complete within 5 seconds
        assert result['valid'] == True
        assert result['trace_count'] > 1000  # Should select significant number of traces

    def test_memory_usage_large_export(self):
        """Test memory usage during large export operations."""
        # This would require memory profiling tools in a real implementation
        # For now, we'll test that large exports don't crash

        large_descriptor_data = np.random.rand(10000, 1000)  # Large dataset

        # Mock export function
        with patch('numpy.save') as mock_save:
            from pages.export_results_page import export_to_segy_with_aoi

            # Should not raise memory errors
            try:
                export_to_segy_with_aoi(large_descriptor_data, "test_output.sgy")
                assert True  # If we get here, no memory error occurred
            except MemoryError:
                pytest.fail("Memory error during large export")

# Run tests with: pytest tests/ -v
```

### Step 5.4: Integration Validation Checklist

**Manual Testing Checklist:**

```markdown
## AOI Implementation Validation Checklist

### Data Loading Phase
- [ ] SEG-Y file uploads successfully
- [ ] Headers are loaded and displayed correctly
- [ ] Inline/crossline ranges are detected properly
- [ ] Well data loads without errors (if provided)
- [ ] Basemap generates correctly

### AOI Selection Phase
- [ ] AOI boundary inputs accept valid ranges
- [ ] Validation rejects invalid boundaries (min > max)
- [ ] Validation rejects out-of-range boundaries
- [ ] AOI selection shows correct trace count
- [ ] AOI summary displays proper information
- [ ] Proceed button works correctly

### Analysis Phase
- [ ] Analysis prerequisites are validated
- [ ] GPU processing works (if available)
- [ ] CPU fallback works when GPU unavailable
- [ ] Progress indicators function properly
- [ ] Analysis completes without errors

### Export Phase
- [ ] Results visualization displays correctly
- [ ] AOI export filenames include boundary parameters
- [ ] Export process completes successfully
- [ ] ZIP files contain expected files
- [ ] Download functionality works
- [ ] Metadata files include AOI information

### Error Handling
- [ ] Invalid file formats are rejected gracefully
- [ ] GPU unavailability is handled properly
- [ ] Memory limitations are managed
- [ ] User feedback is clear and helpful
- [ ] Navigation between steps works correctly
```

---

## Implementation Summary

This phase-by-phase implementation guide provides:

1. **Complete Code Examples**: Direct implementation code for all major components
2. **AOI-Focused Development**: Prioritizes the critical AOI functionality
3. **Objective Checklists**: Clear validation criteria for each phase
4. **Testing Framework**: Comprehensive test suite for validation
5. **Performance Considerations**: Memory management and optimization
6. **Error Handling**: Robust error handling throughout

### Next Steps:
1. Implement Phase 1 (Foundation) first
2. Focus heavily on Phase 2 (AOI) as the critical component
3. Test each phase thoroughly before proceeding
4. Use the provided test suite for validation
5. Follow the manual testing checklist for final verification

The implementation follows the existing modular architecture while incorporating the AOI functionality as documented in the reference materials.
```
```
