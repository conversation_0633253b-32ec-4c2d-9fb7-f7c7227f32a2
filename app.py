"""
WOSS Seismic Analysis Tool - Main Application Router

This module serves as the primary router for the WOSS Seismic Analysis Tool.
It imports page modules and calls their render() functions based on the current step
in the application workflow.
"""

import streamlit as st
import logging

# Import common modules
from common.constants import APP_TITLE
from common.session_state import initialize_session_state, reset_state

# Import centralized GPU manager
from utils.gpu_manager import is_gpu_available

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Configure page - THIS MUST BE THE FIRST STREAMLIT COMMAND
st.set_page_config(page_title=APP_TITLE, layout="wide")

# --- GPU Functions Import (from utils) ---

def _make_gpu_error_functions_global():
    """Helper to define GPU error functions that provide clear error messages."""
    global dlogst_spec_descriptor_gpu, dlogst_spec_descriptor_gpu_2d_chunked, dlogst_spec_descriptor_gpu_2d_chunked_mag

    def gpu_not_available_error(*args, **kwargs):
        raise RuntimeError(
            "GPU processing is required for this application but is not available. "
            "Please ensure you have:\n"
            "1. A CUDA-compatible GPU\n"
            "2. CUDA drivers installed\n"
            "3. CuPy library installed (pip install cupy-cuda11x or cupy-cuda12x)\n"
            "This application is designed for GPU-accelerated processing of inline, crossline, polyline, and AOI operations."
        )

    dlogst_spec_descriptor_gpu = gpu_not_available_error
    dlogst_spec_descriptor_gpu_2d_chunked = gpu_not_available_error
    dlogst_spec_descriptor_gpu_2d_chunked_mag = gpu_not_available_error

# Check GPU availability without importing GPU functions yet
GPU_AVAILABLE = is_gpu_available()

# Define placeholder functions that will be replaced when needed
dlogst_spec_descriptor_gpu = None
dlogst_spec_descriptor_gpu_2d_chunked = None
dlogst_spec_descriptor_gpu_2d_chunked_mag = None

def get_gpu_functions():
    """
    Lazy loader for GPU functions. Only imports when actually needed.
    """
    global dlogst_spec_descriptor_gpu, dlogst_spec_descriptor_gpu_2d_chunked, dlogst_spec_descriptor_gpu_2d_chunked_mag

    if dlogst_spec_descriptor_gpu is None:
        if GPU_AVAILABLE:
            try:
                from utils.dlogst_spec_descriptor_gpu import (
                    dlogst_spec_descriptor_gpu as real_dlogst_spec_descriptor_gpu,
                    dlogst_spec_descriptor_gpu_2d_chunked as real_dlogst_spec_descriptor_gpu_2d_chunked,
                    dlogst_spec_descriptor_gpu_2d_chunked_mag as real_dlogst_spec_descriptor_gpu_2d_chunked_mag
                )
                dlogst_spec_descriptor_gpu = real_dlogst_spec_descriptor_gpu
                dlogst_spec_descriptor_gpu_2d_chunked = real_dlogst_spec_descriptor_gpu_2d_chunked
                dlogst_spec_descriptor_gpu_2d_chunked_mag = real_dlogst_spec_descriptor_gpu_2d_chunked_mag
                logging.info("Successfully imported GPU spectral descriptor functions")
            except ImportError as e:
                logging.warning(f"Failed to import GPU functions: {e}")
                _make_gpu_error_functions_global()
        else:
            _make_gpu_error_functions_global()

    return dlogst_spec_descriptor_gpu, dlogst_spec_descriptor_gpu_2d_chunked, dlogst_spec_descriptor_gpu_2d_chunked_mag

# Import page modules
from pages.load_data_page import render as render_load_data
from pages.configure_display_page import render as render_configure_display
from pages.select_area_page import render as render_select_area
from pages.analyze_data_page import render as render_analyze_data
from pages.export_results_page import render as render_export_results

# Initialize session state
initialize_session_state()

# Store GPU availability in session state for pages to access
st.session_state.GPU_AVAILABLE = GPU_AVAILABLE

# Set application title
st.title(APP_TITLE)

# Now that we've initialized the app and set the page config first, we can add GPU status to sidebar
if GPU_AVAILABLE:
    st.sidebar.success("✅ GPU acceleration enabled")
else:
    st.sidebar.error("❌ GPU required but not available")
    st.sidebar.markdown("""
    **This application requires GPU processing.**

    Please ensure you have:
    - CUDA-compatible GPU
    - CUDA drivers installed
    - CuPy library: `pip install cupy-cuda11x`

    GPU is required for inline, crossline, polyline, and AOI operations.
    """)

# Define application steps for sidebar navigation
APP_STEPS = {
    "1. Load Data": "load_data",
    "2. Configure Display": "configure_display",
    "3. Select Area": "select_mode",  # Use select_mode as in reference
    "4. Analyze Data": "analyze_data",
    "5. Export Results": "export_results",
}

# Get the current step's display name for the radio button default
# This handles cases where current_step might be a sub-step like 'select_traces'
current_display_step = "1. Load Data" # Default
if st.session_state.current_step == "load_data":
    current_display_step = "1. Load Data"
elif st.session_state.current_step == "configure_display":
    current_display_step = "2. Configure Display"
elif st.session_state.current_step in ["select_mode", "select_traces"]:
    current_display_step = "3. Select Area"
elif st.session_state.current_step == "analyze_data":
    current_display_step = "4. Analyze Data"
elif st.session_state.current_step in ["view_results", "export_process", "download_export", "export_results"]:
    current_display_step = "5. Export Results"


st.sidebar.header("Navigation")
selected_display_step = st.sidebar.radio(
    "Go to step:",
    options=list(APP_STEPS.keys()),
    index=list(APP_STEPS.keys()).index(current_display_step), # Set default based on current step
    key="sidebar_navigation"
)

# Update current_step if navigation changes
new_step_from_sidebar_key = APP_STEPS[selected_display_step]

# Determine the actual target page step based on the sidebar key
if new_step_from_sidebar_key == "select_mode":
    target_page_step = "select_mode"
elif new_step_from_sidebar_key == "export_results":
    # When "5. Export Results" is selected from sidebar,
    # the default landing page within that section is "view_results".
    # The main routing logic further down will handle redirection if,
    # for example, analysis is not complete.
    target_page_step = "view_results"
else:
    target_page_step = new_step_from_sidebar_key

# Only update and rerun if the current step is actually different from the target page step
if st.session_state.current_step != target_page_step:
    st.session_state.current_step = target_page_step
    st.rerun()


# Add "Start New Analysis" button to sidebar (visible on all pages)
st.sidebar.markdown("---")  # Add a separator
if st.sidebar.button("🔄 Start New Analysis", use_container_width=True, key="app_start_new_analysis_button"): # Added unique key
    reset_state() # This will set current_step back to 'load_data'
    st.success("Starting new analysis. All temporary data has been cleared.")
    st.rerun()

# Route to the appropriate page based on the current step
# The st.session_state.current_step is now managed by the sidebar navigation or page logic

# Log the current step and important session state variables for debugging
logging.info(f"Current step: {st.session_state.current_step}")
logging.info(f"display_params_configured: {st.session_state.get('display_params_configured')}")
logging.info(f"area_selected: {st.session_state.get('area_selected')}")

if st.session_state.current_step == "load_data":
    render_load_data()
elif st.session_state.current_step == "configure_display":
    render_configure_display()
elif st.session_state.current_step == "select_mode":
    render_select_area()  # Only mode selection
elif st.session_state.current_step == "select_traces":
    # For inline/crossline modes, stay in select_area page but show the input fields
    render_select_area()  # Remove trace_selection parameter

elif st.session_state.current_step in ["view_results", "export_process", "download_export", "export_results"]: # "export_results" is the key from APP_STEPS
    # Ensure sub-steps are handled correctly
    if st.session_state.current_step == "export_results": # Navigated via sidebar
        if not st.session_state.get('analysis_complete'): # Check if analysis is done
            st.session_state.current_step = "analyze_data" # Or a previous step if appropriate
            st.rerun()
        else: # Default to 'view_results'
            st.session_state.current_step = "view_results"
            # No rerun here, render_export_results will handle 'view_results'
    render_export_results()
elif st.session_state.current_step == "analyze_data":
    # Prerequisite checks for Analyze Data
    if not st.session_state.get('segy_file_info'):
        logging.warning("No SEG-Y file loaded. Redirecting to load_data step.")
        st.session_state.current_step = "load_data"
        st.rerun()
    elif not st.session_state.get('display_params_configured'):
        logging.warning("Display parameters not configured. Redirecting to configure_display step.")
        st.session_state.current_step = "configure_display"
        st.rerun()
    elif not st.session_state.get('area_selected'):
        logging.warning("Area not selected. Redirecting to select_mode step.")
        st.session_state.current_step = "select_mode" # or "select_area"
        st.rerun()
    # New check for well_analysis_sub_option if mode is "By well markers"
    elif st.session_state.get('selection_mode') == "By well markers" and not st.session_state.get('well_analysis_sub_option'):
        logging.warning("Well analysis sub-option (Individual/Grouping) not selected. Redirecting to select_mode step.")
        st.session_state.current_step = "select_mode"
        st.rerun()
    else:
        logging.info("All prerequisites met for analyze_data. Rendering page.")
        render_analyze_data()
else:
    # Default to load_data if current_step is not recognized (should be rare with radio button)
    st.session_state.current_step = "load_data"
    render_load_data()

# --- Implementation Notes for `pages.select_area_page.render_select_area` ---
# The following comments detail the requirements for "Step 3: Select Area", specifically for
# "Option 1: Well marker-pair selection". This logic should be implemented within the
# `render_select_area` function in `pages/select_area_page.py`.

# Requirement: Allow multiple well-marker pair selections.
# Reference: The Tkinter implementation's `select_well_marker_pairs` function in
#            `3D_WOSS_Main_Script_init.py` (lines 1007-1061) uses a multi-select listbox.

# Implementation Guide for `render_select_area` in `pages/select_area_page.py`:
#
# 1. When presenting well-marker pairs for selection (if this mode is chosen by the user):
#    - Use `st.multiselect` instead of `st.selectbox` or `st.radio`.
#    - Example:
#      `selected_marker_data_list = st.multiselect("Select well-marker pair(s):", options=available_marker_pairs_formatted, key="well_marker_multiselect")`
#      (where `available_marker_pairs_formatted` is a list of strings like "WellA - MarkerX")
#
# 2. `selected_marker_data_list` will be a list of the selected string representations.
#    You will need to map these back to the actual well and marker data.
#
# 3. Store the list of selected well-marker pair information (e.g., a list of dictionaries,
#    or a filtered DataFrame) in `st.session_state`. For example:
#    `st.session_state.selected_well_marker_pairs = processed_list_of_selected_pairs`
#
# 4. After selection, a button (e.g., "Complete Step 3 and Continue") should trigger the processing
#    of selected pairs and transition to the next step ("precompute_qc").
#
# 5. Ensure downstream processing (e.g., in `render_precompute_qc` or `render_analyze_data`)
#    can handle a list of well-marker pairs if this selection mode was used.
#    This might involve iterating through `st.session_state.selected_well_marker_pairs`.
#    The `precompute_qc` page should check `st.session_state.area_selected_mode` (e.g., 'well_markers', 'trace_range')
#    to determine how `st.session_state.selected_area_data` (or similar) should be interpreted.
#
# Example of processing the selection and transitioning within `render_select_area`:
#
#   `if 'well_data_df' in st.session_state and not st.session_state.well_data_df.empty:`
#       `well_df = st.session_state.well_data_df`
#       `# Format options for multiselect: "WellName - SurfaceName"`
#       `marker_options = sorted([f"{row['Well']} - {row['Surface']}" for _, row in well_df.iterrows()])`
#
#       `if not marker_options:`
#           `st.warning("No well marker pairs available to select from the loaded well data.")`
#       `else:`
#           `selected_labels = st.multiselect(`
#               `"Select well-marker pair(s) for analysis:",`
#               `marker_options,`
#               `default=st.session_state.get('previously_selected_marker_labels', []), # Optional: persist selection across reruns within the step`
#               `key="well_marker_multiselect"`
#           `)`
#           `st.session_state.previously_selected_marker_labels = selected_labels # Store for default`
#
#           `if st.button("✔️ Complete Step 3 and Continue to Pre-computation", key="complete_step3_button_well_markers"):`
#               `if selected_labels:`
#                   `selected_pairs_data = []`
#                   `for label in selected_labels:`
#                       `well_name, surface_name = label.split(" - ", 1)`
#                       `pair_df = well_df[(well_df['Well'] == well_name) & (well_df['Surface'] == surface_name)]`
#                       `if not pair_df.empty:`
#                           `# Store relevant data, e.g., the first match as a dictionary or specific fields`
#                           `selected_pairs_data.append(pair_df.iloc[0].to_dict())`
#                   `st.session_state.selected_well_marker_pairs = selected_pairs_data`
#                   `st.session_state.area_selected_details = {'type': 'well_markers', 'count': len(selected_pairs_data), 'labels': selected_labels}`
#                   `st.session_state.area_selected = True`
#                   `st.session_state.area_selected_mode = 'well_markers'`
#                   `st.success(f"{len(selected_pairs_data)} well-marker pair(s) selected. Proceeding to Pre-computation & QC.")`
#                   `st.session_state.current_step = "precompute_qc" # CRITICAL: Transition to next step`
#                   `# Clean up temporary selection state if needed`
#                   `if 'previously_selected_marker_labels' in st.session_state: del st.session_state.previously_selected_marker_labels`
#                   `st.rerun()`
#               `else:`
#                   `st.warning("Please select at least one well-marker pair to continue.")`
#           `elif selected_labels: # Show current selection if button not pressed yet`
#               `st.info(f"{len(selected_labels)} well-marker pair(s) currently selected. Click the button above to confirm and continue.")`
#           `else:`
#               `st.info("No well-marker pairs currently selected. Please make a selection and click the button to continue.")`
#   `else:`
#       `st.warning("Well data not loaded or empty. Please load well data in Step 1 to use this option.")`
#
# This note replaces the previous, more general comment block.
