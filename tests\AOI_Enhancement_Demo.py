"""
AOI Enhancement Demonstration Script

This script demonstrates the enhanced AOI functionality without requiring
the full Streamlit application to be running.
"""

import pandas as pd
import numpy as np
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import AOI validation functions
from utils.aoi_validation import (
    validate_aoi_size_performance,
    get_aoi_boundary_suggestions
)

def create_sample_data():
    """Create sample seismic data for demonstration."""
    print("Creating sample seismic data...")
    
    # Create a realistic seismic survey grid
    inline_range = range(1000, 2001)  # 1001 inlines
    xline_range = range(3000, 4001)   # 1001 crosslines
    
    # Create headers DataFrame
    inlines = []
    crosslines = []
    trace_indices = []
    
    trace_idx = 0
    for inline in inline_range:
        for xline in xline_range:
            inlines.append(inline)
            crosslines.append(xline)
            trace_indices.append(trace_idx)
            trace_idx += 1
    
    headers_df = pd.DataFrame({
        'inline': inlines,
        'crossline': crosslines,
        'trace_idx': trace_indices
    })
    
    print(f"Created sample data with {len(headers_df):,} traces")
    print(f"Inline range: {min(inlines)} - {max(inlines)}")
    print(f"Crossline range: {min(crosslines)} - {max(crosslines)}")
    
    return headers_df

def demonstrate_aoi_suggestions(headers_df):
    """Demonstrate AOI boundary suggestions."""
    print("\n" + "="*60)
    print("AOI BOUNDARY SUGGESTIONS DEMONSTRATION")
    print("="*60)
    
    suggestions = get_aoi_boundary_suggestions(headers_df)
    
    for name, suggestion in suggestions.items():
        print(f"\n{suggestion['description']}:")
        print(f"  Inline range: {suggestion['inline_min']:,} - {suggestion['inline_max']:,}")
        print(f"  Crossline range: {suggestion['xline_min']:,} - {suggestion['xline_max']:,}")
        
        # Calculate coverage
        inline_span = suggestion['inline_max'] - suggestion['inline_min'] + 1
        xline_span = suggestion['xline_max'] - suggestion['xline_min'] + 1
        total_traces = inline_span * xline_span
        
        print(f"  Grid size: {inline_span:,} × {xline_span:,} = {total_traces:,} traces")

def demonstrate_performance_validation():
    """Demonstrate AOI performance validation."""
    print("\n" + "="*60)
    print("AOI PERFORMANCE VALIDATION DEMONSTRATION")
    print("="*60)
    
    test_sizes = [1000, 25000, 50000, 75000, 100000, 150000]
    
    for size in test_sizes:
        result = validate_aoi_size_performance(size)
        
        print(f"\nAOI with {size:,} traces:")
        print(f"  Valid: {result['valid']}")
        
        if result['warnings']:
            for warning in result['warnings']:
                print(f"  ⚠️  {warning}")
        else:
            print("  ✅ No performance warnings")
        
        if result['recommendations']:
            for rec in result['recommendations']:
                print(f"  💡 {rec}")

def demonstrate_aoi_filtering(headers_df):
    """Demonstrate AOI filtering logic."""
    print("\n" + "="*60)
    print("AOI FILTERING DEMONSTRATION")
    print("="*60)
    
    # Define a custom AOI
    aoi_inline_min = 1200
    aoi_inline_max = 1800
    aoi_xline_min = 3200
    aoi_xline_max = 3800
    
    print(f"\nCustom AOI Definition:")
    print(f"  Inline range: {aoi_inline_min:,} - {aoi_inline_max:,}")
    print(f"  Crossline range: {aoi_xline_min:,} - {aoi_xline_max:,}")
    
    # Filter traces within AOI
    aoi_df = headers_df[
        (headers_df['inline'] >= aoi_inline_min) &
        (headers_df['inline'] <= aoi_inline_max) &
        (headers_df['crossline'] >= aoi_xline_min) &
        (headers_df['crossline'] <= aoi_xline_max)
    ]
    
    # Calculate statistics
    total_traces = len(headers_df)
    aoi_traces = len(aoi_df)
    coverage_pct = (aoi_traces / total_traces) * 100
    
    inline_span = aoi_inline_max - aoi_inline_min + 1
    xline_span = aoi_xline_max - aoi_xline_min + 1
    grid_size = inline_span * xline_span
    grid_coverage = (aoi_traces / grid_size) * 100
    
    print(f"\nAOI Statistics:")
    print(f"  Total traces in AOI: {aoi_traces:,}")
    print(f"  Survey coverage: {coverage_pct:.2f}%")
    print(f"  Grid coverage: {grid_coverage:.2f}%")
    print(f"  Inline span: {inline_span:,}")
    print(f"  Crossline span: {xline_span:,}")

def demonstrate_validation_scenarios():
    """Demonstrate various validation scenarios."""
    print("\n" + "="*60)
    print("AOI VALIDATION SCENARIOS DEMONSTRATION")
    print("="*60)
    
    scenarios = [
        {
            'name': 'Valid AOI',
            'inline_min': 1200, 'inline_max': 1800,
            'xline_min': 3200, 'xline_max': 3800
        },
        {
            'name': 'Invalid - Inline min > max',
            'inline_min': 1800, 'inline_max': 1200,
            'xline_min': 3200, 'xline_max': 3800
        },
        {
            'name': 'Invalid - Crossline min > max',
            'inline_min': 1200, 'inline_max': 1800,
            'xline_min': 3800, 'xline_max': 3200
        },
        {
            'name': 'Small AOI',
            'inline_min': 1500, 'inline_max': 1510,
            'xline_min': 3500, 'xline_max': 3510
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['name']}:")
        print(f"  Inline: {scenario['inline_min']:,} - {scenario['inline_max']:,}")
        print(f"  Crossline: {scenario['xline_min']:,} - {scenario['xline_max']:,}")
        
        # Check basic logic validation
        if scenario['inline_min'] > scenario['inline_max']:
            print("  ❌ Invalid: Inline minimum > maximum")
        elif scenario['xline_min'] > scenario['xline_max']:
            print("  ❌ Invalid: Crossline minimum > maximum")
        else:
            print("  ✅ Valid boundary logic")
            
            # Calculate trace count
            inline_span = scenario['inline_max'] - scenario['inline_min'] + 1
            xline_span = scenario['xline_max'] - scenario['xline_min'] + 1
            estimated_traces = inline_span * xline_span
            
            print(f"  Estimated traces: {estimated_traces:,}")
            
            # Check performance
            perf_result = validate_aoi_size_performance(estimated_traces)
            if perf_result['warnings']:
                print(f"  ⚠️  {perf_result['warnings'][0]}")
            else:
                print("  ✅ Good performance expected")

def main():
    """Main demonstration function."""
    print("AOI ENHANCEMENT DEMONSTRATION")
    print("="*60)
    print("This script demonstrates the enhanced AOI functionality")
    print("for manual definition of inline and crossline ranges.")
    
    # Create sample data
    headers_df = create_sample_data()
    
    # Run demonstrations
    demonstrate_aoi_suggestions(headers_df)
    demonstrate_performance_validation()
    demonstrate_aoi_filtering(headers_df)
    demonstrate_validation_scenarios()
    
    print("\n" + "="*60)
    print("DEMONSTRATION COMPLETE")
    print("="*60)
    print("\nKey Features Demonstrated:")
    print("✅ AOI boundary suggestions (Full, Center 50%, Center 25%)")
    print("✅ Performance validation with warnings and recommendations")
    print("✅ AOI filtering and trace selection logic")
    print("✅ Validation scenarios for different boundary conditions")
    print("✅ Statistical calculations (coverage, spans, trace counts)")
    
    print("\nThe enhanced AOI functionality provides:")
    print("• Complete manual control over inline/crossline ranges")
    print("• Real-time validation with helpful error messages")
    print("• Performance warnings for large AOI selections")
    print("• Smart suggestions and preset options")
    print("• Comprehensive statistics and preview capabilities")
    print("• Seamless integration with export and analysis workflows")

if __name__ == '__main__':
    main()
