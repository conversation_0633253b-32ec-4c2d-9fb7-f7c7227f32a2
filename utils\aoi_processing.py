"""
AOI Processing Module for WOSS Seismic Analysis Tool.

This module provides processing functions specifically for 
Area of Interest (AOI) data processing, including trace loading,
data validation, and processing pipeline integration.

Implements Phase 2 AOI processing functionality as specified in AOI_implementation_phase.md
"""

import streamlit as st
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any
from utils.data_utils import load_trace_sample

def load_trace_data_for_aoi_analysis() -> np.ndarray:
    """
    Load trace data for AOI analysis with comprehensive error handling.

    Returns:
        np.ndarray: Array of loaded trace data

    Raises:
        ValueError: If prerequisites are not met
        RuntimeError: If trace loading fails
    """
    try:
        # Validate prerequisites
        if not hasattr(st.session_state, 'header_loader') or st.session_state.header_loader is None:
            raise ValueError("SEG-Y header loader not available. Please load data first.")

        if not hasattr(st.session_state, 'selected_indices') or not st.session_state.selected_indices:
            raise ValueError("No trace indices selected. Please select an area first.")

        header_loader = st.session_state.header_loader
        selected_indices = st.session_state.selected_indices

        # Validate trace indices
        if not isinstance(selected_indices, list) or len(selected_indices) == 0:
            raise ValueError("Invalid or empty trace indices list")

        # Check for reasonable trace count
        if len(selected_indices) > 50000:
            logging.warning(f"Large number of traces selected ({len(selected_indices)}). This may take significant time.")

        # Load traces in batches for memory efficiency
        batch_size = min(1000, len(selected_indices))
        all_traces = []
        failed_indices = []

        for i in range(0, len(selected_indices), batch_size):
            batch_indices = selected_indices[i:i+batch_size]
            try:
                batch_traces = header_loader.get_trace_data(batch_indices)
                if batch_traces is not None:
                    all_traces.extend(batch_traces)
                else:
                    failed_indices.extend(batch_indices)
            except Exception as batch_error:
                logging.error(f"Failed to load batch {i//batch_size + 1}: {str(batch_error)}")
                failed_indices.extend(batch_indices)

        if not all_traces:
            raise RuntimeError("Failed to load any trace data")

        if failed_indices:
            logging.warning(f"Failed to load {len(failed_indices)} traces out of {len(selected_indices)}")

        return np.array(all_traces)

    except Exception as e:
        error_msg = f"Error loading trace data for AOI analysis: {str(e)}"
        logging.error(error_msg, exc_info=True)
        raise RuntimeError(error_msg) from e

def process_aoi_analysis(trace_data: np.ndarray, gpu_function) -> Dict[str, Any]:
    """
    Process AOI analysis with GPU acceleration.
    
    Args:
        trace_data: Array of trace data
        gpu_function: GPU processing function
        
    Returns:
        Dict containing processed descriptors
    """
    try:
        # Get analysis parameters
        params = st.session_state.get('analysis_params', {})
        
        # Execute GPU processing
        with st.spinner("Processing AOI with GPU acceleration..."):
            descriptors = gpu_function(
                trace_data,
                dt=st.session_state.header_loader.dt,
                **params
            )
        
        return descriptors
        
    except Exception as e:
        st.warning(f"GPU processing failed: {str(e)}. Falling back to CPU...")
        # Implement CPU fallback
        from utils.dlogst_spec_descriptor_cpu import dlogst_spec_descriptor_cpu
        return dlogst_spec_descriptor_cpu(trace_data, **params)

def validate_aoi_processing_parameters() -> Dict[str, Any]:
    """
    Validate AOI processing parameters and configuration.
    
    Returns:
        Dict with validation results
    """
    validation_result = {'valid': True, 'errors': [], 'warnings': []}
    
    try:
        # Check if AOI is properly selected
        if not st.session_state.get('area_selected') or st.session_state.get('area_selected_mode') != 'aoi':
            validation_result['valid'] = False
            validation_result['errors'].append("AOI not properly selected")
            return validation_result
        
        # Check AOI bounds
        aoi_bounds = st.session_state.get('aoi_bounds')
        if not aoi_bounds:
            validation_result['valid'] = False
            validation_result['errors'].append("AOI bounds not defined")
            return validation_result
        
        # Check processing option
        processing_option = st.session_state.get('aoi_processing_option')
        if not processing_option:
            validation_result['warnings'].append("AOI processing option not set, using default")
            st.session_state.aoi_processing_option = "Full AOI"
        
        # Check selected indices
        selected_indices = st.session_state.get('selected_indices', [])
        if not selected_indices:
            validation_result['valid'] = False
            validation_result['errors'].append("No trace indices selected for AOI")
            return validation_result
        
        # Check trace count is reasonable
        if len(selected_indices) > 10000:
            validation_result['warnings'].append(f"Large AOI selected ({len(selected_indices)} traces). Processing may take time.")
        elif len(selected_indices) < 10:
            validation_result['warnings'].append(f"Small AOI selected ({len(selected_indices)} traces). Results may be limited.")
        
        # Check GPU availability for large AOI
        if len(selected_indices) > 1000 and not st.session_state.get('GPU_AVAILABLE', False):
            validation_result['warnings'].append("Large AOI without GPU acceleration. Processing will be slower.")
        
        return validation_result
        
    except Exception as e:
        logging.error(f"AOI processing validation error: {str(e)}")
        validation_result['valid'] = False
        validation_result['errors'].append(f"Validation error: {str(e)}")
        return validation_result

def get_aoi_processing_configuration() -> Dict[str, Any]:
    """
    Get current AOI processing configuration.
    
    Returns:
        Dict with processing configuration
    """
    try:
        area_details = st.session_state.get('area_selected_details', {})
        
        config = {
            'processing_mode': 'aoi',
            'processing_option': st.session_state.get('aoi_processing_option', 'Full AOI'),
            'trace_count': len(st.session_state.get('selected_indices', [])),
            'aoi_bounds': st.session_state.get('aoi_bounds', {}),
            'gpu_available': st.session_state.get('GPU_AVAILABLE', False),
            'batch_size': st.session_state.get('batch_size', 'Auto'),
            'area_details': area_details
        }
        
        # Add AOI-specific configuration
        if area_details.get('type') == 'aoi' and 'boundaries' in area_details:
            boundaries = area_details['boundaries']
            config['aoi_size'] = {
                'inline_count': boundaries['inline_max'] - boundaries['inline_min'] + 1,
                'xline_count': boundaries['xline_max'] - boundaries['xline_min'] + 1,
                'total_grid_points': (boundaries['inline_max'] - boundaries['inline_min'] + 1) * 
                                   (boundaries['xline_max'] - boundaries['xline_min'] + 1)
            }
        
        return config
        
    except Exception as e:
        logging.error(f"Error getting AOI processing configuration: {str(e)}")
        return {'error': str(e)}

def prepare_aoi_for_analysis() -> bool:
    """
    Prepare AOI data for analysis by validating and setting up processing parameters.
    
    Returns:
        bool: True if preparation successful, False otherwise
    """
    try:
        # Validate processing parameters
        validation = validate_aoi_processing_parameters()
        
        if not validation['valid']:
            for error in validation['errors']:
                st.error(f"❌ {error}")
            return False
        
        # Show warnings if any
        for warning in validation['warnings']:
            st.warning(f"⚠️ {warning}")
        
        # Set up processing configuration
        config = get_aoi_processing_configuration()
        st.session_state.aoi_processing_config = config
        
        # Prepare analysis parameters
        if 'analysis_params' not in st.session_state:
            st.session_state.analysis_params = {}
        
        # Add AOI-specific parameters
        st.session_state.analysis_params.update({
            'processing_mode': 'aoi',
            'aoi_bounds': st.session_state.aoi_bounds,
            'trace_count': config['trace_count']
        })
        
        return True
        
    except Exception as e:
        logging.error(f"Error preparing AOI for analysis: {str(e)}")
        st.error(f"❌ Error preparing AOI for analysis: {str(e)}")
        return False

def get_aoi_memory_requirements() -> Dict[str, Any]:
    """
    Estimate memory requirements for AOI processing.
    
    Returns:
        Dict with memory requirement estimates
    """
    try:
        selected_indices = st.session_state.get('selected_indices', [])
        trace_count = len(selected_indices)
        
        if trace_count == 0:
            return {'error': 'No traces selected'}
        
        # Estimate based on typical seismic trace size
        # Assume ~2000 samples per trace, 4 bytes per float32
        bytes_per_trace = 2000 * 4
        total_bytes = trace_count * bytes_per_trace
        
        # Convert to MB
        total_mb = total_bytes / (1024 * 1024)
        
        # Estimate processing overhead (descriptors, intermediate arrays)
        processing_overhead = total_mb * 2  # 2x overhead estimate
        
        requirements = {
            'trace_count': trace_count,
            'estimated_trace_data_mb': total_mb,
            'estimated_processing_mb': processing_overhead,
            'estimated_total_mb': total_mb + processing_overhead,
            'recommended_batch_size': min(1000, max(100, int(4000 / (total_mb / trace_count))))
        }
        
        return requirements
        
    except Exception as e:
        logging.error(f"Error estimating AOI memory requirements: {str(e)}")
        return {'error': str(e)}

def optimize_aoi_batch_size() -> int:
    """
    Optimize batch size for AOI processing based on available memory and trace count.
    
    Returns:
        int: Optimized batch size
    """
    try:
        memory_req = get_aoi_memory_requirements()
        
        if 'error' in memory_req:
            return 500  # Default batch size
        
        recommended_batch = memory_req.get('recommended_batch_size', 500)
        
        # Adjust based on GPU availability
        if st.session_state.get('GPU_AVAILABLE', False):
            # GPU can handle larger batches
            recommended_batch = min(2000, recommended_batch * 2)
        
        # Ensure minimum and maximum bounds
        recommended_batch = max(50, min(4000, recommended_batch))
        
        return recommended_batch
        
    except Exception as e:
        logging.error(f"Error optimizing AOI batch size: {str(e)}")
        return 500  # Default fallback

def create_aoi_processing_summary() -> Dict[str, Any]:
    """
    Create a summary of AOI processing configuration and parameters.
    
    Returns:
        Dict with processing summary
    """
    try:
        config = get_aoi_processing_configuration()
        memory_req = get_aoi_memory_requirements()
        
        summary = {
            'aoi_configuration': config,
            'memory_requirements': memory_req,
            'processing_ready': True,
            'timestamp': pd.Timestamp.now().isoformat()
        }
        
        # Add validation status
        validation = validate_aoi_processing_parameters()
        summary['validation'] = validation
        
        if not validation['valid']:
            summary['processing_ready'] = False
        
        return summary
        
    except Exception as e:
        logging.error(f"Error creating AOI processing summary: {str(e)}")
        return {'error': str(e), 'processing_ready': False}
