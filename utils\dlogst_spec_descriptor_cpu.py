# -*- coding: utf-8 -*-
"""
CPU implementation of spectral descriptor calculations.
This is a simplified version of the GPU implementation for fallback purposes.

Created on April 25, 2025 by devri.agustianto
"""

import numpy as np
from scipy import signal
import logging

def dlogst_spec_descriptor_cpu(data, dt, fmax=None, shape=None, kmax=None, int_val=None,
                              b1=None, b2=None, p_bandwidth=2, roll_percent=0.85, use_band_limited=False,
                              **kwargs):
    """
    Compute spectral descriptors from a 1D signal using CPU (NumPy/SciPy).
    This is a simplified version of the GPU implementation for fallback purposes.
    
    Args:
        data: Input signal (1D array)
        dt: Time sampling interval in seconds
        fmax: Maximum frequency to analyze (default: len(data)//2)
        shape: Shape parameter for logistic function (default: 0.05)
        kmax: Maximum wavenumber (default: 0.25 * (1 / (2 * dt)))
        int_val: Integration value (default: 0.1 * kmax)
        b1: Lower frequency bound for band-limited analysis (default: 0.0)
        b2: Upper frequency bound for band-limited analysis (default: fmax)
        p_bandwidth: Power for bandwidth calculation (default: 2)
        roll_percent: Percentile for rolloff calculation (default: 0.85)
        use_band_limited: Whether to use band-limited analysis (default: False)
        **kwargs: Additional parameters (ignored)
        
    Returns:
        dict: Dictionary containing spectral descriptors
    """
    logging.info("Using CPU implementation of spectral descriptor calculation")
    
    # Default parameter handling
    data = np.asarray(data, dtype=np.float32).flatten()
    LS = len(data)
    dt = np.float32(dt)
    
    if fmax is None: fmax = LS // 2
    if shape is None: shape = np.float32(0.05)
    if kmax is None: kmax = np.float32(0.25 * (1 / (2 * dt)))
    if int_val is None: int_val = np.float32(0.1 * kmax)
    if b1 is None: b1 = np.float32(0.0)
    if b2 is None: b2 = np.float32(fmax)
    
    # Small epsilon for numerical stability
    EPSILON = 1e-6
    
    # Time and frequency axes
    time_vec = np.arange(LS, dtype=np.float32) * dt
    freqst = np.fft.rfftfreq(LS, dt)
    idf = np.argmin(np.abs(freqst - fmax))
    freqst = freqst[:idf+1]
    N2 = len(freqst)
    
    # Compute FFT of input data
    Fd = np.fft.rfft(data)
    
    # Simplified spectral descriptors
    mag = np.abs(Fd[:N2])
    
    # Calculate basic spectral descriptors
    total_sum_mag = np.sum(mag)
    sum_freq_mag = np.sum(freqst * mag)
    
    # Peak frequency (dominant frequency)
    peak_idx = np.argmax(mag)
    peak_freq = freqst[peak_idx] if peak_idx < len(freqst) else 0.0
    
    # Spectral centroid
    spec_centroid = sum_freq_mag / total_sum_mag if total_sum_mag > EPSILON else 0.0
    
    # Dominant frequency (weighted average)
    mag_p = mag ** 2
    sum_freq_mag_p = np.sum(freqst * mag_p)
    sum_mag_p = np.sum(mag_p)
    fdom = sum_freq_mag_p / sum_mag_p if sum_mag_p > EPSILON else 0.0
    
    # Spectral slope
    mean_freq = np.mean(freqst)
    freq_diff = freqst - mean_freq
    den_slope = np.sum(freq_diff ** 2)
    
    # Helper function for slope calculation
    def calculate_slope(freq, signal_data, den):
        mean_f = np.mean(freq)
        signal_sum = np.sum(freq[:, np.newaxis] * signal_data, axis=0)
        signal_total_sum = np.sum(signal_data, axis=0)
        return np.where(np.abs(den) > EPSILON, 
                       (signal_sum - mean_f * signal_total_sum) / den, 
                       0.0)
    
    # Helper function for spectral decrease
    def calculate_decrease(signal_data):
        total_sum = np.sum(signal_data, axis=0)
        if signal_data.shape[0] <= 1:
            return np.zeros_like(total_sum)
        k_vec = np.arange(1, signal_data.shape[0], dtype=np.float32)
        weighted = (signal_data[1:] - signal_data[0:1]) / k_vec[:, np.newaxis]
        sum_weighted = np.sum(weighted, axis=0)
        return np.where(total_sum > EPSILON, sum_weighted / total_sum, 0.0)
    
    # Create a simple approximation of mag_voice
    # In a real implementation, this would be more complex
    mag_voice = np.zeros((N2, LS), dtype=np.float32)
    for i in range(N2):
        mag_voice[i, :] = mag[i] * np.cos(2 * np.pi * freqst[i] * time_vec)
    
    # Calculate slopes
    mag_slope = calculate_slope(freqst, mag[:, np.newaxis], den_slope)
    mag_voice_slope = np.mean(mag_voice, axis=1)  # Simplified
    voice_slope = np.zeros_like(mag_slope)  # Simplified
    
    # Spectral decrease
    spec_decrease = calculate_decrease(mag[:, np.newaxis])
    
    # Normalized frequencies
    fsamp = 1 / dt
    norm_peak_freq = peak_freq * (2 * np.pi / fsamp)
    norm_spec_centroid = spec_centroid * (2 * np.pi / fsamp)
    norm_fdom = fdom * (2 * np.pi / fsamp)
    
    # High frequency content (HFC)
    hfc = sum_freq_mag
    
    # Spectral bandwidth
    deviation = np.abs(freqst[:, np.newaxis] - spec_centroid)
    bandwidth_sum = np.sum(mag[:, np.newaxis] * (deviation ** p_bandwidth))
    spec_bandwidth = (bandwidth_sum / total_sum_mag) ** (1.0 / p_bandwidth) if total_sum_mag > EPSILON else 0.0
    
    # Spectral rolloff
    threshold = roll_percent * total_sum_mag
    cum_sum = np.cumsum(mag)
    spec_rolloff = freqst[np.argmax(cum_sum >= threshold)] if np.any(cum_sum >= threshold) else 0.0
    
    # Return results
    results = {
        'data': data,
        'peak_freq': np.full(LS, peak_freq),
        'spec_centroid': np.full(LS, spec_centroid),
        'fdom': np.full(LS, fdom),
        'spec_slope': np.full(LS, mag_slope[0]),
        'mag_voice_slope': np.full(LS, mag_voice_slope[0]),
        'voice_slope': np.full(LS, voice_slope[0]),
        'spec_decrease': np.full(LS, spec_decrease[0]),
        'time': time_vec,
        'freqst': freqst,
        'norm_peak_freq': np.full(LS, norm_peak_freq),
        'norm_spec_centroid': np.full(LS, norm_spec_centroid),
        'norm_fdom': np.full(LS, norm_fdom),
        'hfc': np.full(LS, hfc),
        'spec_bandwidth': np.full(LS, spec_bandwidth),
        'spec_rolloff': np.full(LS, spec_rolloff),
        'mag': mag[:, np.newaxis],
        'mag_voice': mag_voice
    }
    
    return results
