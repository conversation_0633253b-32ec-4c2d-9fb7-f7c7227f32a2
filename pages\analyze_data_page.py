
"""
Analyze Data Page for the WOSS Seismic Analysis Tool.

This module handles the UI rendering for analyzing the data.
It follows the principles outlined in rules.md, particularly regarding
the separation of concerns between UI and backend logic.
"""

import streamlit as st
import numpy as np
import pandas as pd
import logging
import os
import tempfile
import segyio
import zipfile
import shutil
from io import BytesIO
from tqdm import tqdm  # Import tqdm for progress bars

# Import common modules
from common.constants import (
    APP_TITLE, AVAILABLE_OUTPUTS_SINGLE, AVAILABLE_OUTPUTS_MULTI,
    AVAILABLE_OUTPUTS_SECTION, EXPORTABLE_ATTR_DISPLAY_NAMES,
    ATTR_NAME_MAP, REVERSE_ATTR_NAME_MAP
)
from common.session_state import initialize_session_state, reset_state
from common.ui_elements import get_suggested_batch_size

# Import utility functions
from utils.data_utils import get_well_marker_pairs, get_nearest_trace_index, load_trace_sample, merge_segy_batch_files # Corrected import
from utils.general_utils import find_traces_near_polyline, parse_polyline_string
from utils.export_utils import generate_aoi_export_filename, sort_trace_indices_by_header_loader
from utils.processing import calculate_woss
from utils.visualization import plot_trace_with_descriptors, plot_descriptor_section # Visualization imports
from utils.aoi_processing import (
    validate_aoi_processing_parameters,
    get_aoi_processing_configuration,
    prepare_aoi_for_analysis,
    load_trace_data_for_aoi_analysis,
    process_aoi_analysis
)

# Helper functions for AOI export
def get_suggested_batch_size_for_export(num_unique_groups):
    """
    Suggest a reasonable batch size for export based on the number of unique groups.

    Args:
        num_unique_groups: Number of unique groups (inlines, crosslines, etc.)

    Returns:
        int: Suggested batch size for export
    """
    # Simple heuristic: for small numbers, use the number itself
    if num_unique_groups <= 10:
        return num_unique_groups

    # For larger numbers, use a fraction to keep file sizes manageable
    elif num_unique_groups <= 100:
        return max(10, num_unique_groups // 5)
    elif num_unique_groups <= 1000:
        return max(20, num_unique_groups // 10)
    else:
        return max(50, num_unique_groups // 20)

# Import GPU utility functions
from utils.gpu_utils import check_gpu_availability, log_gpu_info

# Import GPU functions if available
# Check GPU availability and import functions if available
GPU_AVAILABLE = check_gpu_availability()

if GPU_AVAILABLE:
    try:
        from utils.dlogst_spec_descriptor_gpu import (
            dlogst_spec_descriptor_gpu,
            dlogst_spec_descriptor_gpu_2d_chunked,
            dlogst_spec_descriptor_gpu_2d_chunked_mag
        )
        logging.info("Successfully imported GPU spectral descriptor functions for analysis")
        # Log GPU information for debugging
        log_gpu_info()
    except ImportError as e:
        logging.error(f"Failed to import GPU functions: {e}")
        GPU_AVAILABLE = False
        # Define error functions
        def gpu_not_available_error(*args, **kwargs):
            st.error("GPU processing is required for this application but is not available.")
            raise RuntimeError("GPU processing is required for this application but is not available.")

        dlogst_spec_descriptor_gpu = gpu_not_available_error
        dlogst_spec_descriptor_gpu_2d_chunked = gpu_not_available_error
        dlogst_spec_descriptor_gpu_2d_chunked_mag = gpu_not_available_error
else:
    # Define error functions for when GPU is not available
    def gpu_not_available_error(*args, **kwargs):
        st.error("GPU processing is required for this application but is not available.")
        raise RuntimeError("GPU processing is required for this application but is not available.")

    dlogst_spec_descriptor_gpu = gpu_not_available_error
    dlogst_spec_descriptor_gpu_2d_chunked = gpu_not_available_error
    dlogst_spec_descriptor_gpu_2d_chunked_mag = gpu_not_available_error

# Define error functions for when GPU is not available
def gpu_not_available_error(*args, **kwargs):
    st.error("GPU processing is required for this application but is not available.")
    raise RuntimeError("GPU processing is required for this application but is not available.")


        

# Note: ATTR_NAME_MAP and REVERSE_ATTR_NAME_MAP are now imported from common.constants

def render_aoi_export():
    """Handle the AOI export configuration, processing, and file generation."""
    st.header("Step 4: AOI Export Configuration")

    # Validate AOI processing parameters
    validation = validate_aoi_processing_parameters()
    if not validation['valid']:
        st.error("❌ AOI processing validation failed:")
        for error in validation['errors']:
            st.error(f"• {error}")
        if st.button("Return to Step 3"):
            st.session_state.current_step = "select_mode"
            st.rerun()
        return

    # Show warnings if any
    for warning in validation['warnings']:
        st.warning(f"⚠️ {warning}")

    # Ensure we have the necessary data
    if not st.session_state.get('selected_indices') or not st.session_state.get('header_loader'):
        st.warning("No AOI selection found. Please select an Area of Interest in Step 3.")
        if st.button("Return to Step 3"):
            st.session_state.current_step = "select_mode"
            st.rerun()
        return
    
    # Check if export is already in progress
    if st.session_state.get('export_in_progress', False):
        render_export_process()
        return

    # Check if merging is in progress
    if st.session_state.get('merging_in_progress', False):
        render_merging_process()
        return

    # Check if we need to show download UI
    if st.session_state.get('export_complete', False):
        render_download_export()
        return
        
    # Show configuration UI
    st.subheader("Configure AOI Export")
    
    # Display AOI information
    aoi_bounds = st.session_state.get('aoi_bounds', {})
    if aoi_bounds:
        st.info(f"Selected AOI: Inlines {aoi_bounds['inline_min']} to {aoi_bounds['inline_max']}, "
                f"Crosslines {aoi_bounds['xline_min']} to {aoi_bounds['xline_max']}")
    
    # Display number of traces
    st.success(f"Found {len(st.session_state.selected_indices)} traces within the AOI.")
    
    # Attribute selection
    st.subheader("Export Configuration")
    
    # Use available spectral descriptors from constants for AOI export
    # Exclude large array attributes (Magnitude Spectrogram, Magnitude * Voice) for AOI export only
    excluded_large_attrs = ["Magnitude Spectrogram", "Magnitude * Voice"]
    available_for_export_display = [
        attr for attr in EXPORTABLE_ATTR_DISPLAY_NAMES 
        if attr not in excluded_large_attrs
    ]
    
    # Select attributes to export
    select_all = st.checkbox("Select All Attributes", value=True, key="select_all_export_attrs")

    if select_all:
        default_selection = available_for_export_display
    else:
        default_selection = []

    selected_attrs_display = st.multiselect(
        "Select Attributes to Export:",
        options=available_for_export_display,
        default=default_selection
    )
    selected_attrs_internal = [ATTR_NAME_MAP[attr_name] for attr_name in selected_attrs_display]
    
    # Grouping and Batching
    grouping_type = st.selectbox(
        "Group Export Files By:",
        options=["inline", "crossline"],
        index=0,
        key="export_grouping_select"
    )
    st.session_state.export_grouping = grouping_type # Store selection
    
    # Get unique group values for the selected grouping
    headers_df = pd.DataFrame({
        'inline': st.session_state.header_loader.inlines,
        'crossline': st.session_state.header_loader.crosslines,
        'trace_idx': st.session_state.header_loader.unique_indices
    })
    
    # Filter to only selected traces
    selected_headers = headers_df[headers_df['trace_idx'].isin(st.session_state.selected_indices)]
    unique_group_values = selected_headers[grouping_type].unique()
    num_unique_groups = len(unique_group_values)
    
    # Suggest batch size based on grouping
    suggested_batch = get_suggested_batch_size_for_export(num_unique_groups)
    st.info(f"Found {num_unique_groups} unique {grouping_type} values in the AOI.")
    
    # Batch step selection
    batch_step = st.number_input(
        f"Number of {grouping_type}s per batch file (suggested: {suggested_batch}):",
        min_value=1,
        max_value=max(100, num_unique_groups),
        value=suggested_batch,
        step=1,
        key="export_batch_step_input"
    )
    st.session_state.export_batch_step = batch_step
    
    # GPU batch size for processing
    if GPU_AVAILABLE:
        st.subheader("GPU Processing Configuration")
        gpu_batch_size = st.number_input(
            "GPU Processing Batch Size (traces per chunk):",
            min_value=1,
            max_value=1000,
            value=get_suggested_batch_size()[0],
            step=1,
            help="Number of traces to process in each GPU batch. Higher values use more memory but may be faster.",
            key="gpu_batch_size_input"
        )
        st.session_state.batch_size = gpu_batch_size
    else:
        st.warning("GPU processing is not available. AOI export requires GPU acceleration.")
    
    # Disable export button if no attributes or no GPU
    disable_export_button = not GPU_AVAILABLE or not selected_attrs_internal
    
    # Start export button
    if st.button("Start Export Process", key="start_export_button", disabled=disable_export_button):
        if not selected_attrs_internal:
            st.error("Please select at least one attribute to export.")
        elif not GPU_AVAILABLE:
            st.error("GPU is required for AOI export processing and is not available.")
        else:
            # Store selected attributes for export
            st.session_state.export_attributes = selected_attrs_internal
            
            # Create output directory
            if not st.session_state.get('export_output_dir'):
                st.session_state.export_output_dir = tempfile.mkdtemp(prefix="woss_aoi_export_")
                logging.info(f"Created export output directory: {st.session_state.export_output_dir}")
            
            # Set export in progress flag
            st.session_state.export_in_progress = True
            st.session_state.export_progress = 0
            st.session_state.export_current_batch = 0
            st.session_state.export_total_batches = 0
            
            # Create tracking variables for progress
            unique_values = sorted(unique_group_values)  # Sort the unique values
            num_batches = (len(unique_values) + batch_step - 1) // batch_step  # Ceiling division
            
            # Store export configuration
            st.session_state.export_unique_values = unique_values
            st.session_state.export_total_batches = num_batches
            
            st.rerun()


def render_export_process():
    """Handle the actual export processing and generation of SEG-Y files."""
    st.header("Step 4.5: Processing AOI Export")
    
    # Check if export was actually started
    if not st.session_state.get('export_in_progress', False):
        st.warning("Export process not initiated correctly. Please configure export first.")
        if st.button("Go to Export Configuration"):
            st.session_state.export_in_progress = False
            st.rerun()
        return
    
    # Apply sorting fix: Sort selected_indices by inline then crossline for consistent spatial order
    # This ensures traces are in proper order throughout the entire export process
    if not hasattr(st.session_state, 'selected_indices_sorted') or not st.session_state.selected_indices_sorted:
        st.session_state.selected_indices = sort_trace_indices_by_header_loader(
            st.session_state.header_loader, 
            st.session_state.selected_indices
        ).tolist()
        st.session_state.selected_indices_sorted = True
        logging.info(f"Applied sorting fix: sorted {len(st.session_state.selected_indices)} traces by inline then crossline")

    # Initialize batch file tracking for merging (only once)
    if not hasattr(st.session_state, 'all_batch_files'):
        st.session_state.all_batch_files = {attr: [] for attr in st.session_state.export_attributes}
        logging.info(f"Initialized batch file tracking for attributes: {st.session_state.export_attributes}")

    # Show progress information
    st.subheader("Export Progress")
    
    # Display batch progress
    if st.session_state.export_total_batches > 0:
        progress_text = f"Batch {st.session_state.export_current_batch + 1} of {st.session_state.export_total_batches}"
        progress_bar = st.progress(st.session_state.export_progress)
        st.text(progress_text)
    
    # Process current batch if not complete
    if st.session_state.export_current_batch < st.session_state.export_total_batches:
        # Get batch information
        batch_size = st.session_state.export_batch_step
        current_batch = st.session_state.export_current_batch
        unique_values = st.session_state.export_unique_values
        grouping_type = st.session_state.export_grouping
        
        # Calculate batch range
        start_idx = current_batch * batch_size
        end_idx = min(start_idx + batch_size, len(unique_values))
        batch_values = unique_values[start_idx:end_idx]
        
        # Display batch information
        if grouping_type == "inline":
            st.info(f"Processing inlines {batch_values[0]} to {batch_values[-1]}")
        else:
            st.info(f"Processing crosslines {batch_values[0]} to {batch_values[-1]}")
        
        # Get traces for this batch
        headers_df = pd.DataFrame({
            'inline': st.session_state.header_loader.inlines,
            'crossline': st.session_state.header_loader.crosslines,
            'trace_idx': st.session_state.header_loader.unique_indices
        })
        
        # Filter to traces in selected AOI and current batch
        selected_headers = headers_df[headers_df['trace_idx'].isin(st.session_state.selected_indices)]
        batch_headers = selected_headers[selected_headers[grouping_type].isin(batch_values)]
        # Sort trace indices by inline then crossline for consistent spatial order
        batch_trace_indices = sort_trace_indices_by_header_loader(
            st.session_state.header_loader, 
            batch_headers['trace_idx'].tolist()
        )
        
        # Check if we have traces to process
        if len(batch_trace_indices) == 0:
            st.warning(f"No traces found for this batch of {grouping_type} values. Skipping.")
            # Move to next batch
            st.session_state.export_current_batch += 1
            st.session_state.export_progress = st.session_state.export_current_batch / st.session_state.export_total_batches
            st.rerun()
            return
        
        # Load trace data for the batch
        with st.spinner(f"Loading {len(batch_trace_indices)} traces for processing..."):
            try:
                # Get the number of samples from the SEG-Y file
                import segyio
                with segyio.open(st.session_state.header_loader.source_file_path, 'r', ignore_geometry=True) as segyfile:
                    num_samples = len(segyfile.samples)
                
                section_data_2d = np.zeros((len(batch_trace_indices), num_samples))
                for i, idx in enumerate(batch_trace_indices):
                    trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, idx)
                    if trace_sample is not None:
                        section_data_2d[i] = trace_sample
            except Exception as e:
                st.error(f"Error loading trace data: {e}")
                logging.error(f"Error loading trace data: {e}", exc_info=True)
                return
        
        # Process the data using GPU functions
        with st.spinner("Calculating attributes using GPU..."):
            try:
                # Set up parameters from session state
                spectral_params = st.session_state.plot_settings.copy()
                
                # Get list of attributes to calculate
                export_attributes = st.session_state.export_attributes
                
                # Add required components for WOSS if it's selected
                if "WOSS" in export_attributes and not all(attr in export_attributes for attr in ["hfc", "norm_fdom", "mag_voice_slope"]):
                    # Ensure all needed components are included
                    for attr in ["hfc", "norm_fdom", "mag_voice_slope"]:
                        if attr not in export_attributes:
                            export_attributes.append(attr)
                
                # Calculate the descriptors
                # Filter params using whitelist approach for GPU functions
                valid_gpu_params = {
                    'fmax', 'shape', 'kmax', 'int_val', 'b1', 'b2',
                    'p_bandwidth', 'roll_percent', 'batch_size', 'use_band_limited'
                }
                filtered_spectral_params = {
                    k: v for k, v in spectral_params.items()
                    if k in valid_gpu_params
                }
                all_descriptors = dlogst_spec_descriptor_gpu_2d_chunked(
                    section_data_2d,
                    st.session_state.dt,
                    batch_size=st.session_state.batch_size,
                    **filtered_spectral_params
                )

                # Filter to only the requested descriptors
                calculated_attribute_sections = {key: all_descriptors[key] for key in export_attributes
                                                if key in all_descriptors}
                
                # Add original seismic data if selected
                if "data" in export_attributes:
                    calculated_attribute_sections["data"] = section_data_2d
                
                # Calculate WOSS if needed
                if "WOSS" in export_attributes:
                    # Get HFC percentile cutoff value for WOSS calculation
                    hfc_pc = spectral_params.get('hfc_pc')
                    if hfc_pc is None:
                        # Fallback to old naming for backward compatibility
                        hfc_pc = spectral_params.get('hfc_p95', 1.0)

                    # Create WOSS parameters
                    woss_params = {
                        'hfc_p95': hfc_pc,  # WOSS function still expects 'hfc_p95' parameter name
                        'epsilon': spectral_params.get('epsilon', 1e-4),
                        'fdom_exponent': spectral_params.get('fdom_exponent', 2.0)
                    }
                    
                    # Calculate WOSS for each trace
                    woss_array = np.zeros_like(calculated_attribute_sections['hfc'])
                    for i in range(section_data_2d.shape[0]):
                        trace_components = {
                            'hfc': calculated_attribute_sections['hfc'][i],
                            'norm_fdom': calculated_attribute_sections['norm_fdom'][i],
                            'mag_voice_slope': calculated_attribute_sections['mag_voice_slope'][i]
                        }
                        woss_array[i] = calculate_woss(trace_components, woss_params)
                    
                    # Add to calculated attributes
                    calculated_attribute_sections["WOSS"] = woss_array
            except Exception as e:
                st.error(f"Error calculating attributes: {e}")
                logging.error(f"Error calculating attributes: {e}", exc_info=True)
                return
        
        # Create SEG-Y files for each attribute
        with st.spinner("Creating SEG-Y files..."):
            try:
                # Filter to only the requested export attributes (not the components required for calculation)
                export_attributes_final = [attr for attr in export_attributes if attr in st.session_state.export_attributes]
                
                # Create a file for each attribute
                created_files = []
                for attr_name in export_attributes_final:
                    # Skip if data wasn't calculated for some reason
                    if attr_name not in calculated_attribute_sections:
                        logging.warning(f"Attribute {attr_name} was not calculated. Skipping export.")
                        continue
                    
                    # Create descriptive filename
                    display_name = REVERSE_ATTR_NAME_MAP.get(attr_name, attr_name).replace(" ", "_")
                    
                    # Create a more descriptive batch filename
                    aoi_params = f"_IL{st.session_state.aoi_inline_min}-{st.session_state.aoi_inline_max}_XL{st.session_state.aoi_xline_min}-{st.session_state.aoi_xline_max}"
                    base_name = f"batch_{grouping_type}{batch_values[0]}-{batch_values[-1]}_{attr_name}"
                    filename = generate_aoi_export_filename(base_name, "sgy")
                    
                    output_path = os.path.join(st.session_state.export_output_dir, filename)

                    # Track batch file for later merging
                    st.session_state.all_batch_files[attr_name].append(output_path)

                    # Get the attribute data
                    attribute_data = calculated_attribute_sections[attr_name]
                    
                    # Create a spec for the output file
                    spec = segyio.tools.metadata(st.session_state.header_loader.segyfile)
                    spec.samples = st.session_state.header_loader.segyfile.samples
                    spec.tracecount = len(batch_trace_indices)
                    
                    # Create the SEG-Y file
                    with segyio.create(output_path, spec) as dst:
                        # Copy binary header
                        dst.bin = st.session_state.header_loader.segyfile.bin
                        
                        # Copy text header if available
                        try:
                            dst.text[0] = st.session_state.header_loader.segyfile.text[0]
                        except:
                            pass
                        
                        # Write each trace with its header
                        for i, trace_idx in enumerate(batch_trace_indices):
                            # Copy header from original file using the trace index directly
                            # trace_idx is already the original trace index in the SEG-Y file
                            dst.header[i] = st.session_state.header_loader.segyfile.header[trace_idx]
                            
                            # Write the calculated attribute data as the trace
                            dst.trace[i] = attribute_data[i]
                    
                    created_files.append(output_path)
                    logging.info(f"Created SEG-Y file: {output_path}")
                
                # Store information about created files
                if not hasattr(st.session_state, 'created_files_list'):
                    st.session_state.created_files_list = []
                st.session_state.created_files_list.extend(created_files)
                
            except Exception as e:
                st.error(f"Error creating SEG-Y files: {e}")
                logging.error(f"Error creating SEG-Y files: {e}", exc_info=True)
                return
        
        # Move to next batch
        st.session_state.export_current_batch += 1
        st.session_state.export_progress = st.session_state.export_current_batch / st.session_state.export_total_batches
        
        # Check if we're done
        if st.session_state.export_current_batch >= st.session_state.export_total_batches:
            st.session_state.export_in_progress = False

            # Check if merging is needed (multiple batches created)
            if st.session_state.export_total_batches > 1:
                # Initialize merging process
                st.session_state.merging_in_progress = True
                st.session_state.merge_current_attr = 0
                st.session_state.merge_total_attrs = len(st.session_state.export_attributes)
                st.session_state.merged_files_list = []
                st.session_state.merge_logs = []
                st.success("Batch processing complete! Starting file merging...")
                logging.info(f"Starting merging process for {st.session_state.merge_total_attrs} attributes")
            else:
                # Single batch - no merging needed
                st.session_state.export_complete = True
                st.success("Export processing complete! Ready for download.")
        
        # Rerun to process next batch or show download UI
        st.rerun()
    
    # Show cancel button
    if st.button("Cancel Export", key="cancel_export_button"):
        # Clean up temporary directory
        if st.session_state.get('export_output_dir') and os.path.exists(st.session_state.export_output_dir):
            try:
                shutil.rmtree(st.session_state.export_output_dir)
                logging.info(f"Removed export directory: {st.session_state.export_output_dir}")
            except Exception as e:
                logging.error(f"Error removing export directory: {e}")
        
        # Reset export state
        st.session_state.export_in_progress = False
        st.session_state.export_complete = False
        st.session_state.merging_in_progress = False
        if hasattr(st.session_state, 'created_files_list'):
            del st.session_state.created_files_list
        if hasattr(st.session_state, 'all_batch_files'):
            del st.session_state.all_batch_files
        if hasattr(st.session_state, 'merged_files_list'):
            del st.session_state.merged_files_list
        
        st.success("Export cancelled.")
        st.rerun()


def render_merging_process():
    """Handle the merging of batch files into consolidated SEG-Y volumes."""
    st.header("Step 4.7: Merging Batch Files")

    # Show merging progress
    if st.session_state.merge_total_attrs > 0:
        progress = st.session_state.merge_current_attr / st.session_state.merge_total_attrs
        st.progress(progress, text=f"Merging attribute {st.session_state.merge_current_attr + 1} of {st.session_state.merge_total_attrs}")

    # Get current attribute to merge
    export_attributes_final = st.session_state.export_attributes
    current_attr = export_attributes_final[st.session_state.merge_current_attr]

    # Get batch files for current attribute
    batch_files = st.session_state.all_batch_files[current_attr]

    # Create merged filename with AOI parameters
    aoi_params = f"_IL{st.session_state.aoi_inline_min}-{st.session_state.aoi_inline_max}_XL{st.session_state.aoi_xline_min}-{st.session_state.aoi_xline_max}"
    merged_filename = f"{st.session_state.segy_file_info['name']}_{current_attr}{aoi_params}.sgy"
    merged_path = os.path.join(st.session_state.export_output_dir, merged_filename)

    # Display current attribute being processed
    st.info(f"Merging {len(batch_files)} batch files for attribute: {current_attr}")

    # Perform merging
    with st.spinner(f"Merging {len(batch_files)} batch files for {current_attr}..."):
        try:
            merge_result = merge_segy_batch_files(batch_files, merged_path)

            # Log merge result
            st.session_state.merge_logs.append(f"{current_attr}: {merge_result}")
            logging.info(f"Merge result for {current_attr}: {merge_result}")

            # Update merged files list if successful
            if os.path.exists(merged_path):
                st.session_state.merged_files_list.append(merged_path)
                st.success(f"Successfully merged {current_attr}")

                # Clean up batch files after successful merge
                for batch_file in batch_files:
                    try:
                        if os.path.exists(batch_file):
                            os.remove(batch_file)
                            logging.info(f"Cleaned up batch file: {batch_file}")
                    except Exception as e:
                        logging.warning(f"Could not remove batch file {batch_file}: {e}")
            else:
                st.error(f"Merging failed for {current_attr}. Output file not created.")
                logging.error(f"Merging failed for {current_attr}. Output file not found: {merged_path}")

        except Exception as e:
            st.error(f"Error during merging for {current_attr}: {e}")
            logging.error(f"Error during merging for {current_attr}: {e}", exc_info=True)

    # Move to next attribute
    st.session_state.merge_current_attr += 1

    # Check if merging is complete
    if st.session_state.merge_current_attr >= st.session_state.merge_total_attrs:
        st.session_state.merging_in_progress = False
        st.session_state.export_complete = True
        st.session_state.created_files_list = st.session_state.merged_files_list
        st.success("Merging complete! Ready for download.")
        logging.info(f"Merging process completed. {len(st.session_state.merged_files_list)} merged files created.")

    # Rerun to process next attribute or show download UI
    st.rerun()

    # Show cancel button
    if st.button("Cancel Merging", key="cancel_merging_button"):
        # Clean up temporary directory
        if st.session_state.get('export_output_dir') and os.path.exists(st.session_state.export_output_dir):
            try:
                shutil.rmtree(st.session_state.export_output_dir)
                logging.info(f"Removed export directory: {st.session_state.export_output_dir}")
            except Exception as e:
                logging.error(f"Error removing export directory: {e}")

        # Reset all export and merging state
        st.session_state.export_in_progress = False
        st.session_state.export_complete = False
        st.session_state.merging_in_progress = False
        if hasattr(st.session_state, 'created_files_list'):
            del st.session_state.created_files_list
        if hasattr(st.session_state, 'all_batch_files'):
            del st.session_state.all_batch_files
        if hasattr(st.session_state, 'merged_files_list'):
            del st.session_state.merged_files_list

        st.success("Merging cancelled.")
        st.rerun()


def render_download_export():
    """Provide a download interface for the exported files."""
    st.header("Step 4.6: Download Export Files")
    
    # Check if we have files to download
    if not hasattr(st.session_state, 'created_files_list') or not st.session_state.created_files_list:
        st.warning("No export files found. Please run the export process first.")
        if st.button("Return to Export Configuration"):
            st.session_state.export_complete = False
            st.rerun()
        return
    
    # Show success message
    st.success(f"Successfully created {len(st.session_state.created_files_list)} SEG-Y files!")
    
    # Create a zip file of all exported files
    try:
        zip_buffer = BytesIO()
        # Use a base filename derived from the original SEG-Y if possible
        base_zip_filename = generate_aoi_export_filename(
            f"{st.session_state.segy_file_info['name']}_AOI_Export", "zip"
        )
        
        # Create the zip file
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for file_path in st.session_state.created_files_list:
                if os.path.exists(file_path):
                    # Add file to zip with just the filename (not the full path)
                    zip_file.write(file_path, os.path.basename(file_path))
                else:
                    logging.warning(f"File not found for zip: {file_path}")
        
        # Reset the buffer position to the beginning
        zip_buffer.seek(0)
        
        # Provide download button
        st.download_button(
            label="📥 Download All Files as ZIP",
            data=zip_buffer,
            file_name=base_zip_filename,
            mime="application/zip",
            key="download_zip_button"
        )
        
        # Show information about the exported files
        st.subheader("Exported Files")
        for i, file_path in enumerate(st.session_state.created_files_list):
            st.text(f"{i+1}. {os.path.basename(file_path)}")
        
    except Exception as e:
        st.error(f"Error creating zip file: {e}")
        logging.error(f"Error creating zip file: {e}", exc_info=True)
    
    # Add buttons for further actions
    col1, col2 = st.columns(2)
    with col1:
        if st.button("Start New Export", key="new_export_button"):
            # Clean up directory but keep loaded data
            if st.session_state.get('export_output_dir') and os.path.exists(st.session_state.export_output_dir):
                try:
                    shutil.rmtree(st.session_state.export_output_dir)
                    logging.info(f"Removed export directory: {st.session_state.export_output_dir}")
                except Exception as e:
                    logging.error(f"Error removing export directory: {e}")
            
            # Reset export state but keep selection mode
            st.session_state.export_in_progress = False
            st.session_state.export_complete = False
            st.session_state.merging_in_progress = False
            if hasattr(st.session_state, 'created_files_list'):
                del st.session_state.created_files_list
            if hasattr(st.session_state, 'export_output_dir'):
                del st.session_state.export_output_dir
            if hasattr(st.session_state, 'all_batch_files'):
                del st.session_state.all_batch_files
            if hasattr(st.session_state, 'merged_files_list'):
                del st.session_state.merged_files_list
            if hasattr(st.session_state, 'merge_logs'):
                del st.session_state.merge_logs
            
            st.rerun()
    
    with col2:
        if st.button("Start New Analysis", key="start_new_analysis_button"):
            # Full reset of the application state
            reset_state()
            st.success("Starting new analysis. All temporary data has been cleared.")
            st.rerun()


def render_line_analysis(line_type='inline'):
    """Unified function to handle both inline and crossline analysis."""

    # Determine which line we're analyzing
    if line_type == 'inline':
        selected_value = st.session_state.selected_inline
        line_name = "inline"
        loaded_flag = 'traces_loaded_inline'
        calculated_flag = 'inline_descriptors_calculated'
    else:
        selected_value = st.session_state.selected_crossline
        line_name = "crossline"
        loaded_flag = 'traces_loaded_crossline'
        calculated_flag = 'crossline_descriptors_calculated'

    st.subheader(f"Analysis for {line_name.capitalize()} {selected_value}")

    # Check if traces need to be loaded
    if not st.session_state.get(loaded_flag, False):
        with st.spinner(f"Loading traces for {line_name} {selected_value}..."):
            loaded_trace_data = []
            try:
                for trace_idx in st.session_state.selected_indices:
                    trace_sample = load_trace_sample(
                        st.session_state.header_loader.source_file_path,
                        trace_idx
                    )
                    loaded_trace_data.append({
                        'trace_sample': trace_sample,
                        'trace_idx': trace_idx
                    })

                # Store loaded data
                st.session_state.loaded_trace_data = loaded_trace_data
                st.session_state[loaded_flag] = True
                st.success(f"Successfully loaded {len(loaded_trace_data)} traces.")

            except Exception as e:
                st.error(f"Error loading traces: {e}")
                logging.error(f"Error loading traces for {line_name} {selected_value}: {e}", exc_info=True)
                return

    # Display loaded trace information
    st.info(f"{len(st.session_state.loaded_trace_data)} traces loaded for {line_name} {selected_value}")

    # Select outputs
    st.subheader("Select Outputs")
    available_outputs = AVAILABLE_OUTPUTS_SECTION

    default_outputs = st.session_state.get('selected_outputs', ["Input Signal", "WOSS"])
    default_outputs = [output for output in default_outputs if output in available_outputs]

    st.session_state.selected_outputs = st.multiselect(
        "Select outputs to display:",
        options=available_outputs,
        default=default_outputs,
        key=f"{line_name}_outputs_multiselect"
    )

    if not st.session_state.selected_outputs:
        st.warning("Please select at least one output.")
        return

    # Calculate descriptors button
    if st.button("Calculate Descriptors", key=f"calc_desc_{line_name}"):
        calculate_line_descriptors(line_type)

    # Show results if calculated
    if st.session_state.get(calculated_flag, False):
        display_line_results(line_type)

def calculate_line_descriptors(line_type):
    """Calculate descriptors for inline or crossline analysis."""
    line_name = "inline" if line_type == 'inline' else "crossline"
    calculated_flag = f'{line_name}_descriptors_calculated'

    with st.spinner(f"Calculating descriptors for {line_name}..."):
        try:
            # Use the centralized GPU processing function
            from utils.gpu_utils import process_traces_gpu

            # Prepare traces array
            trace_data_list = [item['trace_sample'] for item in st.session_state.loaded_trace_data]
            traces_array = np.stack(trace_data_list, axis=0)  # traces x samples

            # Get descriptor settings
            descriptor_settings = st.session_state.get('plot_settings', {})
            descriptor_settings.update({
                'dt': st.session_state.get('dt', 0.004),
                'hfc_p95': st.session_state.plot_settings.get('hfc_p95', 1.0),
                'epsilon': st.session_state.plot_settings.get('epsilon', 1e-4),
                'fdom_exponent': st.session_state.plot_settings.get('fdom_exponent', 2.0)
            })

            # Map display names to internal names
            output_mapping = {
                "Input Signal": "data",
                "Normalized dominant frequencies": "norm_fdom",
                "Spectral Slope": "spec_slope",
                "Spectral Bandwidth": "spec_bandwidth",
                "Spectral Rolloff": "spec_rolloff",
                "Mag*Voice Slope": "mag_voice_slope",
                "Spectral Decrease": "spec_decrease",
                "HFC": "hfc",
                "WOSS": "WOSS"
            }

            # Get internal names for selected outputs
            outputs_to_calculate = [output_mapping[out] for out in st.session_state.selected_outputs
                                  if out in output_mapping]

            # Add original data if selected
            if "Input Signal" in st.session_state.selected_outputs:
                outputs_to_calculate.append("data")

            # Process with GPU
            if st.session_state.get('GPU_AVAILABLE', False) and st.session_state.batch_size:
                calculated_descriptors = process_traces_gpu(
                    traces_array,
                    descriptor_settings['dt'],
                    st.session_state.batch_size,
                    descriptor_settings,
                    outputs_to_calculate
                )

                # Add original data if requested
                if "data" in outputs_to_calculate:
                    calculated_descriptors["data"] = traces_array

                # Store results
                st.session_state.calculated_descriptors = calculated_descriptors
                st.session_state[calculated_flag] = True
                st.success("Descriptor calculation completed successfully!")

            else:
                st.error("GPU processing is required but not available.")

        except Exception as e:
            st.error(f"Error calculating descriptors: {e}")
            logging.error(f"Error calculating descriptors for {line_name}: {e}", exc_info=True)

def display_line_results(line_type):
    """Display results for inline or crossline analysis."""
    line_name = "inline" if line_type == 'inline' else "crossline"
    selected_value = st.session_state.selected_inline if line_type == 'inline' else st.session_state.selected_crossline

    st.subheader("Analysis Results")
    st.success("Descriptors calculated successfully!")

    # Show basic statistics
    if st.session_state.calculated_descriptors:
        st.write("**Available descriptors:**")
        for key in st.session_state.calculated_descriptors.keys():
            data_shape = st.session_state.calculated_descriptors[key].shape
            st.write(f"- {key}: {data_shape}")

    # Add export button
    if st.button(f"Export {line_name.capitalize()} Results", key=f"export_{line_name}"):
        export_inline_results(line_type)

def export_inline_results(line_type):
    """Export inline/crossline analysis results for Step 5 visualization."""
    line_name = "inline" if line_type == 'inline' else "crossline"

    try:
        # Get the calculated descriptors
        calculated_descriptors = st.session_state.get('calculated_descriptors', {})
        if not calculated_descriptors:
            st.error("No calculated descriptors found to export.")
            return

        # Get trace data and metadata
        trace_data = st.session_state.get('loaded_trace_data', [])
        if not trace_data:
            st.error("No trace data found to export.")
            return

        # Prepare export data structure
        export_data = {
            'mode': f"Single {line_name} (all {'crosslines' if line_type == 'inline' else 'inlines'})",
            'line_type': line_type,
            'line_value': st.session_state.selected_inline if line_type == 'inline' else st.session_state.selected_crossline,
            'descriptors': calculated_descriptors,
            'trace_data': trace_data,
            'trace_indices': [item['trace_idx'] for item in trace_data],
            'metadata': {
                'dt': st.session_state.get('dt', 0.004),
                'processing_params': st.session_state.get('plot_settings', {}),
                'export_timestamp': pd.Timestamp.now().isoformat()
            }
        }

        # Store export data for Step 5
        st.session_state.exported_line_analysis = export_data
        st.session_state.line_analysis_exported = True
        st.session_state.analysis_complete = True  # Mark analysis as complete for Step 5

        st.success(f"{line_name.capitalize()} analysis results exported successfully!")
        st.info("Results are now available for visualization in Step 5: View Analysis Results")

        # Provide option to go to Step 5
        col1, col2 = st.columns(2)
        with col1:
            if st.button("Go to Step 5: View Analysis Results", key=f"goto_step5_{line_name}"):
                st.session_state.current_step = "view_results"
                st.rerun()
        with col2:
            if st.button("Continue Analysis", key=f"continue_analysis_{line_name}"):
                st.rerun()

    except Exception as e:
        st.error(f"Error exporting {line_name} results: {e}")
        logging.error(f"Error exporting {line_name} results: {e}", exc_info=True)

def display_polyline_results():
    """Display results for polyline analysis."""
    st.markdown("---")
    st.subheader("Analysis Results")
    st.success("Descriptors calculated successfully!")

    # Show basic statistics
    if st.session_state.calculated_descriptors:
        st.write("**Available descriptors:**")
        for key in st.session_state.calculated_descriptors.keys():
            data_shape = st.session_state.calculated_descriptors[key].shape
            st.write(f"- {key}: {data_shape}")

    # Add export button
    if st.button("Export Polyline Results", key="export_polyline"):
        export_polyline_results()

def export_polyline_results():
    """Export polyline analysis results for Step 5 visualization."""
    try:
        # Get the calculated descriptors
        calculated_descriptors = st.session_state.get('calculated_descriptors', {})
        if not calculated_descriptors:
            st.error("No calculated descriptors found to export.")
            return

        # Get trace data and metadata
        trace_data = st.session_state.get('loaded_trace_data', [])
        if not trace_data:
            st.error("No trace data found to export.")
            return

        # Get polyline file information
        polyline_file_info = st.session_state.get('polyline_file_info', {})
        polyline_file_name = polyline_file_info.get('name', 'Unknown Polyline')

        # Prepare export data structure
        export_data = {
            'mode': "By Polyline File Import",
            'line_type': 'polyline',
            'line_value': polyline_file_name,
            'descriptors': calculated_descriptors,
            'trace_data': trace_data,
            'trace_indices': [item.get('trace_index', item.get('trace_idx', i)) for i, item in enumerate(trace_data)],
            'metadata': {
                'dt': st.session_state.get('dt', 0.004),
                'processing_params': st.session_state.get('plot_settings', {}),
                'export_timestamp': pd.Timestamp.now().isoformat(),
                'polyline_file': polyline_file_name,
                'polyline_tolerance': st.session_state.get('polyline_tolerance', 0.0),
                'selected_indices': st.session_state.get('selected_indices', [])
            }
        }

        # Store export data for Step 5
        st.session_state.exported_line_analysis = export_data
        st.session_state.line_analysis_exported = True
        st.session_state.analysis_complete = True  # Mark analysis as complete for Step 5

        st.success("Polyline analysis results exported successfully!")
        st.info("Results are now available for visualization in Step 5: View Analysis Results")

        # Provide option to go to Step 5
        col1, col2 = st.columns(2)
        with col1:
            if st.button("Go to Step 5: View Analysis Results", key="goto_step5_polyline"):
                st.session_state.current_step = "view_results"
                st.rerun()
        with col2:
            if st.button("Continue Analysis", key="continue_analysis_polyline"):
                st.rerun()

    except Exception as e:
        st.error(f"Error exporting polyline results: {e}")
        logging.error(f"Error exporting polyline results: {e}", exc_info=True)

def export_well_marker_results():
    """Export well marker analysis results for Step 5 visualization."""
    try:
        # Get the calculated descriptors from individual well analysis results
        individual_results = st.session_state.get('individual_well_analysis_results', [])
        if not individual_results:
            st.error("No well marker analysis results found to export.")
            return

        # Get trace data and metadata
        trace_data = st.session_state.get('loaded_trace_data', [])
        if not trace_data:
            st.error("No trace data found to export.")
            return

        # Convert individual results to descriptors format compatible with Step 5
        calculated_descriptors = {}

        # Extract all available descriptor types from the first valid result
        descriptor_types = set()
        for result in individual_results:
            if 'descriptors' in result and result['descriptors']:
                descriptor_types.update(result['descriptors'].keys())

        # Initialize descriptor arrays
        for desc_type in descriptor_types:
            calculated_descriptors[desc_type] = []

        # Collect descriptors from all wells
        for result in individual_results:
            if 'descriptors' in result and result['descriptors']:
                for desc_type in descriptor_types:
                    if desc_type in result['descriptors']:
                        calculated_descriptors[desc_type].append(result['descriptors'][desc_type])
                    else:
                        # Fill missing descriptors with None or appropriate default
                        calculated_descriptors[desc_type].append(None)

        # Get well marker information
        selected_pairs = st.session_state.get('selected_well_marker_pairs', [])
        if selected_pairs:
            if isinstance(selected_pairs[0], dict):
                # Dictionary format: extract Well and Surface keys
                well_marker_names = [f"{pair.get('Well', 'Unknown')} - {pair.get('Surface', 'Unknown')}" for pair in selected_pairs]
            else:
                # String format: use directly
                well_marker_names = [str(pair) for pair in selected_pairs]
        else:
            well_marker_names = []

        # Prepare export data structure
        export_data = {
            'mode': st.session_state.get('selection_mode', 'By well markers'),
            'line_type': 'well_markers',
            'line_value': f"{len(well_marker_names)} Well Markers",
            'descriptors': calculated_descriptors,
            'trace_data': trace_data,
            'trace_indices': st.session_state.get('selected_indices', []),
            'metadata': {
                'dt': st.session_state.get('dt', 0.004),
                'processing_params': st.session_state.get('plot_settings', {}),
                'export_timestamp': pd.Timestamp.now().isoformat(),
                'well_marker_pairs': well_marker_names,
                'analysis_mode': st.session_state.get('well_analysis_sub_option', 'Individual Wells Analysis'),
                'selected_indices': st.session_state.get('selected_indices', [])
            }
        }

        # Store export data for Step 5
        st.session_state.exported_line_analysis = export_data
        st.session_state.line_analysis_exported = True
        st.session_state.analysis_complete = True  # Mark analysis as complete for Step 5

        st.success("Well marker analysis results exported successfully!")
        st.info("Results are now available for visualization in Step 5: View Analysis Results")

        # Provide option to go to Step 5
        col1, col2 = st.columns(2)
        with col1:
            if st.button("Go to Step 5: View Analysis Results", key="goto_step5_well_markers"):
                st.session_state.current_step = "view_results"
                st.rerun()
        with col2:
            if st.button("Continue Analysis", key="continue_analysis_well_markers"):
                st.rerun()

    except Exception as e:
        st.error(f"Error exporting well marker results: {e}")
        logging.error(f"Error exporting well marker results: {e}", exc_info=True)

def render():
    """Render the analyze data page UI."""
    # Initialize session state if needed
    initialize_session_state()

    st.header("Step 4: Analyze Data")
    st.sidebar.header("Analysis Options")

    # Check if data is loaded
    if not st.session_state.header_loader:
        st.warning("Please load data first.")
        st.session_state.current_step = "load_data"
        st.rerun()
        return

    # Initialize precomputed_data as None
    precomputed_data = None

    # Check if we have precomputed data (from previous implementation)
    if st.session_state.get('precomputed_data_output') is not None:
        precomputed_data = st.session_state.get('precomputed_data_output')
        logging.info("Using existing precomputed data")
    else:
        # If no precomputed data, we'll work with raw data directly
        logging.info("No precomputed data found, will work with raw data directly")

    # Check if we have the HFC percentile cutoff value for WOSS calculation
    hfc_pc_value = st.session_state.plot_settings.get('hfc_pc')
    if hfc_pc_value is None:
        # Fallback to old naming for backward compatibility
        hfc_pc_value = st.session_state.plot_settings.get('hfc_p95')

    if hfc_pc_value is None:
        st.warning("HFC percentile cutoff value not found in session state. WOSS calculation might be inaccurate.")
        # We'll continue anyway, as calculate_woss has a fallback mechanism
    else:
        hfc_percentile = st.session_state.plot_settings.get('hfc_percentile', 95.0)
        st.info(f"Using HFC p{int(hfc_percentile)} cutoff value: {hfc_pc_value:.3f} for WOSS calculation.")

    # Store GPU availability in session state
    st.session_state.GPU_AVAILABLE = GPU_AVAILABLE
    
    # Handle AOI mode specially - use separate export flow
    if st.session_state.get('selection_mode') == "By inline/crossline section (AOI)":
        render_aoi_export()
        return

    # Well Markers Mode
    if st.session_state.selection_mode == "By well markers":
        st.subheader("Selected Area Information") # Add subheader for clarity
        if st.session_state.well_df is not None and not st.session_state.well_df.empty:
            # Display information about the selected area from session state
            if st.session_state.get('selected_well_markers'):
                 st.write(f"Selected well markers: {', '.join(st.session_state.selected_well_markers)}")
            if st.session_state.get('selected_well_marker_pairs'):
                # Handle both string and dictionary formats for selected_well_marker_pairs
                pairs = st.session_state.selected_well_marker_pairs
                if pairs:
                    if isinstance(pairs[0], dict):
                        # Dictionary format: extract Well and Surface keys
                        pair_strings = [f"{pair.get('Well', 'Unknown')} - {pair.get('Surface', 'Unknown')}" for pair in pairs]
                    else:
                        # String format: use directly
                        pair_strings = [str(pair) for pair in pairs]
                    st.write(f"Selected well-marker pairs: {', '.join(pair_strings)}")
            if st.session_state.get('selected_indices'):
                 st.write(f"Number of selected traces: {len(st.session_state.selected_indices)}")

            # Use the loaded trace data from select_area_page.py
            loaded_trace_data = st.session_state.get('loaded_trace_data', [])

            if loaded_trace_data:
                st.success(f"Loaded {len(loaded_trace_data)} traces for analysis.")
            else:
                st.warning("No traces loaded for analysis from the selected well-marker pairs.")

            # Add a button to calculate descriptors
            st.markdown("---")
            st.markdown("### Next Steps:")
            st.markdown("1. Click **Calculate Descriptors** to process the selected traces and compute signal descriptors")
            st.markdown("2. Review the statistical summary that will appear")
            st.markdown("3. Click **View Results** to proceed to the results page for visualization and analysis options")

            if not st.session_state.get('GPU_AVAILABLE', False):
                st.error("GPU processing is required for this analysis mode, but no GPU is available. Please check your system configuration.")
            elif st.button("Calculate Descriptors", key="calculate_descriptors_button", help="Process the selected traces and compute spectral descriptors"):
                if not loaded_trace_data:
                    st.warning("No traces loaded to calculate descriptors.")
                    return

                # Get the selected analysis sub-option
                analysis_sub_option = st.session_state.get("well_analysis_sub_option", "Grouping Well Analysis") # Default to grouping if not set

                if analysis_sub_option == "Plot Individual Wells Analysis":
                    st.session_state.individual_well_analysis_results = [] # Initialize
                    # Placeholder for individual well analysis logic
                    # This will iterate through loaded_trace_data, calculate descriptors and stats for each
                    # and append to st.session_state.individual_well_analysis_results
                    # For now, just set analysis_complete to True to allow UI to proceed
                    
                    # --- Start of Detailed Individual Well Analysis Logic ---
                    num_traces = len(loaded_trace_data)
                    skipped_traces_count = 0
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    status_text.text("Initializing individual well descriptor calculation...")

                    # Ensure plot_settings time/frequency limits are set once before the loop if they affect all traces
                    # This logic is similar to backup/app_ref.py:1543-1555
                    if 'Time (Y-axis)' not in st.session_state.plot_settings and 'time_min' in st.session_state.plot_settings:
                        st.session_state.plot_settings['Time (Y-axis)'] = (
                            st.session_state.plot_settings.get('time_min', 0.0),
                            st.session_state.plot_settings.get('time_max', 4.0) # Assuming dt is 4ms, so 4s is 1000 samples
                        )
                    if 'Frequency' not in st.session_state.plot_settings and 'freq_min' in st.session_state.plot_settings:
                        nyquist_freq = 0.5 / st.session_state.dt if hasattr(st.session_state, 'dt') and st.session_state.dt > 0 else 125.0
                        st.session_state.plot_settings['Frequency'] = (
                            st.session_state.plot_settings.get('freq_min', 0.0),
                            st.session_state.plot_settings.get('freq_max', nyquist_freq)
                        )

                    for i, trace_data in enumerate(loaded_trace_data):
                        well_marker_name = trace_data.get('well_marker_name')  # Don't default to 'Unknown'
                        display_name = well_marker_name if well_marker_name else f"Trace {trace_data.get('trace_idx', i)}"
                        status_text.text(f"Processing trace {i+1}/{num_traces} for {display_name} (Individual)...")
                        
                        descriptor = {}
                        individual_trace_stats = {}

                        try:
                            # 1. Descriptor Calculation (mirroring backup/app_ref.py:1530-1579)
                            base_plot_settings = st.session_state.get('plot_settings', {})
                            descriptor_settings = {
                                'use_band_limited': base_plot_settings.get('use_band_limited', False),
                                'shape': base_plot_settings.get('shape', 0.35), # Current app uses float, ref used string. Align if necessary.
                                'kmax': base_plot_settings.get('kmax', 120.0),
                                'int_val': base_plot_settings.get('int_val', 35.0),
                                'b1': base_plot_settings.get('b1', 5.0),
                                'b2': base_plot_settings.get('b2', 40.0),
                                'p_bandwidth': base_plot_settings.get('p_bandwidth', 2.0),
                                'roll_percent': base_plot_settings.get('roll_percent', 0.80),
                                # Epsilon is primarily for WOSS, not directly for dlogst_spec_descriptor_gpu
                            }
                            
                            # Filter params using whitelist approach for GPU functions
                            valid_gpu_params = {
                                'fmax', 'shape', 'kmax', 'int_val', 'b1', 'b2',
                                'p_bandwidth', 'roll_percent', 'use_band_limited'
                            }
                            gpu_descriptor_settings = {
                                k: v for k, v in descriptor_settings.items()
                                if k in valid_gpu_params
                            }

                            trace_sample = trace_data['trace_sample']
                            if trace_sample is None or len(trace_sample) == 0:
                                raise ValueError("Invalid trace sample: empty or None")

                            # Validate trace sample data
                            if not isinstance(trace_sample, np.ndarray):
                                trace_sample = np.array(trace_sample, dtype=np.float32)

                            if np.all(np.isnan(trace_sample)):
                                raise ValueError("Trace sample contains only NaN values")

                            if np.all(trace_sample == 0):
                                logging.warning(f"Trace sample for {display_name} contains only zeros")

                            fmax_value = len(trace_sample) // 2 # As in current analyze_data_page.py

                            # Log detailed information for debugging
                            logging.info(f"Processing {display_name}: trace_length={len(trace_sample)}, fmax={fmax_value}, dt={st.session_state.dt}")
                            logging.info(f"GPU settings: {gpu_descriptor_settings}")

                            descriptor = dlogst_spec_descriptor_gpu(
                                trace_sample,
                                st.session_state.dt,
                                fmax=fmax_value, # Keep fmax based on current implementation
                                **gpu_descriptor_settings
                            )

                            # Enhanced validation of GPU function return value
                            if descriptor is None:
                                raise ValueError("GPU function returned None instead of descriptor dictionary.")

                            if not isinstance(descriptor, dict):
                                raise ValueError(f"GPU function returned {type(descriptor).__name__} instead of dictionary: {descriptor}")

                            if isinstance(descriptor, str):
                                raise ValueError(f"GPU function returned string error: {descriptor}")

                            if 'data' not in descriptor:
                                available_keys = list(descriptor.keys()) if isinstance(descriptor, dict) else "N/A"
                                raise ValueError(f"GPU descriptor missing 'data' key. Available keys: {available_keys}")

                            if not isinstance(descriptor['data'], np.ndarray):
                                raise ValueError(f"GPU descriptor 'data' is not a numpy array: {type(descriptor['data'])}")

                            # Additional validation: ensure key spectral descriptors are present and numeric
                            expected_numeric_keys = ['peak_freq', 'spec_centroid', 'fdom', 'norm_fdom', 'hfc']
                            missing_keys = []
                            invalid_keys = []

                            for key in expected_numeric_keys:
                                if key not in descriptor:
                                    missing_keys.append(key)
                                else:
                                    value = descriptor[key]
                                    if not isinstance(value, (np.ndarray, int, float, np.number)):
                                        invalid_keys.append(f"{key}:{type(value).__name__}")

                            if missing_keys:
                                logging.warning(f"GPU descriptor missing expected keys: {missing_keys}")

                            if invalid_keys:
                                logging.warning(f"GPU descriptor has non-numeric values: {invalid_keys}")
                                # Don't raise error for this, just log warning

                            # Log successful calculation and validation
                            logging.info(f"Successfully calculated and validated descriptors for {well_marker_name}. Keys: {list(descriptor.keys())}, data shape: {descriptor['data'].shape}")

                            # 2. Statistics Calculation (mirroring backup/app_ref.py:1585-1779 for this single descriptor)
                            # Define descriptor_mapping for this analysis
                            descriptor_mapping = {
                                # Signal data
                                "Input Signal": "data",

                                # Spectrograms and time-frequency representations
                                "Magnitude Spectrogram": "tf_map",
                                "Magnitude * Voice": "mag_voice",

                                # Frequency-related descriptors
                                "Normalized dominant frequencies": "norm_fdom",
                                "Normalized Dominant Frequency": "norm_fdom",
                                "Dominant Frequency": "fdom",
                                "Peak Frequency": "peak_freq",
                                "Spectral Centroid": "spec_centroid",

                                # Slope-related descriptors
                                "Spectral Slope": "spec_slope",
                                "Mag*Voice Slope": "mag_voice_slope",
                                "Voice Slope": "voice_slope",
                                "Slope Magnitude": "mag_voice_slope",

                                # Bandwidth and spectral shape descriptors
                                "Spectral Bandwidth": "spec_bandwidth",
                                "Spectral Rolloff": "spec_rolloff",
                                "Spectral Decrease": "spec_decrease",
                                "Normalized Spectral Decrease": "spec_decrease",

                                # High-frequency content
                                "HFC": "hfc",
                                "Normalized HFC": "hfc",  # We'll normalize this manually

                                # Composite descriptors
                                "WOSS": "WOSS"
                            }
                            
                            # Initialize stats for this specific trace
                            current_trace_stats_values = {}
                            for output_key in AVAILABLE_OUTPUTS_SINGLE + AVAILABLE_OUTPUTS_MULTI: # Use combined list as per reference
                                current_trace_stats_values[output_key] = {'values': []}
                            current_trace_stats_values['Normalized HFC'] = {'values': []} # Custom stat
                            current_trace_stats_values['Normalized Spectral Decrease'] = {'values': []}
                            # Add specific keys for spectrogram stats if they are calculated and stored per trace
                            for spec_stat_base in ["Magnitude Spectrogram", "Magnitude * Voice"]:
                                for stat_suffix in ["_max", "_median", "_std"]:
                                    current_trace_stats_values[spec_stat_base + stat_suffix] = {'values': []}


                            # Process the single descriptor for this trace
                            if descriptor and not descriptor.get('error'):
                                for output_name, values_dict in current_trace_stats_values.items():
                                    internal_key = descriptor_mapping.get(output_name)
                                    
                                    # Handle spectrogram aggregate stats names correctly
                                    is_spec_agg_stat = False
                                    for spec_base in ["Magnitude Spectrogram", "Magnitude * Voice"]:
                                        if output_name.startswith(spec_base + "_"):
                                            internal_key = descriptor_mapping.get(spec_base) # Base key for data
                                            is_spec_agg_stat = True
                                            break
                                    
                                    if not internal_key or internal_key not in descriptor:
                                        continue

                                    desc_value = descriptor[internal_key]

                                    if output_name == "Magnitude Spectrogram" and isinstance(desc_value, np.ndarray) and desc_value.ndim == 2:
                                        mean_val = np.mean(desc_value)
                                        if not np.isnan(mean_val): values_dict['values'].append(mean_val)
                                        if not np.isnan(np.max(desc_value)): current_trace_stats_values["Magnitude Spectrogram_max"]['values'].append(np.max(desc_value))
                                        if not np.isnan(np.median(desc_value)): current_trace_stats_values["Magnitude Spectrogram_median"]['values'].append(np.median(desc_value))
                                        if not np.isnan(np.std(desc_value)): current_trace_stats_values["Magnitude Spectrogram_std"]['values'].append(np.std(desc_value))
                                    elif output_name == "Magnitude * Voice" and isinstance(desc_value, np.ndarray) and desc_value.ndim == 2:
                                        mean_val = np.mean(desc_value)
                                        if not np.isnan(mean_val): values_dict['values'].append(mean_val)
                                        if not np.isnan(np.max(desc_value)): current_trace_stats_values["Magnitude * Voice_max"]['values'].append(np.max(desc_value))
                                        if not np.isnan(np.median(desc_value)): current_trace_stats_values["Magnitude * Voice_median"]['values'].append(np.median(desc_value))
                                        if not np.isnan(np.std(desc_value)): current_trace_stats_values["Magnitude * Voice_std"]['values'].append(np.std(desc_value))
                                    elif output_name == 'Normalized HFC' and 'hfc' in descriptor:
                                        # Import the helper function
                                        from utils.processing import get_robust_hfc_normalization_value

                                        # Get robust HFC normalization value
                                        hfc_p95_setting, source_description = get_robust_hfc_normalization_value(
                                            hfc_data=descriptor['hfc'],
                                            plot_settings=base_plot_settings
                                        )
                                        logging.info(f"Normalized HFC using value: {hfc_p95_setting} (source: {source_description})")

                                        normalized_hfc_val = descriptor['hfc'] / hfc_p95_setting
                                        values_dict['values'].extend(normalized_hfc_val if isinstance(normalized_hfc_val, np.ndarray) else [normalized_hfc_val])
                                    elif output_name == 'Normalized Spectral Decrease' and 'spec_decrease' in descriptor:
                                        # Import the helper function
                                        from utils.processing import get_robust_spec_decrease_normalization_value

                                        # Get robust Spectral Decrease normalization value
                                        spec_decrease_p95_setting, source_description = get_robust_spec_decrease_normalization_value(
                                            spec_decrease_data=descriptor['spec_decrease'],
                                            plot_settings=base_plot_settings
                                        )
                                        logging.info(f"Normalized Spectral Decrease using value: {spec_decrease_p95_setting} (source: {source_description})")

                                        if spec_decrease_p95_setting > 0:
                                            normalized_spec_decrease_val = descriptor['spec_decrease'] / spec_decrease_p95_setting
                                            values_dict['values'].extend(normalized_spec_decrease_val if isinstance(normalized_spec_decrease_val, np.ndarray) else [normalized_spec_decrease_val])
                                    elif output_name == 'WOSS' and 'WOSS' not in descriptor and all(k in descriptor for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                                        woss_calc_params = base_plot_settings.copy() # Start with plot_settings
                                        # Ensure hfc_p95 is in woss_calc_params, sourced from session_state.hfc_p95 or plot_settings.hfc_p95
                                        if st.session_state.get('hfc_p95') is not None:
                                            woss_calc_params['hfc_p95'] = st.session_state.hfc_p95
                                        elif 'hfc_p95' not in woss_calc_params and 'hfc' in descriptor: # Fallback if not globally set
                                             woss_calc_params['hfc_p95'] = np.percentile(descriptor['hfc'], base_plot_settings.get('hfc_percentile', 95.0))

                                        woss_values = calculate_woss(descriptor, woss_calc_params) # calculate_woss is imported
                                        values_dict['values'].extend(woss_values if isinstance(woss_values, np.ndarray) else [woss_values])
                                    elif isinstance(desc_value, np.ndarray) and desc_value.ndim == 1 and not is_spec_agg_stat:
                                        values_dict['values'].extend(desc_value)
                                    elif not isinstance(desc_value, np.ndarray) and not is_spec_agg_stat: # Handle scalar descriptors
                                         values_dict['values'].append(desc_value)


                            # Calculate final statistics (min, max, p5, p90) for this trace
                            individual_trace_stats_summary = {}
                            for desc_name_stat, stat_vals_dict in current_trace_stats_values.items():
                                if stat_vals_dict['values']:
                                    vals = np.array(stat_vals_dict['values'])
                                    individual_trace_stats_summary[desc_name_stat] = {
                                        'min': np.min(vals), 'max': np.max(vals),
                                        'p5': np.percentile(vals, 5), 'p90': np.percentile(vals, 90),
                                        'mean': np.mean(vals), 'median': np.median(vals), 'std': np.std(vals) # Add more stats
                                    }
                                else:
                                    individual_trace_stats_summary[desc_name_stat] = {
                                        'min': 'N/A', 'max': 'N/A', 'p5': 'N/A', 'p90': 'N/A',
                                        'mean': 'N/A', 'median': 'N/A', 'std': 'N/A'
                                    }
                            individual_trace_stats = individual_trace_stats_summary


                            # 3. Store results
                            st.session_state.individual_well_analysis_results.append({
                                'well_marker_name': well_marker_name,
                                'trace_idx': trace_data.get('trace_idx'),
                                'descriptors': descriptor,
                                'statistics': individual_trace_stats
                            })
                            logging.info(f"Successfully processed and calculated stats for (Individual) {display_name}")

                        except Exception as e:
                            logging.error(f"Individual well analysis failed for {display_name}: {e}", exc_info=True)
                            st.session_state.individual_well_analysis_results.append({
                                'well_marker_name': well_marker_name,
                                'trace_idx': trace_data.get('trace_idx'),
                                'descriptors': {}, # Store empty if error during calc
                                'statistics': {},  # Store empty if error during calc
                                'error': str(e)
                            })
                            skipped_traces_count += 1
                        progress_bar.progress((i + 1) / num_traces)
                    
                    status_text.text("Individual well processing complete.")
                    progress_bar.empty()
                    if skipped_traces_count > 0:
                        st.warning(f"{skipped_traces_count} trace(s) failed during individual processing. Check logs.")
                    else:
                        st.success("All individual well analyses processed successfully.")
                    # --- End of Detailed Individual Well Analysis Logic ---

                    # Convert individual results to the format expected by view_results
                    # This ensures compatibility with the existing visualization code
                    logging.info("=== CONVERTING INDIVIDUAL ANALYSIS RESULTS ===")
                    converted_descriptors = []

                    for i, result in enumerate(st.session_state.individual_well_analysis_results):
                        try:
                            logging.debug(f"Processing result {i}: {type(result)} with keys {list(result.keys()) if isinstance(result, dict) else 'not a dict'}")

                            if 'descriptors' in result and result['descriptors']:
                                original_descriptor = result['descriptors']
                                logging.debug(f"Original descriptor type: {type(original_descriptor)}")

                                # Validate that the descriptor is actually a dictionary
                                if not isinstance(original_descriptor, dict):
                                    logging.error(f"Result {i}: descriptor is not a dictionary, it's {type(original_descriptor)}: {original_descriptor}")
                                    converted_descriptors.append({
                                        'error': f'Invalid descriptor type: {type(original_descriptor).__name__}',
                                        'well_marker_name': result.get('well_marker_name'),
                                        'trace_idx': result.get('trace_idx')
                                    })
                                    continue

                                # Validate that the descriptor contains expected spectral data
                                expected_keys = ['data', 'peak_freq', 'spec_centroid', 'fdom', 'norm_fdom', 'hfc']
                                has_spectral_data = any(key in original_descriptor for key in expected_keys)

                                if not has_spectral_data:
                                    logging.warning(f"Result {i}: descriptor lacks expected spectral data keys. Available keys: {list(original_descriptor.keys())}")

                                # Create a deep copy to avoid reference issues
                                try:
                                    descriptor_copy = original_descriptor.copy()

                                    # Validate the copy
                                    if not isinstance(descriptor_copy, dict):
                                        logging.error(f"Result {i}: descriptor copy failed, got {type(descriptor_copy)}")
                                        raise ValueError(f"Descriptor copy resulted in {type(descriptor_copy)}")

                                    # Add metadata to the descriptor for identification
                                    descriptor_copy['well_marker_name'] = result.get('well_marker_name')
                                    descriptor_copy['trace_idx'] = result.get('trace_idx')

                                    converted_descriptors.append(descriptor_copy)
                                    logging.debug(f"Successfully converted result {i} with {len(descriptor_copy)} keys")

                                except Exception as copy_error:
                                    logging.error(f"Result {i}: Error copying descriptor: {copy_error}")
                                    converted_descriptors.append({
                                        'error': f'Descriptor copy failed: {copy_error}',
                                        'well_marker_name': result.get('well_marker_name'),
                                        'trace_idx': result.get('trace_idx')
                                    })
                            else:
                                # Add empty descriptor with error info for failed traces
                                error_msg = result.get('error', 'Unknown error')
                                logging.debug(f"Result {i}: Adding error descriptor: {error_msg}")
                                converted_descriptors.append({
                                    'error': error_msg,
                                    'well_marker_name': result.get('well_marker_name'),
                                    'trace_idx': result.get('trace_idx')
                                })
                        except Exception as e:
                            logging.error(f"Error processing individual result {i}: {e}", exc_info=True)
                            converted_descriptors.append({
                                'error': f'Processing error: {e}',
                                'well_marker_name': result.get('well_marker_name') if isinstance(result, dict) else None,
                                'trace_idx': result.get('trace_idx') if isinstance(result, dict) else None
                            })

                    # Final validation before storing
                    logging.info(f"Conversion complete: {len(converted_descriptors)} descriptors converted")
                    valid_count = len([d for d in converted_descriptors if isinstance(d, dict) and 'error' not in d])
                    error_count = len([d for d in converted_descriptors if isinstance(d, dict) and 'error' in d])
                    invalid_count = len([d for d in converted_descriptors if not isinstance(d, dict)])

                    logging.info(f"Conversion summary: {valid_count} valid, {error_count} errors, {invalid_count} invalid")

                    if invalid_count > 0:
                        logging.error(f"Found {invalid_count} invalid descriptors after conversion!")
                        for i, desc in enumerate(converted_descriptors):
                            if not isinstance(desc, dict):
                                logging.error(f"Invalid descriptor at index {i}: {type(desc)} - {desc}")

                    # Store the converted descriptors in session state using safe storage
                    try:
                        from utils.session_state_handler import safe_store_descriptors
                        safe_store_descriptors(converted_descriptors, 'calculated_descriptors')
                        logging.info("Used enhanced descriptor storage with type validation")
                    except ImportError:
                        logging.warning("Session state handler not available, using basic storage")
                        st.session_state.calculated_descriptors = converted_descriptors
                    st.session_state.analysis_complete = True # Mark analysis as complete

                    # For individual analysis, we don't need global statistics
                    # but we should store some summary info for the UI
                    st.session_state.descriptor_statistics = {
                        'analysis_type': 'individual',
                        'total_traces': len(st.session_state.individual_well_analysis_results),
                        'successful_traces': len([r for r in st.session_state.individual_well_analysis_results if 'error' not in r]),
                        'failed_traces': len([r for r in st.session_state.individual_well_analysis_results if 'error' in r])
                    }


                elif analysis_sub_option == "Grouping Well Analysis":
                    # Check GPU availability before processing
                    from utils.gpu_manager import is_gpu_available, get_gpu_info
                    gpu_available = is_gpu_available()
                    gpu_info = get_gpu_info()

                    logging.info(f"Starting grouping well analysis - GPU available: {gpu_available}")
                    if gpu_info:
                        logging.info(f"GPU info: {gpu_info}")

                    if not gpu_available:
                        st.error("⚠️ **GPU Not Available**: GPU processing is required for spectral descriptor calculation. "
                               "Please ensure CUDA and CuPy are properly installed and a compatible GPU is available.")
                        st.info("**Troubleshooting Steps:**\n"
                               "1. Check if NVIDIA GPU drivers are installed\n"
                               "2. Verify CUDA toolkit installation\n"
                               "3. Ensure CuPy is installed: `pip install cupy-cuda11x` or `pip install cupy-cuda12x`\n"
                               "4. Restart the application after installing dependencies")
                        st.stop()

                    # This is the existing logic for grouped analysis
                    calculated_descriptors = []
                    num_traces = len(loaded_trace_data)
                    skipped_traces_count = 0

                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    status_text.text("Initializing descriptor calculation...")

                    with st.spinner("Calculating spectral descriptors using GPU..."):
                        for i, trace_data in enumerate(loaded_trace_data):
                            well_marker_name = trace_data.get('well_marker_name')  # Don't default to 'Unknown'
                            display_name = well_marker_name if well_marker_name else f"Trace {trace_data.get('trace_idx', i)}"
                            status_text.text(f"Processing trace {i+1}/{num_traces} for {display_name} using GPU...")

                            descriptor = {}
                            try:
                                # Use the descriptor settings from plot_settings
                                descriptor_settings = {
                                    'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                                    'shape': st.session_state.plot_settings.get('shape', 0.35),
                                    'kmax': st.session_state.plot_settings.get('kmax', 120.0),
                                    'int_val': st.session_state.plot_settings.get('int_val', 35.0),
                                    'b1': st.session_state.plot_settings.get('b1', 5.0),
                                    'b2': st.session_state.plot_settings.get('b2', 40.0),
                                    'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
                                    'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80),
                                    'epsilon': st.session_state.plot_settings.get('epsilon', 1e-4) # Added epsilon
                                }

                                # Ensure time and frequency limits are properly set (can remain as is)
                                if 'Time (Y-axis)' not in st.session_state.plot_settings and 'time_min' in st.session_state.plot_settings:
                                    st.session_state.plot_settings['Time (Y-axis)'] = (
                                        st.session_state.plot_settings.get('time_min', 0.0),
                                        st.session_state.plot_settings.get('time_max', 4.0)
                                    )
                                if 'Frequency' not in st.session_state.plot_settings and 'freq_min' in st.session_state.plot_settings:
                                    nyquist_freq = 0.5 / st.session_state.dt if hasattr(st.session_state, 'dt') and st.session_state.dt > 0 else 125.0
                                    st.session_state.plot_settings['Frequency'] = (
                                        st.session_state.plot_settings.get('freq_min', 0.0),
                                        st.session_state.plot_settings.get('freq_max', nyquist_freq)
                                    )

                                trace_sample = trace_data['trace_sample']
                                fmax_value = len(trace_sample) // 2

                                logging.info(f"Attempting GPU processing for {well_marker_name}: length={len(trace_sample)}, fmax={fmax_value}")

                                # Filter params using whitelist approach for GPU functions
                                valid_gpu_params = {
                                    'fmax', 'shape', 'kmax', 'int_val', 'b1', 'b2',
                                    'p_bandwidth', 'roll_percent', 'use_band_limited'
                                }
                                gpu_descriptor_settings = {
                                    k: v for k, v in descriptor_settings.items()
                                    if k in valid_gpu_params
                                }

                                # Verify trace_sample is valid before GPU call
                                if trace_sample is None or len(trace_sample) == 0:
                                    raise ValueError(f"Invalid trace sample for {well_marker_name}: empty or None")

                                # Enhanced input validation before GPU processing
                                if trace_sample is None:
                                    raise ValueError("Trace sample is None")

                                if not isinstance(trace_sample, np.ndarray):
                                    raise ValueError(f"Trace sample is not numpy array: {type(trace_sample)}")

                                if len(trace_sample) == 0:
                                    raise ValueError("Trace sample is empty")

                                if np.isnan(trace_sample).all():
                                    raise ValueError("Trace sample contains only NaN values")

                                if np.isinf(trace_sample).any():
                                    logging.warning(f"Trace sample for {well_marker_name} contains infinite values")
                                    trace_sample = np.nan_to_num(trace_sample, posinf=0.0, neginf=0.0)

                                if np.isnan(trace_sample).any():
                                    nan_count = np.isnan(trace_sample).sum()
                                    logging.warning(f"Trace sample for {well_marker_name} contains {nan_count} NaN values out of {len(trace_sample)}")
                                    if nan_count / len(trace_sample) > 0.5:  # More than 50% NaN
                                        raise ValueError(f"Trace sample contains too many NaN values: {nan_count}/{len(trace_sample)}")
                                    # Replace NaN with zeros for processing
                                    trace_sample = np.nan_to_num(trace_sample, nan=0.0)

                                # Validate dt parameter
                                if st.session_state.dt <= 0:
                                    raise ValueError(f"Invalid dt value: {st.session_state.dt}")

                                descriptor = dlogst_spec_descriptor_gpu(
                                    trace_sample,
                                    st.session_state.dt,
                                    fmax=fmax_value,
                                    **gpu_descriptor_settings
                                )

                                if descriptor is None:
                                    raise ValueError("GPU function returned None instead of descriptor dictionary.")

                                # Check if the descriptor itself indicates an error (e.g. from input validation within dlogst_spec_descriptor_gpu)
                                # This depends on how dlogst_spec_descriptor_gpu signals errors (e.g., specific keys or NaN content)
                                # For now, we assume if it returns, it's either valid or contains NaNs that stats will handle.
                                # A more robust check could be if critical fields are all NaN.
                                if 'data' not in descriptor or not isinstance(descriptor['data'], np.ndarray): # Basic check
                                    raise ValueError("GPU descriptor missing 'data' or invalid format.")


                                # Basic validation - only check if descriptor has some valid data
                                # More permissive validation similar to working version
                                if not descriptor or not isinstance(descriptor, dict):
                                    raise ValueError(f"GPU function returned invalid descriptor for {well_marker_name}")

                                # Check if descriptor has at least some valid numeric data
                                has_valid_data = False
                                for key, value in descriptor.items():
                                    if isinstance(value, np.ndarray) and value.size > 0:
                                        if not np.all(np.isnan(value)):
                                            has_valid_data = True
                                            break
                                    elif isinstance(value, (int, float, np.number)) and np.isfinite(value):
                                        has_valid_data = True
                                        break

                                if not has_valid_data:
                                    raise ValueError(f"GPU descriptor contains no valid data for {well_marker_name}")

                                logging.info(f"Successfully calculated GPU descriptors for {well_marker_name}")

                                if descriptor: # Ensure descriptor is not an empty dict from a caught error path
                                    descriptor['well_marker_name'] = well_marker_name
                                    descriptor['trace_idx'] = trace_data.get('trace_idx')
                                calculated_descriptors.append(descriptor)

                            except Exception as e:
                                # Enhanced error logging for debugging
                                logging.error(f"GPU descriptor calculation failed for trace {well_marker_name}: {e}", exc_info=True)
                                logging.error(f"Input data details - shape: {trace_sample.shape if hasattr(trace_sample, 'shape') else 'No shape'}, "
                                            f"dtype: {trace_sample.dtype if hasattr(trace_sample, 'dtype') else type(trace_sample)}, "
                                            f"length: {len(trace_sample) if hasattr(trace_sample, '__len__') else 'No length'}")
                                logging.error(f"Processing parameters - dt: {st.session_state.dt}, fmax: {fmax_value}")
                                logging.error(f"GPU settings: {gpu_descriptor_settings}")

                                # Check GPU availability when error occurs
                                from utils.gpu_manager import is_gpu_available
                                gpu_status = is_gpu_available()
                                logging.error(f"GPU availability during error: {gpu_status}")

                                # Instead of creating an error descriptor, create a minimal valid descriptor
                                # This ensures we have a valid descriptor object even when calculation fails
                                sample_length = len(trace_sample) if hasattr(trace_sample, '__len__') else 0

                                # Create a minimal valid descriptor with zeros for required fields
                                minimal_descriptor = {
                                    'data': trace_sample.copy() if hasattr(trace_sample, 'copy') else np.zeros(sample_length, dtype=np.float32),
                                    'well_marker_name': well_marker_name,
                                    'trace_idx': trace_data.get('trace_idx'),
                                    'hfc': np.zeros(sample_length, dtype=np.float32),
                                    'norm_fdom': np.zeros(sample_length, dtype=np.float32),
                                    'mag_voice_slope': np.zeros(sample_length, dtype=np.float32)
                                }

                                # Add error information but don't make it the primary key
                                minimal_descriptor['_error_info'] = {
                                    'error': str(e),
                                    'error_type': type(e).__name__,
                                    'gpu_available': gpu_status
                                }

                                calculated_descriptors.append(minimal_descriptor)

                                # More informative warning message
                                st.warning(f"⚠️ GPU processing failed for trace {well_marker_name}: {type(e).__name__}. "
                                         f"Using minimal descriptor with zeros. GPU Available: {gpu_status}. Check logs for details.")
                                skipped_traces_count += 1

                        progress_bar.progress((i + 1) / num_traces)

                    status_text.text("All traces processed.")
                    progress_bar.empty()

                    st.session_state.calculated_descriptors = calculated_descriptors
                    st.session_state.analysis_complete = True

                    if skipped_traces_count > 0:
                        st.warning(f"Descriptor calculation finished. {skipped_traces_count} trace(s) could not be processed due to GPU errors. Please check logs for details.")
                    else:
                        st.success("Descriptor calculation complete.")

                    # Add a button to view results
                    st.markdown("---")
                    st.markdown("### Visualization:")
                    st.markdown("Click **View Results** to proceed to the results page for visualization and analysis options")

                    # This button is now outside the if/elif for sub-option,
                    # but will only be effective if st.session_state.analysis_complete is True
                    # The statistics calculation below is part of the "Grouping Well Analysis"
                    # For "Plot Individual Wells Analysis", statistics are per-well.
                    
                    # Calculate statistics for the descriptors (Only for Grouping Well Analysis)
                    if calculated_descriptors: # This will be populated by the Grouping Well Analysis path
                        # Initialize dictionaries to store statistics for all available outputs
                        stats = {}

                        # Create mapping between display names and internal descriptor keys
                        descriptor_mapping = {
                            # Signal data
                            "Input Signal": "data",

                            # Spectrograms and time-frequency representations
                            "Magnitude Spectrogram": "tf_map",
                            "Magnitude * Voice": "mag_voice",

                            # Frequency-related descriptors
                            "Normalized dominant frequencies": "norm_fdom",
                            "Normalized Dominant Frequency": "norm_fdom",
                            "Dominant Frequency": "fdom",
                            "Peak Frequency": "peak_freq",
                            "Spectral Centroid": "spec_centroid",

                            # Slope-related descriptors
                            "Spectral Slope": "spec_slope",
                            "Mag*Voice Slope": "mag_voice_slope",
                            "Voice Slope": "voice_slope",
                            "Slope Magnitude": "mag_voice_slope",

                            # Bandwidth and spectral shape descriptors
                            "Spectral Bandwidth": "spec_bandwidth",
                            "Spectral Rolloff": "spec_rolloff",
                            "Spectral Decrease": "spec_decrease",
                            "Normalized Spectral Decrease": "spec_decrease",

                            # High-frequency content
                            "HFC": "hfc",
                            "Normalized HFC": "hfc",  # We'll normalize this manually

                            # Composite descriptors
                            "WOSS": "WOSS"
                        }

                        # Initialize stats for all available outputs
                        for output in AVAILABLE_OUTPUTS_SINGLE + AVAILABLE_OUTPUTS_MULTI:
                            if output not in stats:  # Initialize for all, including Mag Spec
                                stats[output] = {'values': []}

                        # Add custom stats for normalized values
                        stats['Normalized HFC'] = {'values': []}
                        stats['Normalized Spectral Decrease'] = {'values': []}

                        # Removed pre-initialization of _max, _median, _std for spectrograms

                        # Collect values for each descriptor
                        for desc_idx, desc in enumerate(calculated_descriptors): # Use enumerate for logging if needed
                            # Skip if it's an error placeholder or truly empty
                            if not desc or 'error' in desc:
                                if 'error' in desc:
                                     logging.warning(f"Skipping statistics calculation for trace {desc.get('well_marker_name', 'Unknown')} due to previous error: {desc['error']}")
                                else:
                                     logging.warning(f"Skipping statistics calculation for trace index {desc_idx} due to empty descriptor.")
                                continue

                            # Process each available output
                            for output_name, values_dict in stats.items():
                                # Get the corresponding internal key
                                internal_key = descriptor_mapping.get(output_name)

                                # Skip if no mapping or descriptor doesn't exist in current result
                                if not internal_key:
                                    continue
                                if internal_key not in desc:
                                    continue

                                # Aligned Spectrogram statistics (only mean)
                                if output_name == "Magnitude Spectrogram" and isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 2:
                                    spec_array = desc[internal_key]
                                    mean_mag_spec = np.mean(spec_array)
                                    if not np.isnan(mean_mag_spec): values_dict['values'].append(mean_mag_spec)
                                elif output_name == "Magnitude * Voice" and isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 2:
                                    mag_voice_array = desc[internal_key]
                                    mean_mag_voice = np.mean(mag_voice_array)
                                    if not np.isnan(mean_mag_voice): values_dict['values'].append(mean_mag_voice)
                                # Aligned Normalized HFC calculation
                                elif output_name == 'Normalized HFC' and 'hfc' in desc:
                                    # Import the helper function
                                    from utils.processing import get_robust_hfc_normalization_value

                                    # Get robust HFC normalization value
                                    hfc_p95_val, source_description = get_robust_hfc_normalization_value(
                                        hfc_data=desc['hfc'],
                                        plot_settings=st.session_state.plot_settings
                                    )
                                    logging.info(f"Normalized HFC using value: {hfc_p95_val} (source: {source_description})")

                                    normalized_hfc_val = desc['hfc'] / hfc_p95_val
                                    values_dict['values'].extend(normalized_hfc_val if isinstance(normalized_hfc_val, np.ndarray) else [normalized_hfc_val])
                                elif output_name == 'Normalized Spectral Decrease' and 'spec_decrease' in desc:
                                    # Import the helper function
                                    from utils.processing import get_robust_spec_decrease_normalization_value

                                    # Get robust Spectral Decrease normalization value
                                    spec_decrease_p95_val, source_description = get_robust_spec_decrease_normalization_value(
                                        spec_decrease_data=desc['spec_decrease'],
                                        plot_settings=st.session_state.plot_settings
                                    )
                                    logging.info(f"Normalized Spectral Decrease using value: {spec_decrease_p95_val} (source: {source_description})")

                                    if spec_decrease_p95_val > 0:
                                        normalized_spec_decrease_val = desc['spec_decrease'] / spec_decrease_p95_val
                                        values_dict['values'].extend(normalized_spec_decrease_val if isinstance(normalized_spec_decrease_val, np.ndarray) else [normalized_spec_decrease_val])
                                    else:
                                        logging.warning(f"Invalid spec_decrease_p95 value ({spec_decrease_p95_val}) for Normalized Spectral Decrease calculation. Skipping normalization for this trace.")
                                # Handle WOSS calculation if not already present
                                elif output_name == 'WOSS' and 'WOSS' not in desc and all(k in desc for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                                    # Ensure we're using the plot_settings with the hfc_p95 value from precomputation
                                    # Initialize woss_params, can be from plot_settings or an empty dict
                                    woss_params = st.session_state.get('plot_settings', {}).copy()

                                    # Get HFC p95 from session state (set in Step 2)
                                    hfc_p95_session_value = st.session_state.get('hfc_p95')

                                    if hfc_p95_session_value is not None:
                                        woss_params['hfc_p95'] = hfc_p95_session_value
                                        logging.info(f"Using HFC p95 from session state: {hfc_p95_session_value}")
                                    elif 'hfc_p95' not in woss_params:
                                        # Fallback: if not in session_state AND not in plot_settings, calculate from current data
                                        logging.warning("HFC p95 value not found in session state or plot_settings. Calculating from current data.")
                                        if 'hfc' in desc and isinstance(desc['hfc'], np.ndarray) and desc['hfc'].size > 0:
                                            hfc_percentile = woss_params.get('hfc_percentile', 95.0) # Default to 95 if not specified
                                            calculated_hfc_p95 = np.percentile(desc['hfc'], hfc_percentile)
                                            woss_params['hfc_p95'] = float(calculated_hfc_p95)
                                            logging.info(f"Calculated HFC p{hfc_percentile} from current data: {calculated_hfc_p95}")
                                        else:
                                            logging.warning("Could not calculate HFC p95 from current data as 'hfc' descriptor is missing or empty.")
                                            # calculate_woss itself has a fallback if hfc_p95 is still missing in woss_params

                                    # Log the parameters being used for WOSS calculation
                                    logging.info(f"WOSS calculation parameters: hfc_p95={woss_params.get('hfc_p95', 'Not set/Fallback in calculate_woss')}, epsilon={woss_params.get('epsilon', 1e-4)}")
                                    woss = calculate_woss(desc, woss_params)
                                    values_dict['values'].extend(woss)
                                # Regular case (for 1D arrays)
                                elif isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 1:
                                    values_dict['values'].extend(desc[internal_key])

                        # Calculate statistics for each descriptor
                        for desc_name, desc_stats in stats.items():
                            if desc_stats['values']:
                                values = np.array(desc_stats['values'])
                                desc_stats['min'] = np.min(values)
                                desc_stats['max'] = np.max(values)
                                desc_stats['p5'] = np.percentile(values, 5)
                                desc_stats['p90'] = np.percentile(values, 90)

                                # HFC p95 value is now calculated and stored in Step 2.
                                # This ensures the same value is used for all subsequent calculations.
                                pass
                            else:
                                desc_stats['min'] = 'N/A'
                                desc_stats['max'] = 'N/A'
                                desc_stats['p5'] = 'N/A'
                                desc_stats['p90'] = 'N/A'

                        # Display the statistics in an expander
                        with st.expander("📊 Descriptor Statistics Summary", expanded=True):
                            st.markdown("### Signal Descriptor Statistics")
                            st.markdown("The following statistics have been calculated for the selected traces:")

                            # Group descriptors into categories for better organization
                            categories = {
                                "Primary Descriptors": [
                                    "Slope Magnitude", "Normalized HFC", "Normalized Spectral Decrease", "Normalized Dominant Frequencies",
                                    "WOSS", "Normalized Dominant Frequency", "Mag*Voice Slope"
                                ],
                                "Spectral Properties": [
                                    "Spectral Slope", "Spectral Bandwidth", "Spectral Rolloff",
                                    "Spectral Decrease", "Magnitude * Voice" # Mean of Mag*Voice will be here
                                ],
                                "Signal Characteristics": [
                                    "Input Signal", "HFC", "Magnitude Spectrogram" # Mean of Mag Spec will be here
                                ],
                                "Other Descriptors": []  # For any remaining descriptors (Aligned with backup/app_ref.py)
                            }

                            # Assign each descriptor to a category
                            categorized_stats = {cat: [] for cat in categories.keys()}

                            for desc_name, desc_stats in stats.items():
                                if not desc_stats['values']:  # Skip empty descriptors
                                    continue

                                # Find which category this descriptor belongs to
                                assigned = False
                                for cat_name, cat_descriptors in categories.items():
                                    if desc_name in cat_descriptors:
                                        if isinstance(desc_stats['min'], (int, float)):
                                            categorized_stats[cat_name].append({
                                                "Descriptor": desc_name,
                                                "Min": desc_stats['min'],  # Keep as numeric
                                                "Max": desc_stats['max'],  # Keep as numeric
                                                "P5": desc_stats['p5'],    # Keep as numeric
                                                "P90": desc_stats['p90']   # Keep as numeric
                                            })
                                        else:
                                            categorized_stats[cat_name].append({
                                                "Descriptor": desc_name,
                                                "Min": desc_stats['min'],
                                                "Max": desc_stats['max'],
                                                "P5": desc_stats['p5'],
                                                "P90": desc_stats['p90']
                                            })
                                        assigned = True
                                        break

                                # If not assigned to any specific category, put in "Other Descriptors"
                                if not assigned and desc_stats['values']:
                                    if isinstance(desc_stats['min'], (int, float)):
                                        categorized_stats["Other Descriptors"].append({
                                            "Descriptor": desc_name,
                                            "Min": desc_stats['min'],  # Keep as numeric
                                            "Max": desc_stats['max'],  # Keep as numeric
                                            "P5": desc_stats['p5'],    # Keep as numeric
                                            "P90": desc_stats['p90']   # Keep as numeric
                                        })
                                    else:
                                        categorized_stats["Other Descriptors"].append({
                                            "Descriptor": desc_name,
                                            "Min": desc_stats['min'],
                                            "Max": desc_stats['max'],
                                            "P5": desc_stats['p5'],
                                            "P90": desc_stats['p90']
                                        })

                            # Display tables by category
                            for cat_name, cat_stats in categorized_stats.items():
                                if cat_stats:  # Only show categories with data
                                    st.subheader(cat_name)
                                    # Create DataFrame with numeric values
                                    df = pd.DataFrame(cat_stats)

                                    # Format the DataFrame for display with 4 decimal places
                                    # but keep the underlying data as numeric
                                    formatted_df = df.style.format({
                                        "Min": "{:.4f}",
                                        "Max": "{:.4f}",
                                        "P5": "{:.4f}",
                                        "P90": "{:.4f}"
                                    }, na_rep="N/A")

                                    st.table(formatted_df)

                            # Add explanation
                            st.markdown("""
                            **Note:**
                            - **Min/Max**: Absolute minimum and maximum values across all traces
                            - **P5**: 5th percentile value (95% of values are above this)
                            - **P90**: 90th percentile value (10% of values are above this)
                            """)

                        # Store the statistics in session state for later use
                        st.session_state.descriptor_statistics = stats
                    # End of "Grouping Well Analysis" specific logic
                
                    # Common "View Results" button, enabled if analysis_complete is True
                    if st.session_state.get('analysis_complete', False):
                        st.markdown("---")
                        st.success("Analysis processing is complete.")
                        
                        col1, col2 = st.columns(2)
                        with col1:
                            if st.button("View Results", key="view_results_button_well_markers", help="Proceed to the results page"):
                                st.session_state.current_step = "view_results"
                                st.rerun()
                        with col2:
                            if st.button("Export Results", key="export_results_button_well_markers", help="Export the calculated descriptors for Step 5 visualization"):
                                export_well_marker_results()
            else:
                st.warning("Please select at least one well-marker pair.")

        # This 'else' corresponds to 'if st.session_state.well_df is not None ...'
        else:
            st.markdown("---")
            st.success("✅ Descriptor calculation is complete. You can now view the detailed results.")
            if st.button("View Results", key="view_results_button_main", help="Open the detailed analysis report"):
                st.session_state.current_step = "view_results"
                st.rerun()
        # Well data validation - only show error for well markers mode when well data is missing
        if st.session_state.well_df is None or st.session_state.well_df.empty:
            st.error("No well data available. Please go back and upload well data.")
            if st.button("Back to Mode Selection"):
                st.session_state.current_step = "select_mode"
                st.rerun()

    # AOI Mode - Only proceed if AOI selection has been confirmed
    if st.session_state.selection_mode == "By inline/crossline section (AOI)":
        # Check if AOI selection has been properly confirmed in Step 3
        if not st.session_state.get('area_selected') or st.session_state.get('area_selected_mode') != 'aoi':
            st.warning("⚠️ AOI selection not completed. Please return to Step 3 to complete your AOI selection.")
            if st.button("← Return to Step 3: Select AOI"):
                st.session_state.current_step = "select_mode"
                st.rerun()
            return

        if not st.session_state.get('aoi_selection_confirmed', False):
            st.warning("⚠️ AOI selection not confirmed. Please return to Step 3 to confirm your AOI selection.")
            if st.button("← Return to Step 3: Confirm AOI"):
                st.session_state.current_step = "select_mode"
                st.rerun()
            return

        # Verify we have selected indices from the confirmed AOI
        if not st.session_state.get('selected_indices'):
            st.error("❌ No traces selected. Please return to Step 3 to complete AOI selection.")
            if st.button("← Return to Step 3"):
                st.session_state.current_step = "select_mode"
                st.rerun()
            return

        # Display confirmed AOI information
        st.success("✅ AOI Selection Confirmed")
        aoi_bounds = st.session_state.get('aoi_bounds', {})
        if aoi_bounds:
            st.info(f"**Selected AOI:** Inlines {aoi_bounds['inline_min']} to {aoi_bounds['inline_max']}, "
                   f"Crosslines {aoi_bounds['xline_min']} to {aoi_bounds['xline_max']} "
                   f"({len(st.session_state.selected_indices)} traces)")

        # Add a button to proceed to analysis
        st.markdown("---")
        st.markdown("### AOI Analysis Workflow:")
        st.markdown("1. Click **Calculate Descriptors** to process the selected traces and compute signal descriptors")
        st.markdown("2. Review the statistical summary that will appear")
        st.markdown("3. Click **View Results** to proceed to the results page for visualization and analysis options")

        if st.button("Calculate Descriptors", key="calculate_descriptors_button", help="Process the selected traces and compute spectral descriptors"):
            # Calculate descriptors for the selected AOI traces
            with st.spinner("Calculating spectral descriptors for AOI..."):
                # Load trace data for the selected indices
                loaded_trace_data = []
                max_len = 0
                for trace_idx in st.session_state.selected_indices:
                    # Check if we have pre-computed data for this trace
                    if precomputed_data:
                        precomputed_item = next((item for item in precomputed_data if item.get('trace_idx') == trace_idx), None)
                        if precomputed_item and 'processed_trace' in precomputed_item:
                            # Use the processed trace from pre-computation
                            trace_sample = precomputed_item['processed_trace']
                            logging.info(f"Using pre-computed data for trace {trace_idx}")
                        else:
                            # Fallback to loading the original trace
                            trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)
                            logging.info(f"Pre-computed data not found for trace {trace_idx}, using original data")
                    else:
                        # No precomputed data available, load the original trace
                        trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)
                        logging.info(f"Loading original data for trace {trace_idx} (no precomputation)")

                    loaded_trace_data.append({'trace_sample': trace_sample, 'trace_idx': trace_idx})
                    max_len = max(max_len, len(trace_sample))

                # Pad traces if necessary
                if max_len > 0:
                    for item in loaded_trace_data:
                        if len(item['trace_sample']) < max_len:
                            pad_width = max_len - len(item['trace_sample'])
                            item['trace_sample'] = np.pad(item['trace_sample'], (0, pad_width), 'constant')

                # Stack traces into a 2D array for batch processing
                if loaded_trace_data:
                    traces_array = np.stack([t['trace_sample'] for t in loaded_trace_data]).astype(np.float32)
                    fmax_calc = max_len // 2 if max_len > 0 else 250

                    # Use the descriptor settings from plot_settings
                    descriptor_settings = {
                        'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                        'shape': st.session_state.plot_settings.get('shape', 0.35),
                        'kmax': st.session_state.plot_settings.get('kmax', 120.0),
                        'int_val': st.session_state.plot_settings.get('int_val', 35.0),
                        'b1': st.session_state.plot_settings.get('b1', 5.0),
                        'b2': st.session_state.plot_settings.get('b2', 40.0),
                        'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
                        'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80),
                        'epsilon': st.session_state.plot_settings.get('epsilon', 1e-4),
                        'hfc_percentile': st.session_state.plot_settings.get('hfc_percentile', 95.0)
                    }

                    try:
                        if st.session_state.get('GPU_AVAILABLE', False):
                            try:
                                # Filter params using whitelist approach for GPU functions
                                valid_gpu_params = {
                                    'fmax', 'shape', 'kmax', 'int_val', 'b1', 'b2',
                                    'p_bandwidth', 'roll_percent', 'batch_size', 'use_band_limited'
                                }
                                gpu_descriptor_settings = {
                                    k: v for k, v in descriptor_settings.items()
                                    if k in valid_gpu_params
                                }

                                # Log parameters for debugging
                                logging.info(f"Calling dlogst_spec_descriptor_gpu_2d_chunked_mag with parameters: dt={st.session_state.dt}, fmax={fmax_calc}, batch_size={st.session_state.get('batch_size', 512)}, settings={gpu_descriptor_settings}")

                                # Use the 2D chunked GPU function
                                calculated_descriptors_dict = dlogst_spec_descriptor_gpu_2d_chunked_mag(
                                    traces_array,
                                    st.session_state.dt,
                                    fmax=fmax_calc,
                                    batch_size=st.session_state.get('batch_size', 512), # Use batch size from session state
                                    **gpu_descriptor_settings
                                )

                                # Verify that we got results back
                                if not calculated_descriptors_dict:
                                    st.error("GPU calculation returned empty results. Check logs for details.")
                                    logging.error("GPU calculation returned empty results.")
                                    raise ValueError("Empty descriptor results from GPU calculation")

                                # Log the keys we got back
                                logging.info(f"Received descriptor keys: {list(calculated_descriptors_dict.keys())}")

                                # Convert dictionary of arrays to list of dictionaries
                                num_traces_in_batch = traces_array.shape[0]
                                calculated_descriptors = [{} for _ in range(num_traces_in_batch)]
                                for key, value_array in calculated_descriptors_dict.items():
                                    if isinstance(value_array, np.ndarray) and value_array.shape[0] == num_traces_in_batch:
                                        for trace_idx in range(num_traces_in_batch):
                                            calculated_descriptors[trace_idx][key] = value_array[trace_idx]
                                    # Handle 2D arrays like 'mag' and 'mag_voice'
                                    elif isinstance(value_array, np.ndarray) and value_array.ndim == 3 and value_array.shape[0] == num_traces_in_batch:
                                        for trace_idx in range(num_traces_in_batch):
                                            calculated_descriptors[trace_idx][key] = value_array[trace_idx]
                                    else:
                                        logging.warning(f"Shape mismatch for descriptor '{key}' in AOI processing. Expected {num_traces_in_batch} traces, got {value_array.shape if isinstance(value_array, np.ndarray) else 'not an array'}. Skipping this descriptor.")

                                # Verify that descriptors contain expected keys
                                expected_keys = ['data', 'peak_freq', 'spec_centroid', 'fdom', 'norm_fdom', 'hfc', 'mag_voice_slope']
                                if calculated_descriptors:
                                    missing_keys = [key for key in expected_keys if key not in calculated_descriptors[0]]
                                    if missing_keys:
                                        logging.warning(f"Descriptors are missing expected keys: {missing_keys}")
                                        st.warning(f"Descriptor calculation incomplete. Missing: {', '.join(missing_keys)}")

                            except Exception as e:
                                logging.error(f"Error in GPU batch processing: {e}", exc_info=True)
                                st.error(f"GPU batch processing error: {str(e)}")
                                # Fall back to individual processing
                                st.warning("Falling back to individual trace processing...")
                                calculated_descriptors = []
                                for trace_item in tqdm(loaded_trace_data, desc="Processing traces individually"):
                                    try:
                                        # Filter params using whitelist approach for GPU functions
                                        valid_gpu_params = {
                                            'fmax', 'shape', 'kmax', 'int_val', 'b1', 'b2',
                                            'p_bandwidth', 'roll_percent', 'use_band_limited'
                                        }
                                        gpu_descriptor_settings = {
                                            k: v for k, v in descriptor_settings.items()
                                            if k in valid_gpu_params
                                        }

                                        descriptor = dlogst_spec_descriptor_gpu(
                                            trace_item['trace_sample'],
                                            st.session_state.dt,
                                            fmax=len(trace_item['trace_sample']) // 2,
                                            **gpu_descriptor_settings
                                        )
                                        calculated_descriptors.append(descriptor)
                                    except Exception as e_inner:
                                        logging.error(f"Error processing trace {trace_item['trace_idx']}: {e_inner}")
                                        calculated_descriptors.append({})

                        else:
                            st.warning("GPU not available. Processing AOI on CPU (very slow)...")
                            # Fallback to processing traces individually on CPU
                            calculated_descriptors = []
                            try:
                                # Try to import CPU implementation if available
                                from utils.dlogst_spec_descriptor_cpu import dlogst_spec_descriptor_cpu

                                for trace_item in tqdm(loaded_trace_data, desc="Processing traces on CPU"):
                                    try:
                                        descriptor = dlogst_spec_descriptor_cpu(
                                            trace_item['trace_sample'],
                                            st.session_state.dt,
                                            fmax=len(trace_item['trace_sample']) // 2,
                                            **descriptor_settings
                                        )
                                        calculated_descriptors.append(descriptor)
                                    except Exception as e:
                                        logging.error(f"Error calculating descriptor for trace {trace_item['trace_idx']} on CPU: {e}")
                                        calculated_descriptors.append({})
                            except ImportError:
                                st.error("CPU implementation not available. Cannot calculate descriptors.")
                                # Create empty descriptors as fallback
                                calculated_descriptors = [{} for _ in range(len(loaded_trace_data))]
                            except Exception as e:
                                logging.error(f"Error in CPU processing setup: {e}", exc_info=True)
                                st.error(f"CPU processing error: {str(e)}")
                                calculated_descriptors = [{} for _ in range(len(loaded_trace_data))]

                        # Check if we have valid descriptors
                        valid_descriptors = [d for d in calculated_descriptors if d and len(d) > 0]
                        if valid_descriptors:
                            st.session_state.calculated_descriptors = calculated_descriptors
                            st.session_state.analysis_complete = True
                            st.success(f"Descriptor calculation complete for AOI. {len(valid_descriptors)} of {len(calculated_descriptors)} traces processed successfully.")
                        else:
                            st.error("No valid descriptors were calculated. Please check the logs for errors.")
                            st.session_state.calculated_descriptors = []
                            st.session_state.analysis_complete = False

                        # Calculate statistics for the descriptors (same logic as well markers mode)
                        if calculated_descriptors:
                            # Initialize dictionaries to store statistics for all available outputs
                            stats = {}

                            # Create mapping between display names and internal descriptor keys
                            descriptor_mapping = {
                                # Signal data
                                "Input Signal": "data",

                                # Spectrograms and time-frequency representations
                                "Magnitude Spectrogram": "mag", # Use 'mag' key
                                "Magnitude * Voice": "mag_voice", # Use 'mag_voice' key

                                # Frequency-related descriptors
                                "Normalized dominant frequencies": "norm_fdom",
                                "Normalized Dominant Frequency": "norm_fdom",
                                "Dominant Frequency": "fdom",
                                "Peak Frequency": "peak_freq",
                                "Spectral Centroid": "spec_centroid",

                                # Slope-related descriptors
                                "Spectral Slope": "spec_slope",
                                "Mag*Voice Slope": "mag_voice_slope",
                                "Voice Slope": "voice_slope",
                                "Slope Magnitude": "mag_voice_slope",

                                # Bandwidth and spectral shape descriptors
                                "Spectral Bandwidth": "spec_bandwidth",
                                "Spectral Rolloff": "spec_rolloff",
                                "Spectral Decrease": "spec_decrease",

                                # High-frequency content
                                "HFC": "hfc",
                                "Normalized HFC": "hfc",  # We'll normalize this manually

                                # Composite descriptors
                                "WOSS": "WOSS"
                            }

                            # Initialize stats for all available outputs
                            for output in AVAILABLE_OUTPUTS_SINGLE + AVAILABLE_OUTPUTS_MULTI:
                                if output not in stats:
                                    stats[output] = {'values': []}

                            # Add custom stats for normalized values
                            stats['Normalized HFC'] = {'values': []}

                            # Pre-initialize additional statistics for spectrograms to avoid modifying dict during iteration
                            stats['Magnitude Spectrogram_max'] = {'values': []}
                            stats['Magnitude Spectrogram_median'] = {'values': []}
                            stats['Magnitude Spectrogram_std'] = {'values': []}
                            stats['Magnitude * Voice_max'] = {'values': []}
                            stats['Magnitude * Voice_median'] = {'values': []}
                            stats['Magnitude * Voice_std'] = {'values': []}

                            # Collect values for each descriptor
                            for desc in calculated_descriptors:
                                if not desc:  # Skip empty descriptors
                                    continue

                                # Process each available output
                                for output_name, values_dict in stats.items():
                                    # Get the corresponding internal key
                                    internal_key = descriptor_mapping.get(output_name)

                                    # Skip if no mapping or descriptor doesn't exist in current result
                                    if not internal_key:
                                        continue
                                    if internal_key not in desc:
                                        continue

                                    # Enhanced statistics for 2D Spectrogram (handling 3D dimensional arrays for each trace)
                                    if output_name == "Magnitude Spectrogram" and isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 2:
                                        # Calculate multiple statistics for the 2D array
                                        spec_array = desc[internal_key]
                                        # Mean across all values
                                        mean_mag_spec = np.mean(spec_array)
                                        if not np.isnan(mean_mag_spec): values_dict['values'].append(mean_mag_spec)

                                        # These keys should already be pre-initialized before the loop
                                        # No need to check if they exist

                                        # Calculate additional statistics
                                        max_val = np.max(spec_array)
                                        median_val = np.median(spec_array)
                                        std_val = np.std(spec_array)

                                        # Store these statistics
                                        if not np.isnan(max_val): stats[f"{output_name}_max"]['values'].append(max_val)
                                        if not np.isnan(median_val): stats[f"{output_name}_median"]['values'].append(median_val)
                                        if not np.isnan(std_val): stats[f"{output_name}_std"]['values'].append(std_val)

                                    # Enhanced statistics for Magnitude * Voice (handling 3D dimensional arrays for each trace)
                                    elif output_name == "Magnitude * Voice" and isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 2:
                                        # Calculate multiple statistics for the 2D array
                                        mag_voice_array = desc[internal_key]
                                        # Mean across all values
                                        mean_mag_voice = np.mean(mag_voice_array)
                                        if not np.isnan(mean_mag_voice): values_dict['values'].append(mean_mag_voice)

                                        # These keys should already be pre-initialized before the loop
                                        # No need to check if they exist

                                        # Calculate additional statistics
                                        max_val = np.max(mag_voice_array)
                                        median_val = np.median(mag_voice_array)
                                        std_val = np.std(mag_voice_array)

                                        # Store these statistics
                                        if not np.isnan(max_val): stats[f"{output_name}_max"]['values'].append(max_val)
                                        if not np.isnan(median_val): stats[f"{output_name}_median"]['values'].append(median_val)
                                        if not np.isnan(std_val): stats[f"{output_name}_std"]['values'].append(std_val)
                                    # Special case for normalized HFC
                                    elif output_name == 'Normalized HFC' and 'hfc' in desc:
                                        # Import the helper function
                                        from utils.processing import get_robust_hfc_normalization_value

                                        # Get robust HFC normalization value
                                        hfc_p95, source_description = get_robust_hfc_normalization_value(
                                            hfc_data=desc['hfc']
                                        )
                                        logging.info(f"Normalized HFC using value: {hfc_p95} (source: {source_description})")

                                        normalized_hfc = desc['hfc'] / hfc_p95
                                        values_dict['values'].extend(normalized_hfc)
                                    # Handle WOSS calculation if not already present
                                    elif output_name == 'WOSS' and 'WOSS' not in desc and all(k in desc for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                                        # Ensure we're using the plot_settings with the hfc_p95 value from precomputation
                                        # Initialize woss_params, can be from plot_settings or an empty dict
                                        woss_params = st.session_state.get('plot_settings', {}).copy()

                                        # Get HFC p95 from session state (set in Step 2)
                                        hfc_p95_session_value = st.session_state.get('hfc_p95')

                                        if hfc_p95_session_value is not None:
                                            woss_params['hfc_p95'] = hfc_p95_session_value
                                            logging.info(f"Using HFC p95 from session state for WOSS: {hfc_p95_session_value}")
                                        elif 'hfc_p95' not in woss_params:
                                            # Fallback: if not in session_state AND not in plot_settings, calculate_woss has its own internal fallback
                                            logging.warning("HFC p95 value not found in session state or plot_settings for WOSS. Relaying on calculate_woss fallback.")

                                        # Log the parameters being used for WOSS calculation
                                        logging.info(f"WOSS calculation parameters: hfc_p95={woss_params.get('hfc_p95', 'Not set/Fallback in calculate_woss')}, epsilon={woss_params.get('epsilon', 1e-4)}")
                                        woss = calculate_woss(desc, woss_params)
                                        values_dict['values'].extend(woss)
                                    # Regular case (for 1D arrays)
                                    elif isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 1:
                                        values_dict['values'].extend(desc[internal_key])

                            # Calculate statistics for each descriptor
                            for desc_name, desc_stats in stats.items():
                                if desc_stats['values']:
                                    values = np.array(desc_stats['values'])
                                    desc_stats['min'] = np.min(values)
                                    desc_stats['max'] = np.max(values)
                                    desc_stats['p5'] = np.percentile(values, 5)
                                    desc_stats['p90'] = np.percentile(values, 90)
                                else:
                                    desc_stats['min'] = 'N/A'
                                    desc_stats['max'] = 'N/A'
                                    desc_stats['p5'] = 'N/A'
                                    desc_stats['p90'] = 'N/A'

                            # Display the statistics in an expander
                            with st.expander("📊 Descriptor Statistics Summary", expanded=True):
                                st.markdown("### Signal Descriptor Statistics")
                                st.markdown("The following statistics have been calculated for the selected traces:")

                                # Group descriptors into categories for better organization
                                categories = {
                                    "Primary Descriptors": [
                                        "Slope Magnitude", "Normalized HFC", "Normalized Dominant Frequencies",
                                        "WOSS", "Normalized Dominant Frequency", "Mag*Voice Slope"
                                    ],
                                    "Spectral Properties": [
                                        "Spectral Slope", "Spectral Bandwidth", "Spectral Rolloff",
                                        "Spectral Decrease", "Magnitude * Voice"
                                    ],
                                    "Signal Characteristics": [
                                        "Input Signal", "HFC", "Magnitude Spectrogram"
                                    ],
                                    "Spectrogram Statistics": [
                                        "Magnitude Spectrogram_max", "Magnitude Spectrogram_median", "Magnitude Spectrogram_std",
                                        "Magnitude * Voice_max", "Magnitude * Voice_median", "Magnitude * Voice_std"
                                    ],
                                    "Other Descriptors": []  # For any remaining descriptors
                                }

                                # Assign each descriptor to a category
                                categorized_stats = {cat: [] for cat in categories.keys()}

                                for desc_name, desc_stats in stats.items():
                                    if not desc_stats['values']:  # Skip empty descriptors
                                        continue

                                    # Find which category this descriptor belongs to
                                    assigned = False
                                    for cat_name, cat_descriptors in categories.items():
                                        if desc_name in cat_descriptors:
                                            if isinstance(desc_stats['min'], (int, float)):
                                                categorized_stats[cat_name].append({
                                                    "Descriptor": desc_name,
                                                    "Min": desc_stats['min'],  # Keep as numeric
                                                    "Max": desc_stats['max'],  # Keep as numeric
                                                    "P5": desc_stats['p5'],    # Keep as numeric
                                                    "P90": desc_stats['p90']   # Keep as numeric
                                                })
                                            else:
                                                categorized_stats[cat_name].append({
                                                    "Descriptor": desc_name,
                                                    "Min": desc_stats['min'],
                                                    "Max": desc_stats['max'],
                                                    "P5": desc_stats['p5'],
                                                    "P90": desc_stats['p90']
                                                })
                                            assigned = True
                                            break

                                    # If not assigned to any specific category, put in "Other Descriptors"
                                    if not assigned and desc_stats['values']:
                                        if isinstance(desc_stats['min'], (int, float)):
                                            categorized_stats["Other Descriptors"].append({
                                                "Descriptor": desc_name,
                                                "Min": desc_stats['min'],  # Keep as numeric
                                                "Max": desc_stats['max'],  # Keep as numeric
                                                "P5": desc_stats['p5'],    # Keep as numeric
                                                "P90": desc_stats['p90']   # Keep as numeric
                                            })
                                        else:
                                            categorized_stats["Other Descriptors"].append({
                                                "Descriptor": desc_name,
                                                "Min": desc_stats['min'],
                                                "Max": desc_stats['max'],
                                                "P5": desc_stats['p5'],
                                                "P90": desc_stats['p90']
                                            })

                                # Display tables by category
                                for cat_name, cat_stats in categorized_stats.items():
                                    if cat_stats:  # Only show categories with data
                                        st.subheader(cat_name)
                                        # Create DataFrame with numeric values
                                        df = pd.DataFrame(cat_stats)

                                        # Format the DataFrame for display with 4 decimal places
                                        # but keep the underlying data as numeric
                                        formatted_df = df.style.format({
                                            "Min": "{:.4f}",
                                            "Max": "{:.4f}",
                                            "P5": "{:.4f}",
                                            "P90": "{:.4f}"
                                        }, na_rep="N/A")

                                        st.table(formatted_df)

                                # Add explanation
                                st.markdown("""
                                **Note:**
                                - **Min/Max**: Absolute minimum and maximum values across all traces
                                - **P5**: 5th percentile value (95% of values are above this)
                                - **P90**: 90th percentile value (10% of values are above this)
                                """)

                            # Store the statistics in session state for later use
                            st.session_state.descriptor_statistics = stats

                        # Proceed to results - only show this button after descriptors are calculated
                        if st.session_state.analysis_complete:
                            st.markdown("---")
                            st.success("✅ Descriptor calculation is complete for AOI. You can now view the detailed results.")
                            if st.button("View Results", key="view_results_button_aoi", help="Open the detailed analysis report"):
                                st.session_state.current_step = "view_results"
                                st.rerun()

                    except Exception as e:
                        st.error(f"Error calculating descriptors for AOI: {e}")
                        logging.error(f"Descriptor calculation failed for AOI: {e}", exc_info=True)

    # Single Inline Mode
    elif st.session_state.selection_mode == "Single inline (all crosslines)":
        render_line_analysis('inline')

    # Single Crossline Mode
    elif st.session_state.selection_mode == "Single crossline (all inlines)":
        render_line_analysis('crossline')

    # Polyline Mode
    if st.session_state.selection_mode == "By Polyline File Import":
        st.subheader("Polyline Mode")
        
        # Check if we have selected indices
        if not st.session_state.get('selected_indices'):
            st.warning("No traces selected along the polyline. Please go back to Step 3 and select traces.")
            return
            
        # Initialize traces_loaded_polyline if not present
        if 'traces_loaded_polyline' not in st.session_state:
            st.session_state.traces_loaded_polyline = False
            
        # Initialize loaded_trace_data if not present
        if 'loaded_trace_data' not in st.session_state:
            st.session_state.loaded_trace_data = []
            
        st.info(f"Selected {len(st.session_state.selected_indices)} traces along the polyline.")
        
        # Button to load traces if not loaded yet
        if not st.session_state.traces_loaded_polyline:
            if st.button("Load Traces for Processing", key="load_traces_polyline_btn"):
                with st.spinner(f"Loading {len(st.session_state.selected_indices)} traces near polyline..."):
                    try:
                        loaded_data_temp = []
                        # Load each trace
                        for trace_idx in st.session_state.selected_indices:
                            trace_sample = load_trace_sample(st.session_state.segy_temp_file_path, trace_idx)
                            loaded_data_temp.append({
                                'trace_index': trace_idx,
                                'trace_sample': trace_sample
                            })
                        # Store in session state
                        st.session_state.loaded_trace_data = loaded_data_temp
                        st.session_state.traces_loaded_polyline = True
                        st.success(f"Successfully loaded {len(loaded_data_temp)} traces for analysis.")
                        st.rerun()  # Rerun to update the UI
                    except Exception as e:
                        st.error(f"Error loading traces: {e}")
                        st.session_state.traces_loaded_polyline = False
                        logging.error(f"Error loading traces for polyline: {e}", exc_info=True)
        else:
            st.success(f"Loaded {len(st.session_state.loaded_trace_data)} traces for analysis.")
            
            # Select outputs for the section
            st.subheader("Select Outputs for Display")
            available_outputs = AVAILABLE_OUTPUTS_SECTION  # Use the section outputs defined in constants.py
            
            st.session_state.selected_outputs = st.multiselect(
                "Select spectral descriptors to calculate",
                options=available_outputs,
                default=["WOSS"] if "WOSS" in available_outputs else [available_outputs[0]]
            )
            
            # Check if we need to calculate descriptors
            calculate_button_disabled = not st.session_state.traces_loaded_polyline
            if calculate_button_disabled:
                st.warning("Please load traces before calculating descriptors.")
            
            # GPU batch size setting
            if GPU_AVAILABLE:
                suggested_batch_size, gpu_memory_mb = get_suggested_batch_size()
                st.sidebar.subheader("GPU Settings")
                st.session_state.batch_size = st.sidebar.slider(
                    "GPU Batch Size",
                    min_value=1,
                    max_value=max(100, suggested_batch_size * 2),
                    value=suggested_batch_size,
                    step=1,
                    help="Number of traces to process in each GPU batch. Higher values use more GPU memory."
                )
                st.sidebar.info(f"Available GPU Memory: {gpu_memory_mb:.1f} MB")
            
            # Button to calculate descriptors
            if st.button("Calculate Descriptors", key="calculate_descriptors_polyline", disabled=calculate_button_disabled):
                with st.spinner("Calculating spectral descriptors for the polyline section..."):
                    try:
                        # Prepare 2D array from loaded_trace_data
                        trace_samples_list = [item['trace_sample'] for item in st.session_state.loaded_trace_data]
                        section_data_2d = np.stack(trace_samples_list, axis=0)
                        
                        # Get spectral parameters from plot_settings
                        spectral_params = {
                            'winlen': st.session_state.plot_settings.get('winlen', 64),
                            'overlap': st.session_state.plot_settings.get('overlap', 0.5),
                            'fs': 1.0 / st.session_state.dt,  # Calculate sampling frequency from dt
                            'fmin': st.session_state.plot_settings.get('fmin', 0),
                            'fmax': st.session_state.plot_settings.get('fmax', None),
                        }
                        
                        # Determine which outputs to calculate
                        internal_outputs_map = {
                            "Input Signal": "data",
                            "Magnitude Spectrogram": "mag",
                            "Magnitude * Voice": "mag_voice",
                            "Normalized dominant frequencies": "norm_fdom",
                            "Spectral Slope": "spec_slope",
                            "Spectral Bandwidth": "spec_bandwidth", 
                            "Spectral Rolloff": "spec_rolloff",
                            "Mag*Voice Slope": "mag_voice_slope",
                            "Spectral Decrease": "spec_decrease",
                            "HFC": "hfc",
                            "WOSS": "WOSS"
                        }
                        
                        # Map selected outputs to internal names
                        outputs_for_gpu_calc = [internal_outputs_map.get(output, output) for output in st.session_state.selected_outputs]
                        
                        # Ensure we have what we need for WOSS if it's selected
                        if "WOSS" in outputs_for_gpu_calc:
                            required_for_woss = ["hfc", "norm_fdom", "mag_voice_slope"]
                            for required in required_for_woss:
                                if required not in outputs_for_gpu_calc:
                                    outputs_for_gpu_calc.append(required)
                        
                        # Calculate using GPU if available
                        if GPU_AVAILABLE and st.session_state.batch_size:
                            # Filter params using whitelist approach for GPU functions
                            valid_gpu_params = {
                                'fmax', 'shape', 'kmax', 'int_val', 'b1', 'b2',
                                'p_bandwidth', 'roll_percent', 'batch_size', 'use_band_limited'
                            }
                            filtered_spectral_params = {
                                k: v for k, v in spectral_params.items()
                                if k in valid_gpu_params
                            }
                            all_descriptors = dlogst_spec_descriptor_gpu_2d_chunked(
                                section_data_2d,
                                st.session_state.dt,
                                batch_size=st.session_state.batch_size,
                                **filtered_spectral_params
                            )

                            # Filter to only the requested descriptors
                            calculated_attribute_sections = {key: all_descriptors[key] for key in outputs_for_gpu_calc
                                                            if key in all_descriptors}
                        else:
                            st.error("GPU processing is not available. Please ensure GPU functions are loaded or reduce the data size.")
                            return
                        
                        # Add original data if selected
                        if "data" in outputs_for_gpu_calc and "data" not in calculated_attribute_sections:
                            calculated_attribute_sections["data"] = section_data_2d
                        
                        # Calculate WOSS if selected
                        if "WOSS" in outputs_for_gpu_calc and "WOSS" not in calculated_attribute_sections:
                            if all(key in calculated_attribute_sections for key in ["hfc", "norm_fdom", "mag_voice_slope"]):
                                # Get HFC p95 from session state
                                hfc_p95 = st.session_state.get('hfc_p95', 1.0)
                                
                                # Create WOSS parameters
                                woss_params = {
                                    'hfc_p95': hfc_p95,
                                    'epsilon': st.session_state.plot_settings.get('epsilon', 1e-4),
                                    'fdom_exponent': st.session_state.plot_settings.get('fdom_exponent', 2.0)
                                }
                                
                                # Calculate WOSS for each trace in the section
                                woss_array = np.zeros_like(calculated_attribute_sections['hfc'])
                                for i in range(section_data_2d.shape[0]):
                                    trace_components = {
                                        'hfc': calculated_attribute_sections['hfc'][i],
                                        'norm_fdom': calculated_attribute_sections['norm_fdom'][i],
                                        'mag_voice_slope': calculated_attribute_sections['mag_voice_slope'][i]
                                    }
                                    woss_array[i] = calculate_woss(trace_components, woss_params)
                                
                                # Add WOSS to the calculated descriptors
                                calculated_attribute_sections['WOSS'] = woss_array
                        
                        # Store results in session state
                        st.session_state.calculated_descriptors = calculated_attribute_sections
                        st.session_state.analysis_complete = True
                        st.success("Descriptor calculation complete.")
                        st.rerun()
                    except Exception as e:
                        st.error(f"Error calculating descriptors: {e}")
                        logging.error(f"Error calculating descriptors for polyline section: {e}", exc_info=True)
                        return
            
            # Show results if analysis is complete
            if st.session_state.get('analysis_complete', False):
                display_polyline_results()

                st.markdown("---")
                if st.button("Export Polyline Results", key="export_polyline_results_button"):
                    export_polyline_results()
                    st.rerun()

    # Add a "Start New Analysis" button to the sidebar
    st.sidebar.markdown("---")
    if st.sidebar.button("🔄 Start New Analysis", use_container_width=True):
        reset_state()
        st.success("Starting new analysis. All temporary data has been cleared.")
        st.rerun()
