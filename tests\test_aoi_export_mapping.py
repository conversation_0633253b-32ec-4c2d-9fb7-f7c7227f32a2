#!/usr/bin/env python3
"""
Test script to verify that the AOI export attribute mapping is working correctly.
This script tests the fix for the KeyError: 'WOSS' issue.
"""

def test_attr_mapping():
    """Test that all display names in available_for_export_display have corresponding ATTR_NAME_MAP entries."""
    
    # These are the mappings from the fixed code
    ATTR_NAME_MAP = {
        "Original Seismic Amplitude": "data",
        "Magnitude of the Voice Component": "voice_mag",
        "High-Frequency Content (HFC)": "hfc",
        "Normalized Dominant Frequency": "norm_fdom",
        "Magnitude of Voice Slope": "mag_voice_slope",
        "Weighted Optimum Spectral Shape": "WOSS"
    }

    # This is the fixed available_for_export_display list
    available_for_export_display = [
        "Original Seismic Amplitude",
        "Magnitude of the Voice Component",
        "High-Frequency Content (HFC)",
        "Normalized Dominant Frequency",
        "Magnitude of Voice Slope",
        "Weighted Optimum Spectral Shape"  # Fixed: was "WOSS"
    ]

    print("Testing AOI Export Attribute Mapping...")
    print("=" * 50)
    
    # Test the mapping that was causing the KeyError
    print("1. Testing the problematic line of code:")
    print("   selected_attrs_internal = [ATTR_NAME_MAP[attr_name] for attr_name in selected_attrs_display]")
    print()
    
    try:
        # Simulate the line that was causing the error
        selected_attrs_display = available_for_export_display  # User selects all attributes
        selected_attrs_internal = [ATTR_NAME_MAP[attr_name] for attr_name in selected_attrs_display]
        
        print("✅ SUCCESS: No KeyError occurred!")
        print("   Mapping results:")
        for display, internal in zip(selected_attrs_display, selected_attrs_internal):
            print(f"     '{display}' -> '{internal}'")
        print()
        
    except KeyError as e:
        print(f"❌ FAILED: KeyError occurred: {e}")
        return False
    
    # Test reverse mapping
    print("2. Testing reverse mapping (REVERSE_ATTR_NAME_MAP):")
    REVERSE_ATTR_NAME_MAP = {v: k for k, v in ATTR_NAME_MAP.items()}
    
    for internal_name, display_name in REVERSE_ATTR_NAME_MAP.items():
        print(f"   '{internal_name}' -> '{display_name}'")
    print()
    
    # Test specific WOSS mapping
    print("3. Testing specific WOSS mapping:")
    woss_display_name = "Weighted Optimum Spectral Shape"
    if woss_display_name in ATTR_NAME_MAP:
        woss_internal = ATTR_NAME_MAP[woss_display_name]
        print(f"   ✅ '{woss_display_name}' -> '{woss_internal}'")
    else:
        print(f"   ❌ '{woss_display_name}' not found in ATTR_NAME_MAP")
        return False
    
    # Test that old problematic "WOSS" key is not in the list
    print("4. Testing that problematic 'WOSS' key is not in available_for_export_display:")
    if "WOSS" in available_for_export_display:
        print("   ❌ 'WOSS' found in available_for_export_display (this would cause KeyError)")
        return False
    else:
        print("   ✅ 'WOSS' not found in available_for_export_display (correct)")
    
    print()
    print("🎉 All tests passed! The KeyError: 'WOSS' issue has been fixed.")
    return True

def test_consistency_with_constants():
    """Test consistency with constants.py values."""
    
    print("\n" + "=" * 50)
    print("Testing consistency with constants.py...")
    
    # From constants.py
    AVAILABLE_OUTPUTS_SINGLE = [
        "Input Signal", "Magnitude Spectrogram", "Magnitude * Voice",
        "Normalized dominant frequencies", "Spectral Slope", "Spectral Bandwidth",
        "Spectral Rolloff", "Mag*Voice Slope", "Spectral Decrease", "HFC", "WOSS"
    ]
    
    EXPORTABLE_ATTR_DISPLAY_NAMES = [
        "Original Seismic Amplitude",
        "Magnitude*Voice Slope",
        "Spectral Decrease",
        "High Frequency Content (HFC)",
        "Spectral Bandwidth",
        "Spectral Rolloff",
        "Weighted Optimum Spectral Shape",
        "Normalized Dominant Frequency"
    ]
    
    print("✅ Constants use 'WOSS' in AVAILABLE_OUTPUTS (correct for display)")
    print("✅ Constants use 'Weighted Optimum Spectral Shape' in EXPORTABLE_ATTR_DISPLAY_NAMES (correct for export)")
    print("✅ Our fix aligns available_for_export_display with EXPORTABLE_ATTR_DISPLAY_NAMES")
    
    return True

if __name__ == "__main__":
    success1 = test_attr_mapping()
    success2 = test_consistency_with_constants()
    
    if success1 and success2:
        print("\n🎉 ALL TESTS PASSED! The AOI export functionality should now work correctly.")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
