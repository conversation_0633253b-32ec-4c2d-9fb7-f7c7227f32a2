#!/usr/bin/env python3
"""
GPU Diagnostic Tool for WOSS Seismic Analysis
This script helps diagnose GPU-related issues in the WOSS application.
"""

import logging
import numpy as np
import sys
import traceback

def test_gpu_availability():
    """Test if GPU and CuPy are available and working."""
    print("🔍 Testing GPU Availability...")
    
    try:
        import cupy as cp
        print("✅ CuPy imported successfully")
        
        # Test basic GPU operation
        test_array = cp.array([1, 2, 3, 4, 5])
        result = cp.sum(test_array)
        print(f"✅ Basic GPU operation successful: sum([1,2,3,4,5]) = {result}")
        
        # Get GPU info
        device_count = cp.cuda.runtime.getDeviceCount()
        print(f"✅ GPU devices found: {device_count}")
        
        for i in range(device_count):
            cp.cuda.Device(i).use()
            props = cp.cuda.runtime.getDeviceProperties(i)
            print(f"   Device {i}: {props['name'].decode()}")
            
        # Test memory
        free_bytes, total_bytes = cp.cuda.runtime.memGetInfo()
        free_mb = free_bytes / (1024 ** 2)
        total_mb = total_bytes / (1024 ** 2)
        print(f"✅ GPU Memory: {free_mb:.1f} MB free / {total_mb:.1f} MB total")
        
        return True, None
        
    except ImportError as e:
        print(f"❌ CuPy import failed: {e}")
        return False, f"CuPy not installed: {e}"
    except Exception as e:
        print(f"❌ GPU test failed: {e}")
        return False, f"GPU error: {e}"

def test_gpu_descriptor_function():
    """Test the GPU descriptor function with sample data."""
    print("\n🔍 Testing GPU Descriptor Function...")
    
    try:
        from utils.dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu
        print("✅ GPU descriptor function imported successfully")
        
        # Create sample seismic trace data
        dt = 0.001  # 1ms sampling
        duration = 1.0  # 1 second
        t = np.arange(0, duration, dt)
        
        # Create a synthetic seismic trace (combination of frequencies)
        trace = (np.sin(2 * np.pi * 10 * t) +  # 10 Hz
                0.5 * np.sin(2 * np.pi * 25 * t) +  # 25 Hz
                0.3 * np.sin(2 * np.pi * 50 * t))   # 50 Hz
        
        # Add some noise
        trace += 0.1 * np.random.randn(len(trace))
        
        print(f"✅ Created test trace: length={len(trace)}, dt={dt}")
        print(f"   Trace stats: min={trace.min():.3f}, max={trace.max():.3f}, mean={trace.mean():.3f}")
        
        # Test GPU function
        result = dlogst_spec_descriptor_gpu(trace, dt)
        
        if result is None:
            print("❌ GPU function returned None")
            return False, "GPU function returned None"
        
        if not isinstance(result, dict):
            print(f"❌ GPU function returned {type(result)} instead of dict")
            return False, f"Invalid return type: {type(result)}"
        
        print(f"✅ GPU function returned dictionary with {len(result)} keys")
        
        # Check for expected keys
        expected_keys = ['data', 'peak_freq', 'spec_centroid', 'fdom', 'norm_fdom', 'hfc', 
                        'spec_slope', 'mag_voice_slope', 'spec_decrease', 'spec_bandwidth', 'spec_rolloff']
        
        missing_keys = [key for key in expected_keys if key not in result]
        if missing_keys:
            print(f"⚠️  Missing expected keys: {missing_keys}")
        else:
            print("✅ All expected keys present")
        
        # Check data types
        for key, value in result.items():
            if isinstance(value, np.ndarray):
                print(f"   {key}: numpy array, shape={value.shape}, dtype={value.dtype}")
            else:
                print(f"   {key}: {type(value)}, value={value}")
        
        return True, None
        
    except ImportError as e:
        print(f"❌ Failed to import GPU descriptor function: {e}")
        return False, f"Import error: {e}"
    except Exception as e:
        print(f"❌ GPU descriptor function test failed: {e}")
        print(f"   Traceback: {traceback.format_exc()}")
        return False, f"Function error: {e}"

def test_session_state_storage():
    """Test session state storage functionality."""
    print("\n🔍 Testing Session State Storage...")
    
    try:
        from utils.session_state_handler import safe_store_descriptors, safe_retrieve_descriptors
        print("✅ Session state handler imported successfully")
        
        # Create test descriptors
        test_descriptors = [
            {
                'data': np.random.randn(100).astype(np.float32),
                'peak_freq': np.float32(25.0),
                'spec_centroid': np.float32(30.0),
                'well_marker_name': 'Test_Well_1'
            },
            {
                'data': np.random.randn(100).astype(np.float32),
                'peak_freq': np.float32(20.0),
                'spec_centroid': np.float32(28.0),
                'well_marker_name': 'Test_Well_2'
            }
        ]
        
        print(f"✅ Created {len(test_descriptors)} test descriptors")
        return True, None
        
    except ImportError as e:
        print(f"❌ Failed to import session state handler: {e}")
        return False, f"Import error: {e}"
    except Exception as e:
        print(f"❌ Session state test failed: {e}")
        return False, f"Session state error: {e}"

def main():
    """Run all diagnostic tests."""
    print("🚀 WOSS GPU Diagnostic Tool")
    print("=" * 50)
    
    # Test 1: GPU Availability
    gpu_ok, gpu_error = test_gpu_availability()
    
    # Test 2: GPU Descriptor Function (only if GPU is available)
    if gpu_ok:
        desc_ok, desc_error = test_gpu_descriptor_function()
    else:
        desc_ok, desc_error = False, "Skipped due to GPU unavailability"
    
    # Test 3: Session State Storage
    session_ok, session_error = test_session_state_storage()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 50)
    
    print(f"GPU Availability: {'✅ PASS' if gpu_ok else '❌ FAIL'}")
    if not gpu_ok:
        print(f"   Error: {gpu_error}")
    
    print(f"GPU Descriptor Function: {'✅ PASS' if desc_ok else '❌ FAIL'}")
    if not desc_ok:
        print(f"   Error: {desc_error}")
    
    print(f"Session State Storage: {'✅ PASS' if session_ok else '❌ FAIL'}")
    if not session_ok:
        print(f"   Error: {session_error}")
    
    # Recommendations
    print("\n🔧 RECOMMENDATIONS:")
    if not gpu_ok:
        print("1. Install CUDA toolkit from NVIDIA")
        print("2. Install CuPy: pip install cupy-cuda11x (or cupy-cuda12x)")
        print("3. Verify GPU drivers are up to date")
    elif not desc_ok:
        print("1. Check GPU memory availability")
        print("2. Verify input data format")
        print("3. Check for conflicting GPU processes")
    else:
        print("✅ All tests passed! GPU processing should work correctly.")

if __name__ == "__main__":
    main()
