"""
Validation UI Components for WOSS Seismic Analysis Tool.

This module provides UI components for displaying data validation status,
loading progress, and user feedback during the area selection and data
loading process.
"""

import streamlit as st
import time
from typing import Optional, Tuple
from utils.data_validation import (
    DataValidationStatus, 
    should_enable_proceed_button,
    perform_comprehensive_validation,
    reset_loading_state
)

def create_validation_status_display(container, validation_status: Optional[DataValidationStatus] = None):
    """
    Create a comprehensive validation status display.
    
    Args:
        container: Streamlit container for the display
        validation_status: Current validation status, or None to get from session state
    """
    if validation_status is None:
        validation_status = st.session_state.get('data_validation_status')
    
    if validation_status is None:
        container.info("🔍 Data validation not yet performed")
        return
    
    # Main validation status
    if validation_status.is_valid:
        container.success("✅ Data validation passed - Ready for analysis!")
        
        # Show validation details
        if validation_status.validation_details:
            details = validation_status.validation_details
            
            # Create metrics display
            col1, col2, col3 = container.columns(3)
            
            if 'total_selected' in details:
                col1.metric("Total Traces", details['total_selected'])
            elif 'successfully_loaded' in details:
                col1.metric("Loaded Traces", details['successfully_loaded'])
                
            if 'selection_mode' in details:
                col2.metric("Selection Mode", details['selection_mode'])
                
            if 'valid_traces' in details:
                col3.metric("Valid Traces", details['valid_traces'])
            elif 'failed_to_load' in details:
                failed = details['failed_to_load']
                if failed > 0:
                    col3.metric("Failed Traces", failed, delta=f"-{failed}")
                else:
                    col3.metric("Failed Traces", "0", delta="Perfect!")
    
    else:
        # Show validation errors
        if validation_status.validation_errors:
            container.error("❌ Data validation failed:")
            for error in validation_status.validation_errors:
                container.error(f"• {error}")
        else:
            container.warning("⚠️ Data validation incomplete")
    
    # Show warnings only if validation has not passed
    if not validation_status.is_valid and validation_status.validation_warnings:
        with container.expander("⚠️ Validation Warnings", expanded=True): # expanded=True to make it more visible
            for warning in validation_status.validation_warnings:
                st.warning(f"• {warning}")
    
    # Show loading progress if in progress
    if st.session_state.get('data_loading_in_progress', False):
        if validation_status.loading_progress > 0:
            container.progress(validation_status.loading_progress / 100)
            container.text(f"Loading: {validation_status.loaded_traces}/{validation_status.total_traces} traces")

def create_proceed_button_with_validation(container, 
                                        button_text: str = "Proceed to Analysis",
                                        button_key: str = "proceed_to_analysis",
                                        on_click_callback=None,
                                        mode_suffix: str = "") -> bool:
    """
    Create a 'Proceed to Analysis' button with validation checks.
    
    Args:
        container: Streamlit container for the button
        button_text: Text to display on the button
        button_key: Unique key for the button
        on_click_callback: Optional callback function when button is clicked
        
    Returns:
        bool: True if button was clicked and validation passed
    """
    # Check if button should be enabled
    should_enable, reason = should_enable_proceed_button()
    
    # Create button with appropriate state
    if should_enable:
        button_clicked = container.button(
            button_text, 
            key=f"{button_key}{mode_suffix}", 
            type="primary",
            use_container_width=True
        )
        
        if button_clicked:
            if on_click_callback:
                on_click_callback()
            return True
    else:
        # Disabled button with reason
        container.button(
            f"{button_text} ({reason})", 
            key=f"{button_key}_disabled{mode_suffix}", 
            disabled=True,
            use_container_width=True
        )
        
        # Show help text
        if "validation not performed" in reason.lower():
            container.info("💡 Click 'Validate & Load Data' to enable this button")
        elif "loading in progress" in reason.lower():
            container.info("⏳ Please wait for data loading to complete")
        else:
            container.warning(f"⚠️ {reason}")
    
    return False

def create_validation_and_loading_section(container, mode_suffix: str = ""):
    """
    Create a complete validation and loading section with all controls.
    
    Args:
        container: Streamlit container for the section
    """
    container.subheader("📊 Data Validation & Loading")
    
    # Current validation status
    status_container = container.container()
    create_validation_status_display(status_container)
    
    # Control buttons
    col1, col2 = container.columns(2)
    
    # Validate & Load Data button
    if col1.button("🔍 Validate & Load Data", key=f"validate_and_load_data{mode_suffix}", use_container_width=True):
        # Create progress container
        progress_container = container.container()
        
        # Perform comprehensive validation
        validation_status = perform_comprehensive_validation(progress_container)
        
        # Store result in session state
        st.session_state.data_validation_status = validation_status
        st.session_state.last_validation_timestamp = time.time()
        
        # Update data ready flag
        st.session_state.data_ready_for_analysis = validation_status.is_valid
        
        # Rerun to update UI
        st.rerun()
    
    # Reset validation button
    if col2.button("🔄 Reset Validation", key=f"reset_validation{mode_suffix}", use_container_width=True):
        # Reset validation state
        selection_mode = st.session_state.get('selection_mode')
        reset_loading_state(selection_mode)
        
        # Clear validation status
        st.session_state.data_validation_status = None
        st.session_state.data_ready_for_analysis = False
        st.session_state.validation_required = True
        
        st.success("Validation state reset. Please validate data again.")
        st.rerun()

def create_loading_progress_display(container, 
                                  current: int, 
                                  total: int, 
                                  message: str = "Loading traces..."):
    """
    Create a loading progress display with progress bar and status.
    
    Args:
        container: Streamlit container for the display
        current: Current progress count
        total: Total items to process
        message: Status message to display
    """
    if total > 0:
        progress = current / total
        container.progress(progress)
        
        # Status message with counts
        status_msg = f"{message} ({current}/{total} - {progress:.1%})"
        container.text(status_msg)
        
        # ETA calculation (simple)
        if current > 0 and current < total:
            # Estimate based on current progress (very rough)
            remaining = total - current
            container.caption(f"Approximately {remaining} traces remaining...")

def create_data_quality_summary(container, validation_status: DataValidationStatus):
    """
    Create a data quality summary display.
    
    Args:
        container: Streamlit container for the display
        validation_status: Validation status with quality information
    """
    if not validation_status.validation_details:
        return
    
    details = validation_status.validation_details
    
    container.subheader("📈 Data Quality Summary")
    
    # Create quality metrics
    col1, col2, col3, col4 = container.columns(4)
    
    if 'total_selected' in details:
        col1.metric("Selected Traces", details['total_selected'])
    
    if 'successfully_loaded' in details:
        loaded = details['successfully_loaded']
        total = details.get('total_requested', loaded)
        success_rate = (loaded / total * 100) if total > 0 else 0
        
        col2.metric("Loaded Successfully", loaded)
        col3.metric("Success Rate", f"{success_rate:.1f}%")
    
    if 'failed_to_load' in details:
        failed = details['failed_to_load']
        col4.metric("Failed to Load", failed, delta=f"-{failed}" if failed > 0 else "Perfect!")
    
    # Show failed indices if any
    if details.get('failed_indices'):
        with container.expander("❌ Failed Trace Indices", expanded=False):
            failed_indices = details['failed_indices']
            st.write(f"First {len(failed_indices)} failed indices: {failed_indices}")

def show_selection_summary(container):
    """
    Show a summary of the current area selection.
    
    Args:
        container: Streamlit container for the display
    """
    if not st.session_state.get('area_selected'):
        container.warning("No area selected yet")
        return
    
    selection_mode = st.session_state.get('selection_mode', 'Unknown')
    selected_indices = st.session_state.get('selected_indices', [])
    
    container.subheader("📍 Selection Summary")
    
    col1, col2 = container.columns(2)
    col1.metric("Selection Mode", selection_mode)
    col2.metric("Trace Count", len(selected_indices))
    
    # Mode-specific details
    if selection_mode == "Single inline (all crosslines)":
        inline = st.session_state.get('selected_inline')
        if inline is not None:
            container.info(f"Selected inline: {inline}")
            
    elif selection_mode == "Single crossline (all inlines)":
        crossline = st.session_state.get('selected_crossline')
        if crossline is not None:
            container.info(f"Selected crossline: {crossline}")
            
    elif selection_mode == "By inline/crossline section (AOI)":
        aoi_bounds = st.session_state.get('aoi_bounds')
        if aoi_bounds:
            container.info(f"AOI: Inline {aoi_bounds['inline_min']}-{aoi_bounds['inline_max']}, "
                         f"Crossline {aoi_bounds['xline_min']}-{aoi_bounds['xline_max']}")
            
    elif selection_mode == "By Polyline File Import":
        polyline_info = st.session_state.get('polyline_file_info')
        tolerance = st.session_state.get('polyline_tolerance')
        if polyline_info and tolerance:
            container.info(f"Polyline file: {polyline_info['name']}, Tolerance: {tolerance}")
            
    elif selection_mode == "By well markers":
        well_pairs = st.session_state.get('selected_well_marker_pairs', [])
        if well_pairs:
            container.info(f"Selected {len(well_pairs)} well-marker pairs")
