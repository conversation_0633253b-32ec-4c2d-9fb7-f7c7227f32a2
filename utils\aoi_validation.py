"""
AOI Validation Module for WOSS Seismic Analysis Tool.

This module provides comprehensive validation functions specifically for 
Area of Interest (AOI) functionality, including boundary validation,
trace selection validation, and AOI processing functions.

Implements Phase 2 AOI functionality as specified in AOI_implementation_phase.md
"""

import streamlit as st
import pandas as pd
import numpy as np
import logging
import json
import tempfile
import os
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
import plotly.graph_objects as go
import plotly.express as px

def validate_aoi_selection() -> Dict[str, Any]:
    """
    Validate AOI boundaries and return validation result with comprehensive error handling.

    Returns:
        Dict containing validation results with keys:
        - valid: bool indicating if validation passed
        - error: str with error message if validation failed
        - trace_count: int number of traces in AOI if valid
        - aoi_df: DataFrame with traces in AOI if valid
        - warnings: list of warning messages
    """
    try:
        # Initialize result with warnings list
        result = {'valid': False, 'warnings': []}

        # Check if headers_df exists
        if not hasattr(st.session_state, 'headers_df') or st.session_state.headers_df is None:
            return {**result, 'error': 'Seismic headers not loaded. Please load SEG-Y data first.'}

        headers_df = st.session_state.headers_df
        
        # Check if required session state variables exist
        required_vars = ['aoi_inline_min', 'aoi_inline_max', 'aoi_xline_min', 'aoi_xline_max']
        missing_vars = []
        for var in required_vars:
            if not hasattr(st.session_state, var) or getattr(st.session_state, var) is None:
                missing_vars.append(var)

        if missing_vars:
            return {**result, 'error': f'AOI boundaries not set: {", ".join(missing_vars)}'}

        # Validate boundary logic with detailed error messages
        if st.session_state.aoi_inline_min > st.session_state.aoi_inline_max:
            return {**result, 'error': f'Inline minimum ({st.session_state.aoi_inline_min}) cannot be greater than maximum ({st.session_state.aoi_inline_max})'}

        if st.session_state.aoi_xline_min > st.session_state.aoi_xline_max:
            return {**result, 'error': f'Crossline minimum ({st.session_state.aoi_xline_min}) cannot be greater than maximum ({st.session_state.aoi_xline_max})'}
        
        # Check if boundaries are within data range
        actual_inline_min = headers_df['inline'].min()
        actual_inline_max = headers_df['inline'].max()
        actual_xline_min = headers_df['crossline'].min()
        actual_xline_max = headers_df['crossline'].max()
        
        if (st.session_state.aoi_inline_min < actual_inline_min or 
            st.session_state.aoi_inline_max > actual_inline_max):
            return {'valid': False, 'error': f'Inline range {st.session_state.aoi_inline_min}-{st.session_state.aoi_inline_max} outside data range {actual_inline_min}-{actual_inline_max}'}
        
        if (st.session_state.aoi_xline_min < actual_xline_min or 
            st.session_state.aoi_xline_max > actual_xline_max):
            return {'valid': False, 'error': f'Crossline range {st.session_state.aoi_xline_min}-{st.session_state.aoi_xline_max} outside data range {actual_xline_min}-{actual_xline_max}'}
        
        # Filter traces within AOI
        aoi_df = headers_df[
            (headers_df['inline'] >= st.session_state.aoi_inline_min) &
            (headers_df['inline'] <= st.session_state.aoi_inline_max) &
            (headers_df['crossline'] >= st.session_state.aoi_xline_min) &
            (headers_df['crossline'] <= st.session_state.aoi_xline_max)
        ]
        
        if aoi_df.empty:
            return {'valid': False, 'error': 'No traces found within specified AOI boundaries'}
        
        return {
            'valid': True,
            'trace_count': len(aoi_df),
            'aoi_df': aoi_df
        }
        
    except Exception as e:
        logging.error(f"AOI validation error: {str(e)}")
        return {'valid': False, 'error': f'Validation error: {str(e)}'}

def show_aoi_summary():
    """Display AOI selection summary with metrics."""
    st.write("**AOI Summary:**")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Inline Range", 
                 f"{st.session_state.aoi_inline_min} - {st.session_state.aoi_inline_max}")
    with col2:
        st.metric("Crossline Range", 
                 f"{st.session_state.aoi_xline_min} - {st.session_state.aoi_xline_max}")
    with col3:
        inline_count = st.session_state.aoi_inline_max - st.session_state.aoi_inline_min + 1
        xline_count = st.session_state.aoi_xline_max - st.session_state.aoi_xline_min + 1
        st.metric("AOI Size", f"{inline_count} × {xline_count}")

def process_aoi_selection() -> bool:
    """
    Process AOI selection and store results in session state.
    
    Returns:
        bool: True if processing successful, False otherwise
    """
    try:
        validation_result = validate_aoi_selection()
        
        if not validation_result['valid']:
            st.error(validation_result['error'])
            return False
        
        # Store AOI selection data
        aoi_df = validation_result['aoi_df']
        st.session_state.selected_indices = aoi_df['trace_idx'].tolist()
        st.session_state.area_selected = True
        st.session_state.area_selected_mode = 'aoi'
        st.session_state.area_selected_details = {
            'type': 'aoi',
            'trace_count': len(aoi_df),
            'boundaries': {
                'inline_min': st.session_state.aoi_inline_min,
                'inline_max': st.session_state.aoi_inline_max,
                'xline_min': st.session_state.aoi_xline_min,
                'xline_max': st.session_state.aoi_xline_max
            }
        }
        
        # Update AOI bounds for reference
        st.session_state.aoi_bounds = {
            'inline_min': st.session_state.aoi_inline_min,
            'inline_max': st.session_state.aoi_inline_max,
            'xline_min': st.session_state.aoi_xline_min,
            'xline_max': st.session_state.aoi_xline_max
        }
        
        st.success(f"✅ AOI selection complete: {len(aoi_df)} traces selected")
        return True
        
    except Exception as e:
        logging.error(f"AOI processing error: {str(e)}")
        st.error(f"❌ Error processing AOI selection: {str(e)}")
        return False

def validate_aoi_boundaries(inline_min: int, inline_max: int, 
                           xline_min: int, xline_max: int) -> Dict[str, Any]:
    """
    Validate AOI boundaries against data constraints.
    
    Args:
        inline_min: Minimum inline value
        inline_max: Maximum inline value  
        xline_min: Minimum crossline value
        xline_max: Maximum crossline value
        
    Returns:
        Dict with validation results
    """
    try:
        if not st.session_state.get('headers_df') is not None:
            return {'valid': False, 'error': 'No seismic data loaded'}
        
        headers_df = st.session_state.headers_df
        
        # Get actual data ranges
        actual_inline_min = headers_df['inline'].min()
        actual_inline_max = headers_df['inline'].max()
        actual_xline_min = headers_df['crossline'].min()
        actual_xline_max = headers_df['crossline'].max()
        
        # Validate boundary logic
        if inline_min > inline_max:
            return {'valid': False, 'error': 'Inline minimum cannot be greater than maximum'}
        
        if xline_min > xline_max:
            return {'valid': False, 'error': 'Crossline minimum cannot be greater than maximum'}
        
        # Validate against data range
        if inline_min < actual_inline_min or inline_max > actual_inline_max:
            return {'valid': False, 'error': f'Inline range outside data bounds ({actual_inline_min}-{actual_inline_max})'}
        
        if xline_min < actual_xline_min or xline_max > actual_xline_max:
            return {'valid': False, 'error': f'Crossline range outside data bounds ({actual_xline_min}-{actual_xline_max})'}
        
        # Check if any traces exist in the range
        aoi_df = headers_df[
            (headers_df['inline'] >= inline_min) &
            (headers_df['inline'] <= inline_max) &
            (headers_df['crossline'] >= xline_min) &
            (headers_df['crossline'] <= xline_max)
        ]
        
        if aoi_df.empty:
            return {'valid': False, 'error': 'No traces found in specified AOI range'}
        
        return {
            'valid': True,
            'trace_count': len(aoi_df),
            'coverage': {
                'inline_coverage': (inline_max - inline_min + 1),
                'xline_coverage': (xline_max - xline_min + 1),
                'total_grid_points': (inline_max - inline_min + 1) * (xline_max - xline_min + 1)
            }
        }
        
    except Exception as e:
        logging.error(f"AOI boundary validation error: {str(e)}")
        return {'valid': False, 'error': f'Validation error: {str(e)}'}

def get_aoi_trace_statistics() -> Dict[str, Any]:
    """
    Get statistics about traces in the current AOI selection.
    
    Returns:
        Dict with AOI trace statistics
    """
    try:
        if not st.session_state.get('area_selected') or st.session_state.get('area_selected_mode') != 'aoi':
            return {'error': 'No AOI selection active'}
        
        validation_result = validate_aoi_selection()
        if not validation_result['valid']:
            return {'error': validation_result['error']}
        
        aoi_df = validation_result['aoi_df']
        
        # Calculate statistics
        inline_range = aoi_df['inline'].max() - aoi_df['inline'].min() + 1
        xline_range = aoi_df['crossline'].max() - aoi_df['crossline'].min() + 1
        
        stats = {
            'total_traces': len(aoi_df),
            'inline_range': inline_range,
            'crossline_range': xline_range,
            'grid_coverage': len(aoi_df) / (inline_range * xline_range) if (inline_range * xline_range) > 0 else 0,
            'inline_min': int(aoi_df['inline'].min()),
            'inline_max': int(aoi_df['inline'].max()),
            'xline_min': int(aoi_df['crossline'].min()),
            'xline_max': int(aoi_df['crossline'].max())
        }
        
        return stats
        
    except Exception as e:
        logging.error(f"AOI statistics error: {str(e)}")
        return {'error': f'Statistics calculation error: {str(e)}'}

def initialize_aoi_session_state():
    """Initialize AOI-related session state variables if not present."""
    # Initialize AOI boundary variables
    aoi_vars = ['aoi_inline_min', 'aoi_inline_max', 'aoi_xline_min', 'aoi_xline_max']
    for var in aoi_vars:
        if not hasattr(st.session_state, var):
            setattr(st.session_state, var, None)

    # Initialize AOI processing option
    if not hasattr(st.session_state, 'aoi_processing_option'):
        st.session_state.aoi_processing_option = "Full AOI"

    # Initialize AOI bounds dictionary
    if not hasattr(st.session_state, 'aoi_bounds'):
        st.session_state.aoi_bounds = None

    # Initialize AOI confirmation state variables
    if not hasattr(st.session_state, 'aoi_boundaries_confirmed'):
        st.session_state.aoi_boundaries_confirmed = False

    if not hasattr(st.session_state, 'aoi_selection_confirmed'):
        st.session_state.aoi_selection_confirmed = False

    # Initialize enhanced AOI control variables
    if not hasattr(st.session_state, 'aoi_definition_mode'):
        st.session_state.aoi_definition_mode = "suggested"

    if not hasattr(st.session_state, 'aoi_auto_initialized'):
        st.session_state.aoi_auto_initialized = False

    if not hasattr(st.session_state, 'aoi_manual_input_enabled'):
        st.session_state.aoi_manual_input_enabled = True

def reset_aoi_selection():
    """Reset AOI selection and related session state."""
    st.session_state.area_selected = False
    st.session_state.area_selected_mode = None
    st.session_state.area_selected_details = None
    st.session_state.selected_indices = []
    st.session_state.aoi_bounds = None

    # Reset AOI confirmation states
    st.session_state.aoi_boundaries_confirmed = False
    st.session_state.aoi_selection_confirmed = False

    # Reset validation states
    st.session_state.data_validation_status = None
    st.session_state.data_ready_for_analysis = False
    st.session_state.validation_required = True

    logging.info("AOI selection reset")

def get_aoi_boundary_suggestions(headers_df: pd.DataFrame) -> Dict[str, Any]:
    """
    Get suggestions for AOI boundaries based on data distribution.

    Args:
        headers_df: DataFrame with seismic headers

    Returns:
        Dict with boundary suggestions
    """
    try:
        suggestions = {}

        # Get data ranges
        inline_min, inline_max = headers_df['inline'].min(), headers_df['inline'].max()
        xline_min, xline_max = headers_df['crossline'].min(), headers_df['crossline'].max()

        # Calculate center and quarter points
        inline_center = (inline_min + inline_max) // 2
        xline_center = (xline_min + xline_max) // 2

        inline_quarter = (inline_max - inline_min) // 4
        xline_quarter = (xline_max - xline_min) // 4

        suggestions['full_survey'] = {
            'inline_min': inline_min, 'inline_max': inline_max,
            'xline_min': xline_min, 'xline_max': xline_max,
            'description': 'Full survey area'
        }

        suggestions['center_half'] = {
            'inline_min': inline_center - inline_quarter,
            'inline_max': inline_center + inline_quarter,
            'xline_min': xline_center - xline_quarter,
            'xline_max': xline_center + xline_quarter,
            'description': 'Center 50% of survey'
        }

        suggestions['center_quarter'] = {
            'inline_min': inline_center - inline_quarter // 2,
            'inline_max': inline_center + inline_quarter // 2,
            'xline_min': xline_center - xline_quarter // 2,
            'xline_max': xline_center + xline_quarter // 2,
            'description': 'Center 25% of survey'
        }

        return suggestions

    except Exception as e:
        logging.error(f"Error generating AOI boundary suggestions: {str(e)}")
        return {}

def show_aoi_boundary_suggestions():
    """Display enhanced AOI boundary suggestions to help users."""
    if not st.session_state.get('headers_df') is not None:
        return

    headers_df = st.session_state.headers_df
    suggestions = get_aoi_boundary_suggestions(headers_df)

    if suggestions:
        st.markdown("**🚀 Quick AOI Presets:**")
        st.caption("Click any preset to automatically set AOI boundaries")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📊 Full Survey", help="Select entire survey area", use_container_width=True):
                preset = suggestions['full_survey']
                st.session_state.aoi_inline_min = preset['inline_min']
                st.session_state.aoi_inline_max = preset['inline_max']
                st.session_state.aoi_xline_min = preset['xline_min']
                st.session_state.aoi_xline_max = preset['xline_max']
                st.session_state.aoi_definition_mode = "suggested"
                st.rerun()
            st.caption(f"IL: {suggestions['full_survey']['inline_min']}-{suggestions['full_survey']['inline_max']}")
            st.caption(f"XL: {suggestions['full_survey']['xline_min']}-{suggestions['full_survey']['xline_max']}")

        with col2:
            if st.button("🎯 Center 50%", help="Select center 50% of survey", use_container_width=True):
                preset = suggestions['center_half']
                st.session_state.aoi_inline_min = preset['inline_min']
                st.session_state.aoi_inline_max = preset['inline_max']
                st.session_state.aoi_xline_min = preset['xline_min']
                st.session_state.aoi_xline_max = preset['xline_max']
                st.session_state.aoi_definition_mode = "suggested"
                st.rerun()
            st.caption(f"IL: {suggestions['center_half']['inline_min']}-{suggestions['center_half']['inline_max']}")
            st.caption(f"XL: {suggestions['center_half']['xline_min']}-{suggestions['center_half']['xline_max']}")

        with col3:
            if st.button("🔍 Center 25%", help="Select center 25% of survey", use_container_width=True):
                preset = suggestions['center_quarter']
                st.session_state.aoi_inline_min = preset['inline_min']
                st.session_state.aoi_inline_max = preset['inline_max']
                st.session_state.aoi_xline_min = preset['xline_min']
                st.session_state.aoi_xline_max = preset['xline_max']
                st.session_state.aoi_definition_mode = "suggested"
                st.rerun()
            st.caption(f"IL: {suggestions['center_quarter']['inline_min']}-{suggestions['center_quarter']['inline_max']}")
            st.caption(f"XL: {suggestions['center_quarter']['xline_min']}-{suggestions['center_quarter']['xline_max']}")

def clear_aoi_boundaries():
    """Clear all AOI boundary values to allow fresh manual input."""
    st.session_state.aoi_inline_min = None
    st.session_state.aoi_inline_max = None
    st.session_state.aoi_xline_min = None
    st.session_state.aoi_xline_max = None
    st.session_state.aoi_bounds = None
    st.session_state.area_selected = False
    st.session_state.area_selected_mode = None
    st.session_state.aoi_boundaries_confirmed = False
    st.session_state.aoi_selection_confirmed = False
    logging.info("AOI boundaries cleared for manual input")

def initialize_suggested_aoi_boundaries(headers_df: pd.DataFrame):
    """Initialize AOI boundaries with suggested values based on data."""
    try:
        # Get data ranges
        actual_inline_min = headers_df['inline'].min()
        actual_inline_max = headers_df['inline'].max()
        actual_xline_min = headers_df['crossline'].min()
        actual_xline_max = headers_df['crossline'].max()

        # Calculate centered 25% subset as meaningful default
        inline_range = actual_inline_max - actual_inline_min
        xline_range = actual_xline_max - actual_xline_min

        if inline_range >= 4:  # Ensure we have enough range for 25%
            st.session_state.aoi_inline_min = actual_inline_min + (inline_range // 4)
            st.session_state.aoi_inline_max = actual_inline_max - (inline_range // 4)
        else:
            # For small datasets, use center 50%
            st.session_state.aoi_inline_min = actual_inline_min + (inline_range // 4)
            st.session_state.aoi_inline_max = actual_inline_max - (inline_range // 4)

        if xline_range >= 4:
            st.session_state.aoi_xline_min = actual_xline_min + (xline_range // 4)
            st.session_state.aoi_xline_max = actual_xline_max - (xline_range // 4)
        else:
            st.session_state.aoi_xline_min = actual_xline_min + (xline_range // 4)
            st.session_state.aoi_xline_max = actual_xline_max - (xline_range // 4)

        st.session_state.aoi_auto_initialized = True
        logging.info("AOI boundaries initialized with suggested values")

    except Exception as e:
        logging.error(f"Error initializing suggested AOI boundaries: {str(e)}")
        # Fallback to full range
        st.session_state.aoi_inline_min = actual_inline_min
        st.session_state.aoi_inline_max = actual_inline_max
        st.session_state.aoi_xline_min = actual_xline_min
        st.session_state.aoi_xline_max = actual_xline_max

def validate_aoi_size_performance(trace_count: int) -> Dict[str, Any]:
    """
    Validate AOI size for performance considerations.

    Args:
        trace_count: Number of traces in the AOI

    Returns:
        Dict with performance validation results
    """
    result = {'valid': True, 'warnings': [], 'recommendations': []}

    # Define performance thresholds
    LARGE_AOI_THRESHOLD = 50000  # traces
    VERY_LARGE_AOI_THRESHOLD = 100000  # traces

    if trace_count > VERY_LARGE_AOI_THRESHOLD:
        result['warnings'].append(f"Very large AOI ({trace_count:,} traces) may cause performance issues")
        result['recommendations'].append("Consider reducing AOI size or processing in smaller batches")
    elif trace_count > LARGE_AOI_THRESHOLD:
        result['warnings'].append(f"Large AOI ({trace_count:,} traces) may take longer to process")
        result['recommendations'].append("Ensure sufficient memory and processing time")

    return result

def show_aoi_preview():
    """Show a preview of the current AOI selection with visual indicators."""
    try:
        if (st.session_state.aoi_inline_min is None or
            st.session_state.aoi_inline_max is None or
            st.session_state.aoi_xline_min is None or
            st.session_state.aoi_xline_max is None):
            st.info("🔍 **AOI Preview:** Define all boundary values to see preview")
            return

        # Get data ranges
        headers_df = st.session_state.headers_df
        actual_inline_min = headers_df['inline'].min()
        actual_inline_max = headers_df['inline'].max()
        actual_xline_min = headers_df['crossline'].min()
        actual_xline_max = headers_df['crossline'].max()

        # Calculate AOI dimensions
        aoi_inline_span = st.session_state.aoi_inline_max - st.session_state.aoi_inline_min + 1
        aoi_xline_span = st.session_state.aoi_xline_max - st.session_state.aoi_xline_min + 1
        total_inline_span = actual_inline_max - actual_inline_min + 1
        total_xline_span = actual_xline_max - actual_xline_min + 1

        # Calculate coverage percentages
        inline_coverage = (aoi_inline_span / total_inline_span) * 100
        xline_coverage = (aoi_xline_span / total_xline_span) * 100

        st.markdown("**🔍 AOI Preview:**")

        # Create visual representation
        preview_col1, preview_col2 = st.columns(2)

        with preview_col1:
            st.markdown("**Inline Coverage:**")
            st.progress(inline_coverage / 100)
            st.caption(f"{inline_coverage:.1f}% of total inline range")

        with preview_col2:
            st.markdown("**Crossline Coverage:**")
            st.progress(xline_coverage / 100)
            st.caption(f"{xline_coverage:.1f}% of total crossline range")

        # Show position within survey
        inline_start_pct = ((st.session_state.aoi_inline_min - actual_inline_min) / total_inline_span) * 100
        xline_start_pct = ((st.session_state.aoi_xline_min - actual_xline_min) / total_xline_span) * 100

        st.markdown("**📍 Position in Survey:**")
        position_col1, position_col2 = st.columns(2)

        with position_col1:
            st.write(f"• Inline starts at {inline_start_pct:.1f}% of survey")
            st.write(f"• Covers {aoi_inline_span:,} inlines")

        with position_col2:
            st.write(f"• Crossline starts at {xline_start_pct:.1f}% of survey")
            st.write(f"• Covers {aoi_xline_span:,} crosslines")

    except Exception as e:
        logging.error(f"Error showing AOI preview: {str(e)}")
        st.warning("Unable to generate AOI preview")

def export_aoi_definition() -> Optional[str]:
    """
    Export current AOI definition to a JSON string.

    Returns:
        JSON string containing AOI definition or None if no AOI defined
    """
    try:
        if (st.session_state.aoi_inline_min is None or
            st.session_state.aoi_inline_max is None or
            st.session_state.aoi_xline_min is None or
            st.session_state.aoi_xline_max is None):
            return None

        aoi_definition = {
            'version': '1.0',
            'created_at': datetime.now().isoformat(),
            'aoi_boundaries': {
                'inline_min': int(st.session_state.aoi_inline_min),
                'inline_max': int(st.session_state.aoi_inline_max),
                'xline_min': int(st.session_state.aoi_xline_min),
                'xline_max': int(st.session_state.aoi_xline_max)
            },
            'aoi_metadata': {
                'definition_mode': st.session_state.get('aoi_definition_mode', 'manual'),
                'auto_initialized': st.session_state.get('aoi_auto_initialized', False),
                'description': f"AOI: IL {st.session_state.aoi_inline_min}-{st.session_state.aoi_inline_max}, XL {st.session_state.aoi_xline_min}-{st.session_state.aoi_xline_max}"
            }
        }

        # Add validation result if available
        validation_result = validate_aoi_selection()
        if validation_result['valid']:
            aoi_definition['validation_info'] = {
                'trace_count': validation_result['trace_count'],
                'validated_at': datetime.now().isoformat()
            }

        return json.dumps(aoi_definition, indent=2)

    except Exception as e:
        logging.error(f"Error exporting AOI definition: {str(e)}")
        return None

def import_aoi_definition(json_data: str) -> Dict[str, Any]:
    """
    Import AOI definition from JSON string.

    Args:
        json_data: JSON string containing AOI definition

    Returns:
        Dict with import results
    """
    try:
        aoi_definition = json.loads(json_data)

        # Validate JSON structure
        required_keys = ['version', 'aoi_boundaries']
        for key in required_keys:
            if key not in aoi_definition:
                return {'success': False, 'error': f'Missing required field: {key}'}

        boundaries = aoi_definition['aoi_boundaries']
        required_boundary_keys = ['inline_min', 'inline_max', 'xline_min', 'xline_max']
        for key in required_boundary_keys:
            if key not in boundaries:
                return {'success': False, 'error': f'Missing boundary field: {key}'}

        # Validate boundary values
        try:
            inline_min = int(boundaries['inline_min'])
            inline_max = int(boundaries['inline_max'])
            xline_min = int(boundaries['xline_min'])
            xline_max = int(boundaries['xline_max'])
        except (ValueError, TypeError):
            return {'success': False, 'error': 'Invalid boundary values - must be integers'}

        # Check boundary logic
        if inline_min >= inline_max:
            return {'success': False, 'error': 'Inline minimum must be less than maximum'}
        if xline_min >= xline_max:
            return {'success': False, 'error': 'Crossline minimum must be less than maximum'}

        # Apply boundaries to session state
        st.session_state.aoi_inline_min = inline_min
        st.session_state.aoi_inline_max = inline_max
        st.session_state.aoi_xline_min = xline_min
        st.session_state.aoi_xline_max = xline_max

        # Apply metadata if available
        if 'aoi_metadata' in aoi_definition:
            metadata = aoi_definition['aoi_metadata']
            if 'definition_mode' in metadata:
                st.session_state.aoi_definition_mode = metadata['definition_mode']
            if 'auto_initialized' in metadata:
                st.session_state.aoi_auto_initialized = metadata['auto_initialized']

        # Update AOI bounds
        st.session_state.aoi_bounds = {
            'inline_min': inline_min,
            'inline_max': inline_max,
            'xline_min': xline_min,
            'xline_max': xline_max
        }

        # Reset confirmation states
        st.session_state.aoi_boundaries_confirmed = False
        st.session_state.aoi_selection_confirmed = False
        st.session_state.area_selected = False
        st.session_state.area_selected_mode = None

        return {
            'success': True,
            'boundaries': boundaries,
            'metadata': aoi_definition.get('aoi_metadata', {}),
            'created_at': aoi_definition.get('created_at', 'Unknown')
        }

    except json.JSONDecodeError as e:
        return {'success': False, 'error': f'Invalid JSON format: {str(e)}'}
    except Exception as e:
        logging.error(f"Error importing AOI definition: {str(e)}")
        return {'success': False, 'error': f'Import error: {str(e)}'}

def show_aoi_import_export_controls():
    """Display AOI import/export controls in the UI."""
    st.markdown("**💾 Save & Load AOI Definitions:**")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**Export Current AOI:**")
        if st.button("📤 Export AOI", help="Export current AOI definition to file"):
            aoi_json = export_aoi_definition()
            if aoi_json:
                # Create download button
                filename = f"aoi_definition_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                st.download_button(
                    label="💾 Download AOI File",
                    data=aoi_json,
                    file_name=filename,
                    mime="application/json",
                    help="Click to download the AOI definition file"
                )
                st.success("✅ AOI definition ready for download!")
            else:
                st.error("❌ No valid AOI definition to export. Please define AOI boundaries first.")

    with col2:
        st.markdown("**Import AOI Definition:**")
        uploaded_file = st.file_uploader(
            "Choose AOI definition file",
            type=['json'],
            help="Upload a previously exported AOI definition file",
            key="aoi_import_file"
        )

        if uploaded_file is not None:
            try:
                json_data = uploaded_file.read().decode('utf-8')
                import_result = import_aoi_definition(json_data)

                if import_result['success']:
                    st.success("✅ AOI definition imported successfully!")
                    st.info(f"📅 Created: {import_result['created_at']}")

                    # Show imported boundaries
                    boundaries = import_result['boundaries']
                    st.write(f"**Imported AOI:** IL {boundaries['inline_min']}-{boundaries['inline_max']}, XL {boundaries['xline_min']}-{boundaries['xline_max']}")

                    # Trigger rerun to update UI
                    st.rerun()
                else:
                    st.error(f"❌ Import failed: {import_result['error']}")

            except Exception as e:
                st.error(f"❌ Error reading file: {str(e)}")

def create_aoi_visualization() -> Optional[go.Figure]:
    """
    Create a visual representation of the AOI selection within the survey area.

    Returns:
        Plotly figure showing AOI boundaries or None if data not available
    """
    try:
        if not st.session_state.get('headers_df') is not None:
            return None

        headers_df = st.session_state.headers_df

        # Get survey bounds
        survey_inline_min = headers_df['inline'].min()
        survey_inline_max = headers_df['inline'].max()
        survey_xline_min = headers_df['crossline'].min()
        survey_xline_max = headers_df['crossline'].max()

        # Create figure
        fig = go.Figure()

        # Add survey area outline
        fig.add_trace(go.Scatter(
            x=[survey_xline_min, survey_xline_max, survey_xline_max, survey_xline_min, survey_xline_min],
            y=[survey_inline_min, survey_inline_min, survey_inline_max, survey_inline_max, survey_inline_min],
            mode='lines',
            name='Survey Area',
            line=dict(color='lightgray', width=2, dash='dash'),
            hovertemplate='Survey Boundary<extra></extra>'
        ))

        # Add AOI area if defined
        if (st.session_state.aoi_inline_min is not None and
            st.session_state.aoi_inline_max is not None and
            st.session_state.aoi_xline_min is not None and
            st.session_state.aoi_xline_max is not None):

            aoi_inline_min = st.session_state.aoi_inline_min
            aoi_inline_max = st.session_state.aoi_inline_max
            aoi_xline_min = st.session_state.aoi_xline_min
            aoi_xline_max = st.session_state.aoi_xline_max

            # Add AOI rectangle
            fig.add_trace(go.Scatter(
                x=[aoi_xline_min, aoi_xline_max, aoi_xline_max, aoi_xline_min, aoi_xline_min],
                y=[aoi_inline_min, aoi_inline_min, aoi_inline_max, aoi_inline_max, aoi_inline_min],
                mode='lines',
                fill='toself',
                name='AOI Selection',
                line=dict(color='red', width=3),
                fillcolor='rgba(255, 0, 0, 0.2)',
                hovertemplate=f'AOI: IL {aoi_inline_min}-{aoi_inline_max}<br>XL {aoi_xline_min}-{aoi_xline_max}<extra></extra>'
            ))

            # Add AOI center point
            center_inline = (aoi_inline_min + aoi_inline_max) / 2
            center_xline = (aoi_xline_min + aoi_xline_max) / 2

            fig.add_trace(go.Scatter(
                x=[center_xline],
                y=[center_inline],
                mode='markers',
                name='AOI Center',
                marker=dict(color='red', size=10, symbol='cross'),
                hovertemplate=f'AOI Center<br>IL: {center_inline:.0f}<br>XL: {center_xline:.0f}<extra></extra>'
            ))

        # Update layout
        fig.update_layout(
            title='AOI Selection Visualization',
            xaxis_title='Crossline',
            yaxis_title='Inline',
            width=600,
            height=500,
            showlegend=True,
            hovermode='closest',
            plot_bgcolor='white',
            xaxis=dict(
                showgrid=True,
                gridwidth=1,
                gridcolor='lightgray',
                zeroline=False
            ),
            yaxis=dict(
                showgrid=True,
                gridwidth=1,
                gridcolor='lightgray',
                zeroline=False,
                scaleanchor="x",
                scaleratio=1
            )
        )

        return fig

    except Exception as e:
        logging.error(f"Error creating AOI visualization: {str(e)}")
        return None

def create_aoi_coverage_heatmap() -> Optional[go.Figure]:
    """
    Create a heatmap showing trace density and AOI coverage.

    Returns:
        Plotly figure showing coverage heatmap or None if data not available
    """
    try:
        if not st.session_state.get('headers_df') is not None:
            return None

        headers_df = st.session_state.headers_df

        # Create a simplified grid for visualization (to avoid performance issues)
        inline_range = headers_df['inline'].max() - headers_df['inline'].min()
        xline_range = headers_df['crossline'].max() - headers_df['crossline'].min()

        # Determine grid resolution based on data size
        max_grid_size = 100  # Maximum grid points per dimension
        inline_step = max(1, inline_range // max_grid_size)
        xline_step = max(1, xline_range // max_grid_size)

        # Create grid
        inline_bins = np.arange(headers_df['inline'].min(),
                               headers_df['inline'].max() + inline_step,
                               inline_step)
        xline_bins = np.arange(headers_df['crossline'].min(),
                              headers_df['crossline'].max() + xline_step,
                              xline_step)

        # Create 2D histogram
        hist, xedges, yedges = np.histogram2d(
            headers_df['crossline'],
            headers_df['inline'],
            bins=[xline_bins, inline_bins]
        )

        # Create heatmap
        fig = go.Figure(data=go.Heatmap(
            z=hist.T,
            x=xedges[:-1],
            y=yedges[:-1],
            colorscale='Blues',
            name='Trace Density',
            hovertemplate='XL: %{x}<br>IL: %{y}<br>Traces: %{z}<extra></extra>'
        ))

        # Add AOI overlay if defined
        if (st.session_state.aoi_inline_min is not None and
            st.session_state.aoi_inline_max is not None and
            st.session_state.aoi_xline_min is not None and
            st.session_state.aoi_xline_max is not None):

            aoi_inline_min = st.session_state.aoi_inline_min
            aoi_inline_max = st.session_state.aoi_inline_max
            aoi_xline_min = st.session_state.aoi_xline_min
            aoi_xline_max = st.session_state.aoi_xline_max

            # Add AOI rectangle overlay
            fig.add_shape(
                type="rect",
                x0=aoi_xline_min, y0=aoi_inline_min,
                x1=aoi_xline_max, y1=aoi_inline_max,
                line=dict(color="red", width=3),
                fillcolor="rgba(255, 0, 0, 0.1)"
            )

        # Update layout
        fig.update_layout(
            title='Survey Coverage with AOI Selection',
            xaxis_title='Crossline',
            yaxis_title='Inline',
            width=600,
            height=500,
            yaxis=dict(scaleanchor="x", scaleratio=1)
        )

        return fig

    except Exception as e:
        logging.error(f"Error creating AOI coverage heatmap: {str(e)}")
        return None

def show_aoi_visualization():
    """Display AOI visualization components."""
    st.markdown("**🗺️ AOI Visualization:**")

    # Create tabs for different visualizations
    tab1, tab2 = st.tabs(["📍 AOI Map", "🔥 Coverage Heatmap"])

    with tab1:
        st.markdown("**AOI Selection Map**")
        fig = create_aoi_visualization()
        if fig:
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("Load seismic data and define AOI boundaries to see visualization")

    with tab2:
        st.markdown("**Survey Coverage Heatmap**")
        fig = create_aoi_coverage_heatmap()
        if fig:
            st.plotly_chart(fig, use_container_width=True)
            st.caption("Blue intensity shows trace density. Red outline shows AOI selection.")
        else:
            st.info("Load seismic data to see coverage heatmap")

def get_aoi_templates(headers_df: pd.DataFrame) -> Dict[str, Dict]:
    """
    Generate AOI templates for common geological scenarios.

    Args:
        headers_df: DataFrame with seismic headers

    Returns:
        Dict of AOI templates
    """
    try:
        # Get survey bounds
        inline_min = headers_df['inline'].min()
        inline_max = headers_df['inline'].max()
        xline_min = headers_df['crossline'].min()
        xline_max = headers_df['crossline'].max()

        inline_range = inline_max - inline_min
        xline_range = xline_max - xline_min

        templates = {}

        # 1. Structural Analysis Templates
        templates['structural_regional'] = {
            'name': '🏔️ Regional Structural Analysis',
            'description': 'Large area for regional structural interpretation',
            'inline_min': inline_min + inline_range // 8,
            'inline_max': inline_max - inline_range // 8,
            'xline_min': xline_min + xline_range // 8,
            'xline_max': xline_max - xline_range // 8,
            'category': 'Structural',
            'use_case': 'Regional fault mapping and structural trends'
        }

        templates['structural_detailed'] = {
            'name': '🔍 Detailed Structural Analysis',
            'description': 'Focused area for detailed structural interpretation',
            'inline_min': inline_min + inline_range // 3,
            'inline_max': inline_max - inline_range // 3,
            'xline_min': xline_min + xline_range // 3,
            'xline_max': xline_max - xline_range // 3,
            'category': 'Structural',
            'use_case': 'Detailed fault analysis and small-scale structures'
        }

        # 2. Reservoir Analysis Templates
        templates['reservoir_prospect'] = {
            'name': '🛢️ Reservoir Prospect',
            'description': 'Focused area for reservoir characterization',
            'inline_min': inline_min + inline_range // 2 - inline_range // 6,
            'inline_max': inline_min + inline_range // 2 + inline_range // 6,
            'xline_min': xline_min + xline_range // 2 - xline_range // 6,
            'xline_max': xline_min + xline_range // 2 + xline_range // 6,
            'category': 'Reservoir',
            'use_case': 'Reservoir quality and hydrocarbon indicators'
        }

        templates['reservoir_development'] = {
            'name': '🏗️ Field Development',
            'description': 'Area for field development planning',
            'inline_min': inline_min + inline_range // 4,
            'inline_max': inline_max - inline_range // 4,
            'xline_min': xline_min + xline_range // 4,
            'xline_max': xline_max - xline_range // 4,
            'category': 'Reservoir',
            'use_case': 'Well placement and development optimization'
        }

        # 3. Stratigraphic Analysis Templates
        templates['strat_sequence'] = {
            'name': '📚 Sequence Stratigraphy',
            'description': 'Linear transect for sequence analysis',
            'inline_min': inline_min + inline_range // 3,
            'inline_max': inline_max - inline_range // 3,
            'xline_min': xline_min + xline_range // 2 - xline_range // 10,
            'xline_max': xline_min + xline_range // 2 + xline_range // 10,
            'category': 'Stratigraphy',
            'use_case': 'Depositional sequence analysis'
        }

        templates['strat_facies'] = {
            'name': '🌊 Facies Analysis',
            'description': 'Area for depositional facies mapping',
            'inline_min': inline_min + inline_range // 5,
            'inline_max': inline_max - inline_range // 5,
            'xline_min': xline_min + xline_range // 5,
            'xline_max': xline_max - xline_range // 5,
            'category': 'Stratigraphy',
            'use_case': 'Sedimentary facies and environment analysis'
        }

        # 4. Quality Control Templates
        templates['qc_corner'] = {
            'name': '📐 Corner QC',
            'description': 'Survey corner for quality control',
            'inline_min': inline_min,
            'inline_max': inline_min + inline_range // 5,
            'xline_min': xline_min,
            'xline_max': xline_min + xline_range // 5,
            'category': 'Quality Control',
            'use_case': 'Data quality assessment at survey edges'
        }

        templates['qc_center'] = {
            'name': '🎯 Center QC',
            'description': 'Survey center for quality control',
            'inline_min': inline_min + inline_range // 2 - inline_range // 10,
            'inline_max': inline_min + inline_range // 2 + inline_range // 10,
            'xline_min': xline_min + xline_range // 2 - xline_range // 10,
            'xline_max': xline_min + xline_range // 2 + xline_range // 10,
            'category': 'Quality Control',
            'use_case': 'Data quality assessment in survey center'
        }

        # 5. Processing Templates
        templates['processing_test'] = {
            'name': '⚙️ Processing Test',
            'description': 'Small area for processing parameter testing',
            'inline_min': inline_min + inline_range // 2 - inline_range // 20,
            'inline_max': inline_min + inline_range // 2 + inline_range // 20,
            'xline_min': xline_min + xline_range // 2 - xline_range // 20,
            'xline_max': xline_min + xline_range // 2 + xline_range // 20,
            'category': 'Processing',
            'use_case': 'Algorithm testing and parameter optimization'
        }

        # Ensure all values are integers and within bounds
        for template_key, template in templates.items():
            template['inline_min'] = max(inline_min, int(template['inline_min']))
            template['inline_max'] = min(inline_max, int(template['inline_max']))
            template['xline_min'] = max(xline_min, int(template['xline_min']))
            template['xline_max'] = min(xline_max, int(template['xline_max']))

        return templates

    except Exception as e:
        logging.error(f"Error generating AOI templates: {str(e)}")
        return {}

def show_aoi_templates():
    """Display AOI templates for common geological scenarios."""
    if not st.session_state.get('headers_df') is not None:
        st.info("Load seismic data to see AOI templates")
        return

    headers_df = st.session_state.headers_df
    templates = get_aoi_templates(headers_df)

    if not templates:
        st.warning("Unable to generate AOI templates")
        return

    st.markdown("**🎨 AOI Templates for Common Scenarios:**")
    st.caption("Click any template to apply predefined AOI boundaries for specific geological analysis")

    # Group templates by category
    categories = {}
    for template_key, template in templates.items():
        category = template['category']
        if category not in categories:
            categories[category] = []
        categories[category].append((template_key, template))

    # Display templates by category
    for category, category_templates in categories.items():
        st.markdown(f"**{category} Templates:**")

        # Create columns for templates in this category
        cols = st.columns(min(len(category_templates), 3))

        for i, (template_key, template) in enumerate(category_templates):
            col_idx = i % len(cols)

            with cols[col_idx]:
                if st.button(
                    template['name'],
                    help=f"{template['description']}\n\nUse case: {template['use_case']}",
                    key=f"template_{template_key}",
                    use_container_width=True
                ):
                    # Apply template
                    st.session_state.aoi_inline_min = template['inline_min']
                    st.session_state.aoi_inline_max = template['inline_max']
                    st.session_state.aoi_xline_min = template['xline_min']
                    st.session_state.aoi_xline_max = template['xline_max']
                    st.session_state.aoi_definition_mode = "suggested"

                    # Update AOI bounds
                    st.session_state.aoi_bounds = {
                        'inline_min': template['inline_min'],
                        'inline_max': template['inline_max'],
                        'xline_min': template['xline_min'],
                        'xline_max': template['xline_max']
                    }

                    st.success(f"✅ Applied {template['name']} template!")
                    st.rerun()

                # Show template details
                st.caption(f"IL: {template['inline_min']}-{template['inline_max']}")
                st.caption(f"XL: {template['xline_min']}-{template['xline_max']}")

        st.markdown("---")

def save_aoi_for_comparison(name: str) -> bool:
    """
    Save current AOI definition for comparison.

    Args:
        name: Name for the saved AOI

    Returns:
        True if saved successfully, False otherwise
    """
    try:
        if (st.session_state.aoi_inline_min is None or
            st.session_state.aoi_inline_max is None or
            st.session_state.aoi_xline_min is None or
            st.session_state.aoi_xline_max is None):
            return False

        # Initialize comparison list if not exists
        if 'aoi_comparison_list' not in st.session_state:
            st.session_state.aoi_comparison_list = []

        # Create AOI definition
        aoi_def = {
            'name': name,
            'inline_min': int(st.session_state.aoi_inline_min),
            'inline_max': int(st.session_state.aoi_inline_max),
            'xline_min': int(st.session_state.aoi_xline_min),
            'xline_max': int(st.session_state.aoi_xline_max),
            'definition_mode': st.session_state.get('aoi_definition_mode', 'manual'),
            'created_at': datetime.now().isoformat()
        }

        # Calculate statistics
        validation_result = validate_aoi_selection()
        if validation_result['valid']:
            aoi_def['trace_count'] = validation_result['trace_count']

            # Calculate additional statistics
            inline_span = aoi_def['inline_max'] - aoi_def['inline_min'] + 1
            xline_span = aoi_def['xline_max'] - aoi_def['xline_min'] + 1
            aoi_def['inline_span'] = inline_span
            aoi_def['xline_span'] = xline_span
            aoi_def['grid_size'] = inline_span * xline_span

            # Calculate coverage if headers_df is available
            if st.session_state.get('headers_df') is not None:
                headers_df = st.session_state.headers_df
                total_traces = len(headers_df)
                aoi_def['survey_coverage'] = (aoi_def['trace_count'] / total_traces) * 100
                aoi_def['grid_coverage'] = (aoi_def['trace_count'] / aoi_def['grid_size']) * 100

        # Add to comparison list (limit to 5 AOIs)
        st.session_state.aoi_comparison_list.append(aoi_def)
        if len(st.session_state.aoi_comparison_list) > 5:
            st.session_state.aoi_comparison_list.pop(0)

        return True

    except Exception as e:
        logging.error(f"Error saving AOI for comparison: {str(e)}")
        return False

def load_aoi_from_comparison(aoi_def: Dict) -> bool:
    """
    Load AOI definition from comparison list.

    Args:
        aoi_def: AOI definition dictionary

    Returns:
        True if loaded successfully, False otherwise
    """
    try:
        st.session_state.aoi_inline_min = aoi_def['inline_min']
        st.session_state.aoi_inline_max = aoi_def['inline_max']
        st.session_state.aoi_xline_min = aoi_def['xline_min']
        st.session_state.aoi_xline_max = aoi_def['xline_max']

        if 'definition_mode' in aoi_def:
            st.session_state.aoi_definition_mode = aoi_def['definition_mode']

        # Update AOI bounds
        st.session_state.aoi_bounds = {
            'inline_min': aoi_def['inline_min'],
            'inline_max': aoi_def['inline_max'],
            'xline_min': aoi_def['xline_min'],
            'xline_max': aoi_def['xline_max']
        }

        # Reset confirmation states
        st.session_state.aoi_boundaries_confirmed = False
        st.session_state.aoi_selection_confirmed = False
        st.session_state.area_selected = False
        st.session_state.area_selected_mode = None

        return True

    except Exception as e:
        logging.error(f"Error loading AOI from comparison: {str(e)}")
        return False

def show_aoi_comparison_tools():
    """Display AOI comparison tools."""
    st.markdown("**⚖️ AOI Comparison Tools:**")

    # Save current AOI
    col1, col2 = st.columns([2, 1])

    with col1:
        aoi_name = st.text_input(
            "AOI Name",
            placeholder="Enter name for current AOI",
            help="Give your AOI a descriptive name for comparison",
            key="aoi_comparison_name"
        )

    with col2:
        if st.button("💾 Save for Comparison", help="Save current AOI to comparison list"):
            if aoi_name.strip():
                if save_aoi_for_comparison(aoi_name.strip()):
                    st.success(f"✅ Saved '{aoi_name}' for comparison!")
                    st.rerun()
                else:
                    st.error("❌ Failed to save AOI. Please define valid boundaries first.")
            else:
                st.error("❌ Please enter a name for the AOI")

    # Show comparison list
    if st.session_state.get('aoi_comparison_list'):
        st.markdown("**📊 Saved AOIs for Comparison:**")

        comparison_list = st.session_state.aoi_comparison_list

        # Create comparison table
        comparison_data = []
        for i, aoi_def in enumerate(comparison_list):
            comparison_data.append({
                'Name': aoi_def['name'],
                'Inline Range': f"{aoi_def['inline_min']}-{aoi_def['inline_max']}",
                'Crossline Range': f"{aoi_def['xline_min']}-{aoi_def['xline_max']}",
                'Traces': f"{aoi_def.get('trace_count', 'N/A'):,}" if aoi_def.get('trace_count') else 'N/A',
                'Grid Size': f"{aoi_def.get('inline_span', 0)} × {aoi_def.get('xline_span', 0)}",
                'Survey Coverage': f"{aoi_def.get('survey_coverage', 0):.1f}%" if aoi_def.get('survey_coverage') else 'N/A',
                'Mode': aoi_def.get('definition_mode', 'Unknown'),
                'Index': i
            })

        comparison_df = pd.DataFrame(comparison_data)

        # Display table
        st.dataframe(
            comparison_df.drop('Index', axis=1),
            use_container_width=True,
            hide_index=True
        )

        # Action buttons
        st.markdown("**Actions:**")
        action_cols = st.columns(len(comparison_list))

        for i, (col, aoi_def) in enumerate(zip(action_cols, comparison_list)):
            with col:
                if st.button(f"📥 Load", key=f"load_aoi_{i}", help=f"Load '{aoi_def['name']}'"):
                    if load_aoi_from_comparison(aoi_def):
                        st.success(f"✅ Loaded '{aoi_def['name']}'!")
                        st.rerun()
                    else:
                        st.error("❌ Failed to load AOI")

                if st.button(f"🗑️ Remove", key=f"remove_aoi_{i}", help=f"Remove '{aoi_def['name']}'"):
                    st.session_state.aoi_comparison_list.pop(i)
                    st.rerun()

        # Clear all button
        if st.button("🗑️ Clear All Comparisons", help="Remove all saved AOIs"):
            st.session_state.aoi_comparison_list = []
            st.rerun()

        # Comparison visualization
        if len(comparison_list) > 1:
            st.markdown("**📈 Comparison Chart:**")

            # Create comparison chart
            fig = go.Figure()

            # Add bars for each AOI
            names = [aoi['name'] for aoi in comparison_list]
            trace_counts = [aoi.get('trace_count', 0) for aoi in comparison_list]

            fig.add_trace(go.Bar(
                x=names,
                y=trace_counts,
                name='Trace Count',
                text=[f"{count:,}" for count in trace_counts],
                textposition='auto'
            ))

            fig.update_layout(
                title='AOI Comparison - Trace Counts',
                xaxis_title='AOI Name',
                yaxis_title='Number of Traces',
                height=400
            )

            st.plotly_chart(fig, use_container_width=True)

    else:
        st.info("💡 Save AOIs to compare different selections side-by-side")
