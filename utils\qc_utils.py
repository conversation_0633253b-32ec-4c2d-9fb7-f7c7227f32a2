"""
Quality Control utilities for the WOSS Seismic Analysis Tool.

This module contains functions for quality control of seismic data.
"""

import numpy as np
import matplotlib.pyplot as plt
import streamlit as st
import pandas as pd
from scipy import signal
import logging

def plot_amplitude_spectrum(trace_data, ax=None, title="Amplitude Spectrum"):
    """
    Generate an amplitude spectrum plot for a seismic trace.
    
    Args:
        trace_data (numpy.ndarray): Seismic trace data
        ax (matplotlib.axes.Axes, optional): Matplotlib axes to plot on
        title (str, optional): Plot title
    
    Returns:
        matplotlib.axes.Axes: The axes with the plot
    """
    if trace_data is None or len(trace_data) == 0:
        if ax is None:
            fig, ax = plt.subplots()
        ax.text(0.5, 0.5, "No data to display", ha='center', va='center')
        return ax
    
    # Create axes if not provided
    if ax is None:
        fig, ax = plt.subplots()
    
    # Calculate FFT
    amp_spec = np.abs(np.fft.rfft(trace_data))
    freqs = np.fft.rfftfreq(len(trace_data))
    
    # Plot positive frequencies
    ax.plot(freqs, amp_spec)
    ax.set_title(title)
    ax.set_xlabel("Normalized Frequency")
    ax.set_ylabel("Amplitude")
    
    return ax

def calculate_snr(signal_data):
    """
    Calculate signal-to-noise ratio of a trace.
    
    Args:
        signal_data (numpy.ndarray): Seismic trace data
    
    Returns:
        float: Estimated SNR in dB
    """
    if signal_data is None or len(signal_data) == 0:
        return 0.0
    
    # Estimate noise level using median absolute deviation
    # This is a robust estimator of noise
    noise_estimate = 1.4826 * np.median(np.abs(signal_data - np.median(signal_data)))
    
    # Calculate signal power
    signal_power = np.mean(np.square(signal_data))
    
    # Calculate noise power
    noise_power = noise_estimate**2
    
    # Avoid division by zero
    if noise_power < 1e-10:
        return 100.0  # Return a high value for very clean signals
    
    # Calculate SNR in dB
    snr_db = 10 * np.log10(signal_power / noise_power)
    
    return snr_db

def calculate_dominant_frequency(trace_data, dt):
    """
    Calculate the dominant frequency of a seismic trace.
    
    Args:
        trace_data (numpy.ndarray): Seismic trace data
        dt (float): Sampling interval in seconds
    
    Returns:
        float: Dominant frequency in Hz
    """
    if trace_data is None or len(trace_data) == 0 or dt <= 0:
        return 0.0
    
    # Calculate FFT
    amp_spec = np.abs(np.fft.rfft(trace_data))
    freqs = np.fft.rfftfreq(len(trace_data), d=dt)
    
    # Find the frequency with maximum amplitude
    max_idx = np.argmax(amp_spec)
    dom_freq = freqs[max_idx]
    
    return dom_freq

def calculate_bandwidth(trace_data, dt, threshold=0.5):
    """
    Calculate the bandwidth of a seismic trace.
    
    Args:
        trace_data (numpy.ndarray): Seismic trace data
        dt (float): Sampling interval in seconds
        threshold (float): Amplitude threshold relative to peak (0-1)
    
    Returns:
        tuple: (low_freq, high_freq, bandwidth) in Hz
    """
    if trace_data is None or len(trace_data) == 0 or dt <= 0:
        return (0.0, 0.0, 0.0)
    
    # Calculate FFT
    amp_spec = np.abs(np.fft.rfft(trace_data))
    freqs = np.fft.rfftfreq(len(trace_data), d=dt)
    
    # Find peak amplitude
    peak_amp = np.max(amp_spec)
    threshold_amp = peak_amp * threshold
    
    # Find frequencies above threshold
    above_threshold = amp_spec >= threshold_amp
    
    if not np.any(above_threshold):
        return (0.0, 0.0, 0.0)
    
    # Find low and high frequency bounds
    low_idx = np.min(np.where(above_threshold)[0])
    high_idx = np.max(np.where(above_threshold)[0])
    
    low_freq = freqs[low_idx]
    high_freq = freqs[high_idx]
    bandwidth = high_freq - low_freq
    
    return (low_freq, high_freq, bandwidth)

def display_qc_results(original_data, processed_data):
    """
    Display QC results for original and processed data.
    
    Args:
        original_data (list): List of original seismic traces
        processed_data (list): List of processed seismic traces
    
    Returns:
        dict: QC metrics
    """
    if not original_data or not processed_data:
        st.warning("No data available for QC.")
        return {}
    
    # Get sampling interval from session state
    dt = st.session_state.get("dt", 0.004)  # Default to 4ms if not available
    
    # Calculate QC metrics for each trace
    qc_metrics = []
    
    for i, (orig, proc) in enumerate(zip(original_data, processed_data)):
        # Calculate SNR
        orig_snr = calculate_snr(orig)
        proc_snr = calculate_snr(proc)
        
        # Calculate dominant frequency
        orig_dom_freq = calculate_dominant_frequency(orig, dt)
        proc_dom_freq = calculate_dominant_frequency(proc, dt)
        
        # Calculate bandwidth
        orig_low, orig_high, orig_bw = calculate_bandwidth(orig, dt)
        proc_low, proc_high, proc_bw = calculate_bandwidth(proc, dt)
        
        # Store metrics
        qc_metrics.append({
            "Trace": i + 1,
            "Original SNR (dB)": orig_snr,
            "Processed SNR (dB)": proc_snr,
            "SNR Change (dB)": proc_snr - orig_snr,
            "Original Dom. Freq. (Hz)": orig_dom_freq,
            "Processed Dom. Freq. (Hz)": proc_dom_freq,
            "Original Bandwidth (Hz)": orig_bw,
            "Processed Bandwidth (Hz)": proc_bw
        })
    
    # Create DataFrame
    qc_df = pd.DataFrame(qc_metrics)
    
    # Display metrics
    st.subheader("QC Metrics")
    st.dataframe(qc_df.style.format({
        "Original SNR (dB)": "{:.2f}",
        "Processed SNR (dB)": "{:.2f}",
        "SNR Change (dB)": "{:.2f}",
        "Original Dom. Freq. (Hz)": "{:.1f}",
        "Processed Dom. Freq. (Hz)": "{:.1f}",
        "Original Bandwidth (Hz)": "{:.1f}",
        "Processed Bandwidth (Hz)": "{:.1f}"
    }))
    
    # Calculate average metrics
    avg_metrics = {
        "Avg. Original SNR (dB)": np.mean([m["Original SNR (dB)"] for m in qc_metrics]),
        "Avg. Processed SNR (dB)": np.mean([m["Processed SNR (dB)"] for m in qc_metrics]),
        "Avg. SNR Change (dB)": np.mean([m["SNR Change (dB)"] for m in qc_metrics]),
        "Avg. Original Dom. Freq. (Hz)": np.mean([m["Original Dom. Freq. (Hz)"] for m in qc_metrics]),
        "Avg. Processed Dom. Freq. (Hz)": np.mean([m["Processed Dom. Freq. (Hz)"] for m in qc_metrics]),
        "Avg. Original Bandwidth (Hz)": np.mean([m["Original Bandwidth (Hz)"] for m in qc_metrics]),
        "Avg. Processed Bandwidth (Hz)": np.mean([m["Processed Bandwidth (Hz)"] for m in qc_metrics])
    }
    
    return {
        "metrics": qc_metrics,
        "averages": avg_metrics
    }
