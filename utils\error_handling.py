"""
Error Handling Utilities for WOSS Seismic Analysis Tool.

This module provides comprehensive error handling utilities for the area selection
and data processing workflows, including user-friendly error messages, logging,
and recovery suggestions.

Implements Phase 2 error handling enhancements.
"""

import streamlit as st
import logging
import traceback
from typing import Dict, List, Optional, Any, Callable
from functools import wraps

class WOSSError(Exception):
    """Base exception class for WOSS application errors."""
    
    def __init__(self, message: str, error_code: str = None, suggestions: List[str] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "WOSS_ERROR"
        self.suggestions = suggestions or []

class DataLoadError(WOSSError):
    """Exception for data loading errors."""
    pass

class ValidationError(WOSSError):
    """Exception for validation errors."""
    pass

class ProcessingError(WOSSError):
    """Exception for processing errors."""
    pass

class AOIError(WOSSError):
    """Exception for AOI-specific errors."""
    pass

def handle_errors(error_display_func: Callable = None, 
                 recovery_suggestions: List[str] = None,
                 log_error: bool = True):
    """
    Decorator for comprehensive error handling in WOSS functions.
    
    Args:
        error_display_func: Function to display errors (defaults to st.error)
        recovery_suggestions: List of recovery suggestions to show user
        log_error: Whether to log the error
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except WOSSError as e:
                # Handle WOSS-specific errors
                if log_error:
                    logging.error(f"WOSS Error in {func.__name__}: {e.message}", exc_info=True)
                
                display_func = error_display_func or st.error
                display_func(f"❌ {e.message}")
                
                # Show suggestions
                suggestions = e.suggestions + (recovery_suggestions or [])
                if suggestions:
                    st.write("**Suggestions:**")
                    for suggestion in suggestions:
                        st.write(f"• {suggestion}")
                
                return None
                
            except Exception as e:
                # Handle unexpected errors
                if log_error:
                    logging.error(f"Unexpected error in {func.__name__}: {str(e)}", exc_info=True)
                
                display_func = error_display_func or st.error
                display_func(f"❌ Unexpected error: {str(e)}")
                
                if recovery_suggestions:
                    st.write("**Suggestions:**")
                    for suggestion in recovery_suggestions:
                        st.write(f"• {suggestion}")
                
                return None
        return wrapper
    return decorator

def validate_session_state_prerequisites(required_vars: List[str]) -> Dict[str, Any]:
    """
    Validate that required session state variables exist.
    
    Args:
        required_vars: List of required session state variable names
        
    Returns:
        Dict with validation results
    """
    missing_vars = []
    invalid_vars = []
    
    for var in required_vars:
        if not hasattr(st.session_state, var):
            missing_vars.append(var)
        elif getattr(st.session_state, var) is None:
            invalid_vars.append(var)
    
    if missing_vars or invalid_vars:
        return {
            'valid': False,
            'missing_vars': missing_vars,
            'invalid_vars': invalid_vars,
            'error': f"Missing variables: {missing_vars}, Invalid variables: {invalid_vars}"
        }
    
    return {'valid': True}

def show_error_with_recovery(error_message: str, 
                           error_type: str = "Error",
                           recovery_actions: List[Dict[str, Any]] = None):
    """
    Display error message with recovery action buttons.
    
    Args:
        error_message: Error message to display
        error_type: Type of error (Error, Warning, Info)
        recovery_actions: List of recovery actions with 'label', 'action', and 'key'
    """
    # Display error based on type
    if error_type.lower() == "error":
        st.error(f"❌ {error_message}")
    elif error_type.lower() == "warning":
        st.warning(f"⚠️ {error_message}")
    else:
        st.info(f"ℹ️ {error_message}")
    
    # Show recovery actions
    if recovery_actions:
        st.write("**Available Actions:**")
        cols = st.columns(len(recovery_actions))
        
        for i, action in enumerate(recovery_actions):
            with cols[i]:
                if st.button(action['label'], key=action.get('key', f"recovery_{i}")):
                    if callable(action['action']):
                        action['action']()
                    else:
                        # Assume it's a step name
                        st.session_state.current_step = action['action']
                        st.rerun()

def create_error_context(operation: str, 
                        selection_mode: str = None,
                        trace_count: int = None) -> Dict[str, Any]:
    """
    Create error context information for debugging.
    
    Args:
        operation: Current operation being performed
        selection_mode: Current selection mode
        trace_count: Number of traces involved
        
    Returns:
        Dict with context information
    """
    context = {
        'operation': operation,
        'timestamp': pd.Timestamp.now().isoformat(),
        'session_state_keys': list(st.session_state.keys()),
        'gpu_available': st.session_state.get('GPU_AVAILABLE', False)
    }
    
    if selection_mode:
        context['selection_mode'] = selection_mode
    
    if trace_count:
        context['trace_count'] = trace_count
    
    # Add AOI-specific context if relevant
    if selection_mode == "By inline/crossline section (AOI)":
        context['aoi_bounds'] = st.session_state.get('aoi_bounds', {})
        context['aoi_processing_option'] = st.session_state.get('aoi_processing_option')
    
    return context

def log_error_with_context(error: Exception, 
                          context: Dict[str, Any],
                          operation: str):
    """
    Log error with comprehensive context information.
    
    Args:
        error: Exception that occurred
        context: Context information
        operation: Operation that failed
    """
    logging.error(f"Operation '{operation}' failed", extra={
        'error_type': type(error).__name__,
        'error_message': str(error),
        'context': context,
        'traceback': traceback.format_exc()
    })

def safe_execute_with_fallback(primary_func: Callable,
                              fallback_func: Callable = None,
                              error_message: str = "Operation failed") -> Any:
    """
    Safely execute a function with optional fallback.
    
    Args:
        primary_func: Primary function to execute
        fallback_func: Fallback function if primary fails
        error_message: Error message to display
        
    Returns:
        Result of primary or fallback function, or None
    """
    try:
        return primary_func()
    except Exception as e:
        logging.warning(f"Primary function failed: {str(e)}")
        
        if fallback_func:
            try:
                st.warning(f"⚠️ {error_message}. Trying alternative approach...")
                return fallback_func()
            except Exception as fallback_error:
                logging.error(f"Fallback function also failed: {str(fallback_error)}")
                st.error(f"❌ {error_message}. Alternative approach also failed.")
        else:
            st.error(f"❌ {error_message}: {str(e)}")
        
        return None

def validate_data_integrity(data: Any, 
                          data_type: str,
                          min_size: int = None,
                          max_size: int = None) -> Dict[str, Any]:
    """
    Validate data integrity with comprehensive checks.
    
    Args:
        data: Data to validate
        data_type: Expected data type description
        min_size: Minimum expected size
        max_size: Maximum expected size
        
    Returns:
        Dict with validation results
    """
    validation = {'valid': True, 'warnings': [], 'errors': []}
    
    try:
        if data is None:
            validation['valid'] = False
            validation['errors'].append(f"{data_type} is None")
            return validation
        
        # Check data type
        if hasattr(data, '__len__'):
            size = len(data)
            
            if min_size and size < min_size:
                validation['valid'] = False
                validation['errors'].append(f"{data_type} size ({size}) below minimum ({min_size})")
            
            if max_size and size > max_size:
                validation['warnings'].append(f"{data_type} size ({size}) above recommended maximum ({max_size})")
            
            if size == 0:
                validation['valid'] = False
                validation['errors'].append(f"{data_type} is empty")
        
        # Additional checks for numpy arrays
        if hasattr(data, 'dtype'):
            if hasattr(data, 'shape') and len(data.shape) == 0:
                validation['warnings'].append(f"{data_type} is a scalar array")
        
        return validation
        
    except Exception as e:
        validation['valid'] = False
        validation['errors'].append(f"Error validating {data_type}: {str(e)}")
        return validation
