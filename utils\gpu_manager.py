# -*- coding: utf-8 -*-
"""
Centralized GPU Management Module

This module provides a single point of control for GPU availability checking
and CuPy imports. It eliminates redundant import attempts and warning messages
by checking GPU availability once and caching the result.

All other modules should use this module instead of importing CuPy directly.
"""

import logging
import warnings
from typing import Optional, Any, Tuple, Dict

# Global variables to cache GPU state
_gpu_available: Optional[bool] = None
_cupy_module: Optional[Any] = None
_gpu_info_cache: Optional[Dict] = None

def _check_gpu_availability() -> Tuple[bool, Optional[Any]]:
    """
    Internal function to check GPU availability and import CuPy.
    
    Returns:
        Tuple[bool, Optional[Any]]: (gpu_available, cupy_module)
    """
    try:
        # Suppress CuPy warnings during import
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            import cupy as cp
            
        # Try to perform a simple operation to verify GPU is working
        try:
            test_array = cp.array([1, 2, 3])
            _ = cp.sum(test_array)
            logging.info("GPU is available and working correctly with CuPy.")
            return True, cp
        except Exception as e:
            logging.warning(f"CuPy imported but GPU not working correctly: {e}")
            return False, None
            
    except ImportError:
        # CuPy not installed
        logging.info("CuPy not available. GPU acceleration disabled.")
        return False, None
    except Exception as e:
        logging.warning(f"Unexpected error during GPU availability check: {e}")
        return False, None

def is_gpu_available() -> bool:
    """
    Check if GPU is available for computation.
    
    This function caches the result to avoid repeated checks and warnings.
    
    Returns:
        bool: True if GPU is available and working, False otherwise
    """
    global _gpu_available, _cupy_module
    
    if _gpu_available is None:
        _gpu_available, _cupy_module = _check_gpu_availability()
    
    return _gpu_available

def get_cupy() -> Optional[Any]:
    """
    Get the CuPy module if available.
    
    Returns:
        Optional[Any]: CuPy module if available, None otherwise
    """
    global _gpu_available, _cupy_module
    
    if _gpu_available is None:
        _gpu_available, _cupy_module = _check_gpu_availability()
    
    return _cupy_module if _gpu_available else None

def get_gpu_info() -> Optional[Dict]:
    """
    Get information about the available GPU(s).
    
    Returns:
        Optional[Dict]: Dictionary containing GPU information, or None if GPU is not available
    """
    global _gpu_info_cache
    
    if not is_gpu_available():
        return None
    
    if _gpu_info_cache is not None:
        return _gpu_info_cache
    
    cp = get_cupy()
    if cp is None:
        return None
    
    try:
        # Check if CUDA is available
        if not cp.cuda.is_available():
            logging.warning("CUDA is not available.")
            return None
        
        # Get device count
        device_count = cp.cuda.runtime.getDeviceCount()
        
        # Get information for each device
        devices = []
        for i in range(device_count):
            cp.cuda.runtime.setDevice(i)
            props = cp.cuda.runtime.getDeviceProperties(i)
            
            # Get memory info
            free, total = cp.cuda.runtime.memGetInfo()
            free_mb = free / (1024 ** 2)
            total_mb = total / (1024 ** 2)
            
            devices.append({
                'device_id': i,
                'name': props['name'].decode('utf-8'),
                'compute_capability': f"{props['major']}.{props['minor']}",
                'total_memory_mb': total_mb,
                'free_memory_mb': free_mb
            })
        
        _gpu_info_cache = {
            'device_count': device_count,
            'devices': devices,
            'cuda_version': cp.cuda.runtime.runtimeGetVersion()
        }
        
        return _gpu_info_cache
        
    except Exception as e:
        logging.warning(f"Error getting GPU information: {e}")
        return None

def get_gpu_memory_info() -> Tuple[float, float]:
    """
    Get GPU memory information.
    
    Returns:
        Tuple[float, float]: (free_memory_mb, total_memory_mb)
                            Returns (0, 0) if GPU not available
    """
    cp = get_cupy()
    if cp is None:
        return 0.0, 0.0
    
    try:
        free_bytes, total_bytes = cp.cuda.runtime.memGetInfo()
        free_mb = free_bytes / (1024 ** 2)
        total_mb = total_bytes / (1024 ** 2)
        return free_mb, total_mb
    except Exception as e:
        logging.warning(f"Error getting GPU memory info: {e}")
        return 0.0, 0.0

def get_suggested_batch_size() -> Tuple[int, float]:
    """
    Determine optimal batch size based on available GPU memory.

    This function matches the reference implementation's algorithm for calculating
    optimal batch sizes based on available GPU memory.

    Returns:
        Tuple[int, float]: (suggested_batch_size, free_memory_mb)
    """
    if is_gpu_available():
        try:
            free_mb, total_mb = get_gpu_memory_info()
            if free_mb > 0:
                # Use the reference implementation's algorithm:
                # Scale with available memory but keep within reasonable bounds
                # This matches the algorithm from initial_app/utils.py
                suggested_batch = min(max(10, int(free_mb / 100)), 50)
                logging.info(f"GPU available: suggesting batch size {suggested_batch} (free GPU RAM: {free_mb:.1f} MB)")
                return suggested_batch, free_mb
        except Exception as e:
            logging.warning(f"Could not determine optimal batch size from GPU memory: {e}")

    # Fall back to conservative defaults if GPU memory info is unavailable
    free_mb = 0.0  # No GPU available
    suggested_batch = 0  # No processing possible without GPU
    logging.warning(f"GPU not available: batch processing not supported without GPU")
    return suggested_batch, free_mb

def get_suggested_batch_size_torch_fallback() -> Tuple[int, float]:
    """
    Alternative batch size calculation using torch for compatibility.

    This provides a fallback method that matches the reference implementation's
    torch-based memory estimation for cases where CuPy memory info fails.

    Returns:
        Tuple[int, float]: (suggested_batch_size, free_memory_mb)
    """
    try:
        import torch
        if torch.cuda.is_available():
            # Get total and allocated memory (matches reference implementation)
            total_memory = torch.cuda.get_device_properties(0).total_memory
            allocated_memory = torch.cuda.memory_allocated(0)
            free_memory = total_memory - allocated_memory

            # Convert to MB for easier reading
            free_memory_mb = free_memory / (1024 * 1024)

            # Estimate batch size based on free memory
            # Assume each trace needs about 10MB of GPU memory (very rough estimate)
            # and leave 20% of free memory as buffer
            suggested_batch = int((free_memory_mb * 0.8) / 10)

            # Clamp to reasonable range (matches reference implementation)
            suggested_batch = max(10, min(suggested_batch, 1000))

            logging.info(f"Torch GPU memory: suggesting batch size {suggested_batch} (free GPU RAM: {free_memory_mb:.1f} MB)")
            return suggested_batch, free_memory_mb
        else:
            # No GPU available
            return 0, 0.0
    except Exception as e:
        # If anything goes wrong, return a conservative default
        logging.warning(f"Error estimating GPU memory with torch: {e}. Using default batch size.")
        return 20, 4096.0  # Conservative fallback

def reset_gpu_cache():
    """
    Reset the GPU availability cache.
    
    This can be useful for testing or if GPU state changes during runtime.
    """
    global _gpu_available, _cupy_module, _gpu_info_cache
    _gpu_available = None
    _cupy_module = None
    _gpu_info_cache = None
    logging.info("GPU cache reset.")

# Initialize GPU availability check on module import
# This ensures the check happens once when the module is first imported
_gpu_available, _cupy_module = _check_gpu_availability()
