"""
Unit Tests for AOI Validation Module.

This module provides comprehensive unit tests for the AOI validation functionality
implemented in utils/aoi_validation.py as part of Phase 2.
"""

import pytest
import pandas as pd
import numpy as np
import streamlit as st
from unittest.mock import Mock, patch, MagicMock

# Import the modules to test
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.aoi_validation import (
    validate_aoi_selection,
    process_aoi_selection,
    validate_aoi_boundaries,
    get_aoi_trace_statistics,
    initialize_aoi_session_state,
    reset_aoi_selection,
    get_aoi_boundary_suggestions
)

class TestAOIValidation:
    """Test suite for AOI validation functions."""
    
    def setup_method(self):
        """Setup test environment before each test."""
        # Clear session state
        if hasattr(st, 'session_state'):
            st.session_state.clear()
        
        # Create mock headers DataFrame
        self.mock_headers_df = pd.DataFrame({
            'trace_idx': range(100),
            'inline': np.repeat(range(1, 11), 10),
            'crossline': np.tile(range(1, 11), 10),
            'x_coord': np.random.rand(100) * 1000,
            'y_coord': np.random.rand(100) * 1000
        })
        
        # Setup session state with mock data
        st.session_state.headers_df = self.mock_headers_df
        st.session_state.aoi_inline_min = 2
        st.session_state.aoi_inline_max = 5
        st.session_state.aoi_xline_min = 3
        st.session_state.aoi_xline_max = 7
    
    def test_validate_aoi_selection_valid_boundaries(self):
        """Test AOI validation with valid boundaries."""
        result = validate_aoi_selection()
        
        assert result['valid'] == True
        assert result['trace_count'] > 0
        assert 'aoi_df' in result
        assert isinstance(result['aoi_df'], pd.DataFrame)
    
    def test_validate_aoi_selection_invalid_inline_range(self):
        """Test AOI validation with invalid inline range (min > max)."""
        st.session_state.aoi_inline_min = 5
        st.session_state.aoi_inline_max = 2
        
        result = validate_aoi_selection()
        
        assert result['valid'] == False
        assert 'Inline minimum' in result['error']
        assert 'cannot be greater than maximum' in result['error']
    
    def test_validate_aoi_selection_invalid_crossline_range(self):
        """Test AOI validation with invalid crossline range (min > max)."""
        st.session_state.aoi_xline_min = 7
        st.session_state.aoi_xline_max = 3
        
        result = validate_aoi_selection()
        
        assert result['valid'] == False
        assert 'Crossline minimum' in result['error']
        assert 'cannot be greater than maximum' in result['error']
    
    def test_validate_aoi_selection_missing_headers(self):
        """Test AOI validation when headers_df is missing."""
        st.session_state.headers_df = None
        
        result = validate_aoi_selection()
        
        assert result['valid'] == False
        assert 'Seismic headers not loaded' in result['error']
    
    def test_validate_aoi_selection_missing_boundaries(self):
        """Test AOI validation when boundary variables are missing."""
        del st.session_state.aoi_inline_min
        
        result = validate_aoi_selection()
        
        assert result['valid'] == False
        assert 'AOI boundaries not set' in result['error']
        assert 'aoi_inline_min' in result['error']
    
    def test_validate_aoi_selection_out_of_range_boundaries(self):
        """Test AOI validation with boundaries outside data range."""
        st.session_state.aoi_inline_min = 50  # Outside data range
        st.session_state.aoi_inline_max = 60
        
        result = validate_aoi_selection()
        
        assert result['valid'] == False
        assert 'outside data range' in result['error']
    
    def test_validate_aoi_selection_no_traces_found(self):
        """Test AOI validation when no traces are found in range."""
        st.session_state.aoi_inline_min = 15  # Outside actual data
        st.session_state.aoi_inline_max = 20
        
        result = validate_aoi_selection()
        
        assert result['valid'] == False
        assert 'No traces found within specified AOI boundaries' in result['error']
    
    def test_process_aoi_selection_success(self):
        """Test successful AOI selection processing."""
        with patch('utils.aoi_validation.validate_aoi_selection') as mock_validate:
            mock_validate.return_value = {
                'valid': True,
                'trace_count': 20,
                'aoi_df': self.mock_headers_df.iloc[:20]
            }
            
            result = process_aoi_selection()
            
            assert result == True
            assert st.session_state.area_selected == True
            assert st.session_state.area_selected_mode == 'aoi'
            assert len(st.session_state.selected_indices) == 20
    
    def test_process_aoi_selection_validation_failure(self):
        """Test AOI selection processing when validation fails."""
        with patch('utils.aoi_validation.validate_aoi_selection') as mock_validate:
            mock_validate.return_value = {
                'valid': False,
                'error': 'Test validation error'
            }
            
            with patch('streamlit.error') as mock_error:
                result = process_aoi_selection()
                
                assert result == False
                mock_error.assert_called_once()
    
    def test_validate_aoi_boundaries_valid(self):
        """Test AOI boundary validation with valid inputs."""
        result = validate_aoi_boundaries(2, 5, 3, 7)
        
        assert result['valid'] == True
        assert result['trace_count'] > 0
        assert 'coverage' in result
    
    def test_validate_aoi_boundaries_invalid_logic(self):
        """Test AOI boundary validation with invalid logic."""
        result = validate_aoi_boundaries(5, 2, 3, 7)  # min > max
        
        assert result['valid'] == False
        assert 'minimum cannot be greater than maximum' in result['error']
    
    def test_get_aoi_trace_statistics_success(self):
        """Test AOI trace statistics calculation."""
        # Setup AOI selection
        st.session_state.area_selected = True
        st.session_state.area_selected_mode = 'aoi'
        
        with patch('utils.aoi_validation.validate_aoi_selection') as mock_validate:
            mock_validate.return_value = {
                'valid': True,
                'aoi_df': self.mock_headers_df.iloc[:20]
            }
            
            stats = get_aoi_trace_statistics()
            
            assert 'total_traces' in stats
            assert 'inline_range' in stats
            assert 'crossline_range' in stats
            assert 'grid_coverage' in stats
    
    def test_get_aoi_trace_statistics_no_selection(self):
        """Test AOI trace statistics when no AOI is selected."""
        st.session_state.area_selected = False
        
        stats = get_aoi_trace_statistics()
        
        assert 'error' in stats
        assert 'No AOI selection active' in stats['error']
    
    def test_initialize_aoi_session_state(self):
        """Test AOI session state initialization."""
        # Clear existing AOI variables
        aoi_vars = ['aoi_inline_min', 'aoi_inline_max', 'aoi_xline_min', 'aoi_xline_max']
        for var in aoi_vars:
            if hasattr(st.session_state, var):
                delattr(st.session_state, var)
        
        initialize_aoi_session_state()
        
        # Check that all AOI variables are initialized
        for var in aoi_vars:
            assert hasattr(st.session_state, var)
        
        assert hasattr(st.session_state, 'aoi_processing_option')
        assert st.session_state.aoi_processing_option == "Full AOI"
    
    def test_reset_aoi_selection(self):
        """Test AOI selection reset functionality."""
        # Setup some AOI state
        st.session_state.area_selected = True
        st.session_state.area_selected_mode = 'aoi'
        st.session_state.selected_indices = [1, 2, 3]
        st.session_state.aoi_bounds = {'test': 'data'}
        
        reset_aoi_selection()
        
        assert st.session_state.area_selected == False
        assert st.session_state.area_selected_mode is None
        assert st.session_state.selected_indices == []
        assert st.session_state.aoi_bounds is None
    
    def test_get_aoi_boundary_suggestions(self):
        """Test AOI boundary suggestions generation."""
        suggestions = get_aoi_boundary_suggestions(self.mock_headers_df)
        
        assert 'full_survey' in suggestions
        assert 'center_half' in suggestions
        assert 'center_quarter' in suggestions
        
        # Check that suggestions have required keys
        for preset in suggestions.values():
            assert 'inline_min' in preset
            assert 'inline_max' in preset
            assert 'xline_min' in preset
            assert 'xline_max' in preset
            assert 'description' in preset
    
    def test_validate_aoi_selection_with_warnings(self):
        """Test AOI validation that includes warnings."""
        # This test would check for scenarios that generate warnings
        # but still pass validation
        result = validate_aoi_selection()
        
        assert 'warnings' in result
        assert isinstance(result['warnings'], list)

# Integration test class
class TestAOIIntegration:
    """Integration tests for AOI functionality."""
    
    def test_complete_aoi_workflow(self):
        """Test complete AOI workflow from initialization to processing."""
        # Initialize session state
        initialize_aoi_session_state()
        
        # Setup mock data
        mock_headers_df = pd.DataFrame({
            'trace_idx': range(100),
            'inline': np.repeat(range(1, 11), 10),
            'crossline': np.tile(range(1, 11), 10)
        })
        st.session_state.headers_df = mock_headers_df
        
        # Set AOI boundaries
        st.session_state.aoi_inline_min = 2
        st.session_state.aoi_inline_max = 5
        st.session_state.aoi_xline_min = 3
        st.session_state.aoi_xline_max = 7
        
        # Validate AOI
        validation_result = validate_aoi_selection()
        assert validation_result['valid'] == True
        
        # Process AOI selection
        with patch('streamlit.success'):
            process_result = process_aoi_selection()
            assert process_result == True
        
        # Verify final state
        assert st.session_state.area_selected == True
        assert st.session_state.area_selected_mode == 'aoi'
        assert len(st.session_state.selected_indices) > 0

if __name__ == "__main__":
    # Run tests if executed directly
    pytest.main([__file__])
