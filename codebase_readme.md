# WOSS Seismic Analysis Tool - Codebase Structure

## Overview

The WOSS (Weighted Optimum Spectral Shape) Seismic Analysis Tool is a Streamlit-based application designed for GPU-accelerated seismic data analysis. The application provides a comprehensive workflow for loading, processing, analyzing, and exporting seismic data with advanced spectral analysis capabilities.

## Main Pipeline Architecture

The application follows a **5-step workflow** managed by the main router (`app.py`):

1. **Load Data** - Import SEG-Y files and well marker data
2. **Configure Display** - Set up visualization parameters and processing options
3. **Select Area** - Choose analysis area (inline/crossline, polyline, AOI, or well markers)
4. **Analyze Data** - Perform GPU-accelerated spectral analysis
5. **Export Results** - View results and export processed data

## Directory Structure (Main Pipeline)

```
1f_Modular_deep/
├── app.py                          # Main application router and entry point
├── .streamlit/
│   └── config.toml                 # Streamlit configuration
├── common/                         # Shared modules and utilities
│   ├── __init__.py
│   ├── constants.py                # Application constants and configurations
│   ├── session_state.py            # Session state management
│   ├── ui_elements.py              # Reusable UI components
│   └── validation_ui.py            # Data validation UI components
├── pages/                          # Workflow page modules
│   ├── __init__.py
│   ├── load_data_page.py           # Step 1: Data loading interface
│   ├── configure_display_page.py   # Step 2: Display configuration
│   ├── select_area_page.py         # Step 3: Area selection
│   ├── analyze_data_page.py        # Step 4: Analysis execution
│   └── export_results_page.py      # Step 5: Results and export
├── utils/                          # Processing and utility modules
│   ├── __init__.py
│   ├── data_utils.py               # Data loading and manipulation
│   ├── data_validation.py          # Data validation functions
│   ├── dlogst_spec_descriptor_cpu.py # CPU-based spectral analysis
│   ├── dlogst_spec_descriptor_gpu.py # GPU-accelerated spectral analysis
│   ├── export_utils.py             # Data export functionality
│   ├── general_utils.py            # General utility functions
│   ├── gpu_diagnostic.py           # GPU diagnostics and testing
│   ├── gpu_manager.py              # GPU availability management
│   ├── gpu_utils.py                # GPU utility functions
│   ├── precomputation_utils.py     # Pre-computation operations
│   ├── processing.py               # Core processing algorithms
│   ├── qc_utils.py                 # Quality control utilities
│   ├── session_state_handler.py    # Advanced session state handling
│   └── visualization.py            # Data visualization functions
└── tests/                          # Test modules
    └── test_polyline_basic.py      # Basic polyline functionality tests
```

## Key Components

### Core Application (`app.py`)

The main application router that:
- Manages the 5-step workflow navigation
- Handles GPU availability detection and lazy loading
- Provides sidebar navigation and state management
- Routes requests to appropriate page modules
- Implements prerequisite checks for each workflow step

### Common Modules (`common/`)

**constants.py**
- Application title and configuration
- Available output types for different analysis modes
- Descriptor limits and colormap configurations
- Attribute name mappings for export functionality

**session_state.py**
- Centralized session state initialization and management
- State reset functionality for new analyses

**ui_elements.py**
- Reusable UI components and widgets
- Consistent styling and behavior across pages

**validation_ui.py**
- Data validation UI components
- Input validation and error handling interfaces

### Page Modules (`pages/`)

Each page module implements a specific workflow step:

1. **load_data_page.py** - SEG-Y file loading, well data import
2. **configure_display_page.py** - Visualization parameters, processing options
3. **select_area_page.py** - Area selection modes (inline/crossline, polyline, AOI, well markers)
4. **analyze_data_page.py** - GPU-accelerated analysis execution
5. **export_results_page.py** - Results visualization and data export

### Utility Modules (`utils/`)

**Data Processing**
- `data_utils.py` - Data loading, manipulation, and format conversion
- `processing.py` - Core spectral analysis algorithms
- `dlogst_spec_descriptor_gpu.py` - GPU-accelerated spectral descriptor computation
- `dlogst_spec_descriptor_cpu.py` - CPU fallback for spectral analysis

**GPU Management**
- `gpu_manager.py` - GPU availability detection and management
- `gpu_utils.py` - GPU-specific utility functions
- `gpu_diagnostic.py` - GPU diagnostics and performance testing

**Analysis Support**
- `precomputation_utils.py` - Pre-computation operations for optimization
- `qc_utils.py` - Quality control and validation functions
- `visualization.py` - Advanced plotting and visualization functions

**Export and I/O**
- `export_utils.py` - Data export in various formats
- `general_utils.py` - General-purpose utility functions

## Configuration

### Streamlit Configuration (`.streamlit/config.toml`)
```toml
[client]
showSidebarNavigation = false    # Custom navigation via app.py

[server]
maxUploadSize = 8000            # 8GB upload limit for large SEG-Y files
```

## Dependencies and Requirements

### Core Dependencies
- **Streamlit** - Web application framework
- **CuPy** - GPU-accelerated computing (CUDA required)
- **NumPy/SciPy** - Numerical computing
- **Pandas** - Data manipulation
- **Matplotlib/Plotly** - Visualization

### GPU Requirements
- CUDA-compatible GPU
- CUDA drivers installed
- CuPy library (`pip install cupy-cuda11x` or `cupy-cuda12x`)

## Workflow Description

### Step 1: Load Data
- Import SEG-Y seismic files
- Load well marker data (optional)
- Validate data integrity and format

### Step 2: Configure Display
- Set visualization parameters
- Configure processing options
- Define analysis parameters

### Step 3: Select Area
- Choose analysis mode:
  - **Inline/Crossline**: Specific trace ranges
  - **Polyline**: Custom path analysis
  - **AOI (Area of Interest)**: Polygon-based selection
  - **Well Markers**: Analysis around well locations

### Step 4: Analyze Data
- GPU-accelerated spectral analysis
- Compute multiple spectral descriptors:
  - Spectral Slope, Bandwidth, Rolloff
  - High Frequency Content (HFC)
  - Magnitude*Voice Slope
  - Spectral Decrease
  - **WOSS (Weighted Optimum Spectral Shape)**

### Step 5: Export Results
- Visualize analysis results
- Export processed data in various formats
- Generate reports and documentation

## Excluded Components

The following directories and files are excluded from the main pipeline (as per `.gitignore`):

- `archive/` - Legacy and test files
- `backup_tk/` - Backup implementations
- `initial_app/` - Reference implementation
- `docs/` - Documentation and planning files
- Test files and Python cache files
- IDE and OS-specific files

## Key Features

- **GPU Acceleration**: Leverages CUDA for high-performance spectral analysis
- **Modular Architecture**: Clean separation of concerns with dedicated modules
- **State Management**: Robust session state handling for complex workflows
- **Multiple Analysis Modes**: Flexible area selection and analysis options
- **Comprehensive Export**: Multiple output formats and visualization options
- **Error Handling**: Graceful degradation and informative error messages

## Getting Started

1. Ensure GPU requirements are met (CUDA-compatible GPU, drivers, CuPy)
2. Install required Python dependencies
3. Run the application: `streamlit run app.py`
4. Follow the 5-step workflow for seismic analysis

## Architecture Benefits

- **Scalability**: Modular design allows easy addition of new features
- **Maintainability**: Clear separation of UI, processing, and utility functions
- **Performance**: GPU acceleration for computationally intensive operations
- **User Experience**: Intuitive workflow with comprehensive validation and feedback
- **Flexibility**: Multiple analysis modes to suit different geological scenarios