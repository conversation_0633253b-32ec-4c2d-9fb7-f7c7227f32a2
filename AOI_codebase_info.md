# AOI Area Form Implementation - Complete Codebase Reference

## Overview
This document provides a comprehensive reference for the AOI (Area of Interest) area form implementation in the WOSS Seismic Analysis Tool. The AOI functionality allows users to define specific inline/crossline boundaries for targeted seismic data analysis and export.

## 1. Session State Variables for AOI

### Core AOI Variables
The following session state variables are initialized for AOI functionality:

```python
# AOI boundary variables
st.session_state.aoi_inline_min = None  # Minimum inline value
st.session_state.aoi_inline_max = None  # Maximum inline value
st.session_state.aoi_xline_min = None  # Minimum crossline value
st.session_state.aoi_xline_max = None  # Maximum crossline value

# AOI processing options
st.session_state.aoi_processing_option = "Full AOI"  # Default processing mode
st.session_state.aoi_plot_section_type = None  # Plot section type
st.session_state.aoi_plot_fixed_value = None  # Fixed value for plotting
st.session_state.aoi_bounds = {}  # Dictionary storing AOI boundaries
```

## 2. AOI Form Interface Implementation

### Location: app_ref.py (Lines 1819-1910)

#### AOI Mode Selection
```python
elif st.session_state.selection_mode == "By inline/crossline section (AOI)":
    st.subheader("AOI Mode")
```

#### Boundary Input Fields
The AOI form provides four number input fields for boundary specification:

**Inline Range:**
- `aoi_inline_min`: Number input for minimum inline value
- `aoi_inline_max`: Number input for maximum inline value

**Crossline Range:**
- `aoi_xline_min`: Number input for minimum crossline value
- `aoi_xline_max`: Number input for maximum crossline value

#### Initialization Logic
```python
# Initialize session state values if not already set
if st.session_state.aoi_inline_min is None:
    st.session_state.aoi_inline_min = actual_inline_min
if st.session_state.aoi_inline_max is None:
    st.session_state.aoi_inline_max = actual_inline_max
if st.session_state.aoi_xline_min is None:
    st.session_state.aoi_xline_min = actual_xline_min
if st.session_state.aoi_xline_max is None:
    st.session_state.aoi_xline_max = actual_xline_max
```

#### Processing Option
- **Fixed Option**: "Full AOI" (only option currently supported)
- Displayed as: `st.info("Processing Option: Full AOI")`

## 3. AOI Data Processing Workflow

### Location: app_ref.py (Lines 2288-2334)

#### Trace Selection Process
1. **AOI Boundary Application**: Filters traces based on inline/crossline ranges
2. **DataFrame Filtering**: Uses pandas to filter headers DataFrame
3. **Trace Index Collection**: Collects trace indices within AOI boundaries

```python
# Find traces within the AOI
inline_min = st.session_state.aoi_inline_min
inline_max = st.session_state.aoi_inline_max
xline_min = st.session_state.aoi_xline_min
xline_max = st.session_state.aoi_xline_max

# Filter by inline/crossline range
aoi_df = headers_df[
    (headers_df['inline'] >= inline_min) &
    (headers_df['inline'] <= inline_max) &
    (headers_df['crossline'] >= xline_min) &
    (headers_df['crossline'] <= xline_max)
]
```

#### Error Handling
- Validates if traces exist within specified AOI
- Provides user feedback for empty results
- Includes back navigation for parameter adjustment

## 4. AOI Export Configuration

### Location: app_ref.py (Lines 3156-3256)

#### Export Setup
- **Header**: "Step 3.6: Configure AOI Export"
- **AOI Information Display**: Shows inline/crossline ranges and trace count
- **Boundary Display**: Shows calculated AOI size in inlines × crosslines

#### Filename Generation
AOI parameters are incorporated into export filenames:
```python
# Format AOI parameters for filename
aoi_params = f"_IL{st.session_state.aoi_inline_min}-{st.session_state.aoi_inline_max}_XL{st.session_state.aoi_xline_min}-{st.session_state.aoi_xline_max}"
```

## 5. AOI Export Processing

### Location: app_ref.py (Lines 4142-4613)

#### Batch Processing
- **Header**: "Step 4.5: Processing AOI Export"
- **AOI Filtering**: Applies AOI boundaries during batch processing
- **Progress Tracking**: Shows progress with AOI-specific information

#### AOI Mask Creation
```python
# Create mask for the AOI boundaries
aoi_mask = (
    (headers_df['inline'] >= st.session_state.aoi_inline_min) &
    (headers_df['inline'] <= st.session_state.aoi_inline_max) &
    (headers_df['crossline'] >= st.session_state.aoi_xline_min) &
    (headers_df['crossline'] <= st.session_state.aoi_xline_max) &
    headers_df['trace_idx'].isin(selected_indices)
)
```

#### Export Summary
Provides comprehensive summary including:
- AOI boundaries (inline and crossline ranges)
- Total traces processed within AOI
- File naming with AOI parameters

## 6. Data Flow Architecture

### Input Flow
1. **User Input**: AOI boundaries via Streamlit number inputs
2. **Validation**: Range checking against actual seismic data bounds
3. **Storage**: Session state variables for persistence

### Processing Flow
1. **Filtering**: Apply AOI boundaries to trace headers
2. **Selection**: Collect trace indices within AOI
3. **Processing**: Apply spectral descriptor calculations to AOI traces

### Output Flow
1. **Visualization**: Generate plots for AOI-specific data
2. **Export**: Create SEG-Y files with AOI boundaries
3. **Packaging**: Create ZIP files with AOI parameters in filename

## 7. Error Handling and Validation

### Boundary Validation
- Checks if specified ranges are within actual data bounds
- Provides informative error messages for invalid ranges
- Offers navigation back to parameter adjustment

### Empty Result Handling
```python
if aoi_df.empty:
    st.error("No traces found within the specified AOI.")
    if st.button("Back to Mode Selection"):
        st.session_state.current_step = "mode_selection"
```

## 8. User Experience Features

### Real-time Feedback
- Progress spinners during processing
- Success/error messages for operations
- Detailed AOI information display

### Navigation
- Clear back buttons for parameter adjustment
- Step-by-step workflow guidance
- Contextual help information

## 9. Technical Implementation Details

### Data Structures
- **Headers DataFrame**: Contains inline, crossline, and trace index information
- **AOI Bounds Dictionary**: Stores boundary values for reference
- **Selected Indices List**: Maintains trace indices within AOI

### Performance Considerations
- Efficient pandas filtering for large datasets
- Batch processing for memory management
- Progress tracking for long operations

### Integration Points
- Seamless integration with existing seismic data classes
- Compatible with both 2D and 3D seismic data
- Works with existing export utilities

## 10. Usage Examples

### Basic AOI Selection
```python
# Set AOI boundaries
st.session_state.aoi_inline_min = 100
st.session_state.aoi_inline_max = 200
st.session_state.aoi_xline_min = 50
st.session_state.aoi_xline_max = 150
```

### Export with AOI Parameters
```python
# Generate filename with AOI info
filename = f"seismic_data_IL100-200_XL50-150.sgy"
```

## 11. Detailed Batch Processing Workflow

### 11.1  AOI Trace Sorting
1. **Header extraction** – `SegyHeaderLoader` reads inline / crossline / X / Y and removes duplicates, keeping `unique_indices`.
2. **Pandas masking** – In `app_ref.py` the header DataFrame is filtered with the four AOI bounds producing `aoi_df`.
3. **Ordered indices** – `selected_indices = aoi_df['trace_idx'].tolist()` are already sorted by `(inline, crossline)`.

### 11.2  Batch-wise Descriptor Calculation
| Phase | Implementation | Key APIs |
|-------|----------------|----------|
| Batch size decision | `get_suggested_batch_size()` (GPU RAM aware) | `utils.py` |
| Load traces | `load_trace_sample()` inside a list-comprehension per batch | `data_utils.py` |
| Vectorised processing | `dlogst_spec_descriptor()` (GPU → CPU fallback) | `dlogst_spec_descriptor_gpu.py` / `dlogst_spec_descriptor_cpu.py` |
| Derived attribute | `calculate_woss()` for WOSS after descriptors | `processing.py` |

### 11.3  Statistics & Normalisation
`calculate_stats_and_defaults()` aggregates batched results, computes P5/P95, stores global limits inside `plot_settings` for downstream visualisation and export.

### 11.4  SEG-Y Export Pipeline
1. **Per-batch write** – `utils.save_to_segy_3d()` (or `save_to_segy_2d()` in 2-D mode) clones headers, writes batch cube to `AOI_part_##.sgy`.
2. **Merge parts** – `data_utils.merge_segy_batch_files()` concatenates all parts into a single coherent file, preserving sample interval and header consistency.
3. **Filename** – Final output embeds AOI bounds, e.g. `*_IL100-200_XL50-150.sgy`.

### 11.5  Ordering Guarantees
The merged file keeps ascending inline / crossline order inherited from `selected_indices`; no resorting occurs during batching or merging.

## Summary
The AOI implementation provides a complete workflow for defining, processing, and exporting specific regions of seismic data. It includes user-friendly interfaces, robust error handling, and seamless integration with the existing WOSS tool architecture. The implementation supports both interactive exploration and batch processing workflows while maintaining data integrity and providing clear user feedback throughout the process.