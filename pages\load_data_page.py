
"""
Load Data Page for the WOSS Seismic Analysis Tool.

This module handles the UI rendering for loading and selecting data.
It follows the principles outlined in rules.md, particularly regarding
the separation of concerns between UI and backend logic.
"""

import streamlit as st
import os
import tempfile
import logging

# Import common modules
from common.constants import APP_TITLE
from common.session_state import initialize_session_state, reset_state
from common.ui_elements import slider_with_number_input, get_suggested_batch_size

# Import utility functions
from utils.data_utils import SegyHeaderLoader, load_excel_data # Corrected imports
from utils.data_utils import get_sampling_interval, get_trace_count
from utils.data_utils import load_numpy_data

def render():
    """Render the load data page UI."""
    # Initialize session state if needed
    initialize_session_state()
    
    st.header("Step 1: Load Data")
    st.sidebar.header("Data Loading")

    # Data Format Selection
    st.sidebar.subheader("Select Data Format")
    data_format = st.sidebar.radio(
        "Data Format:",
        ('SEG-Y Upload', 'NumPy Upload'),
        horizontal=True,
        key="data_format_selector"
    )
    st.session_state.data_format_type = "SEG-Y" if data_format == 'SEG-Y Upload' else "NumPy"

    if data_format == 'SEG-Y Upload':
        # SEG-Y File Upload
        st.sidebar.subheader("Upload SEG-Y File")
        segy_file = st.sidebar.file_uploader("Choose a SEG-Y file", type=["sgy", "segy"], key="segy_file_uploader")
        if segy_file is not None:
            st.session_state.segy_file_info = {
                'name': segy_file.name,
                'buffer': segy_file
            }
            st.sidebar.success(f"Uploaded {segy_file.name}")
    
    elif data_format == 'NumPy Upload':
        # NumPy File Upload
        st.sidebar.subheader("Upload NumPy File")
        numpy_file = st.sidebar.file_uploader("Choose a NumPy file", type=["npy"], key="numpy_file_uploader")
        
        if numpy_file is not None:
            st.session_state.numpy_file_info = {
                'name': numpy_file.name,
                'buffer': numpy_file
            }
            st.sidebar.success(f"Uploaded {numpy_file.name}")
        
        # NumPy Data Configuration
        st.sidebar.subheader("NumPy Data Configuration")
        dimension_type = st.sidebar.radio(
            "Data Dimension:",
            ('2D', '3D'),
            horizontal=True,
            key="numpy_dimension_selector",
            help="2D: (traces, samples) | 3D: (inlines, crosslines, samples)"
        )
        st.session_state.numpy_data_type = dimension_type
        
        # Optional sampling rate input
        custom_sample_rate = st.sidebar.number_input(
            "Sample Rate (ms)",
            min_value=0.1,
            max_value=100.0,
            value=1.0,
            step=0.1,
            key="numpy_sample_rate",
            help="Sampling interval in milliseconds (default: 1 ms)"
        )

    # Well Data Upload (Optional)
    st.sidebar.subheader("Upload Well Data (Optional)")
    # Add checkbox to toggle using well data
    st.session_state.use_wells = st.sidebar.checkbox("Use Well Data", value=st.session_state.use_wells)

    well_file = st.sidebar.file_uploader("Choose an Excel file", type=["xls", "xlsx"], key="well_file_uploader", disabled=not st.session_state.use_wells)
    if well_file is not None:
        st.session_state.well_file_info = {
            'name': well_file.name,
            'buffer': well_file
        }
        st.sidebar.success(f"Uploaded {well_file.name}")

    # SEG-Y specific configuration (only show for SEG-Y format)
    if data_format == 'SEG-Y Upload':
        # SEG-Y Header Bytes
        st.sidebar.subheader("SEG-Y Header Bytes")
        st.session_state.inline_byte = st.sidebar.number_input("Inline Byte", value=189, step=1)
        st.session_state.xline_byte = st.sidebar.number_input("Crossline Byte", value=193, step=1)
        st.session_state.x_byte = st.sidebar.number_input("X Coordinate Byte", value=73, step=1)
        st.session_state.y_byte = st.sidebar.number_input("Y Coordinate Byte", value=77, step=1)

        # Coordinate Scaler Options
        st.sidebar.subheader("Coordinate Scaler")
        st.session_state.scaler_mode = st.sidebar.radio(
            "Scaler Mode",
            options=["Use Scaler Byte", "Custom Scaler"],
            index=0 if st.session_state.scaler_mode == "Use Scaler Byte" else 1
        )

        if st.session_state.scaler_mode == "Use Scaler Byte":
            st.session_state.scaler_byte = st.sidebar.number_input("Scaler Byte", value=71, step=1)
        else:
            st.session_state.custom_scaler = st.sidebar.number_input(
                "Custom Scaler Value",
                value=st.session_state.custom_scaler,
                min_value=-1000.0,
                max_value=1000.0,
                step=0.1,
                help="Positive value: multiply coordinates by this value. Negative value: divide coordinates by the absolute value."
            )

    # Load Data Button
    if st.sidebar.button("Load Data", key="load_data_button"):
        # Handle SEG-Y data loading
        if st.session_state.data_format_type == "SEG-Y" and st.session_state.segy_file_info:
            tmp_file_path = None  # Initialize path variable
            try:
                # Create temporary file *before* calling cached function
                with tempfile.NamedTemporaryFile(delete=False, suffix=".sgy") as tmp_file:
                    tmp_file.write(st.session_state.segy_file_info['buffer'].getvalue())
                    tmp_file_path = tmp_file.name
                st.session_state.segy_temp_file_path = tmp_file_path  # Store path in session state
                logging.info(f"Created temporary SEG-Y file: {tmp_file_path}")

                # Instantiate SegyHeaderLoader and load headers
                loader_instance = SegyHeaderLoader(tmp_file_path)
                
                use_custom_scaler_arg = (st.session_state.scaler_mode == "Custom Scaler")
                if use_custom_scaler_arg:
                    actual_scaler_arg = st.session_state.custom_scaler
                else:
                    actual_scaler_arg = st.session_state.scaler_byte
                
                # load_headers modifies the instance in-place
                loader_instance.load_headers(
                    st.session_state.inline_byte,
                    st.session_state.xline_byte,
                    st.session_state.x_byte,
                    st.session_state.y_byte,
                    actual_scaler_arg,
                    use_custom_scaler=use_custom_scaler_arg
                )
                st.session_state.header_loader = loader_instance # Assign the instance

                if st.session_state.header_loader: # Check if instance is assigned
                    # Use the stored temporary path for interval calculation
                    st.session_state.dt = get_sampling_interval(st.session_state.segy_temp_file_path)

                    # Get the actual number of traces using our new function
                    trace_count = get_trace_count(st.session_state.segy_temp_file_path)
                    st.session_state.trace_count = trace_count

                    st.success(f"SEG-Y headers loaded. Sampling Interval: {st.session_state.dt*1000:.2f} ms, Traces: {trace_count}")
                    logging.info(f"SEG-Y headers loaded from {st.session_state.segy_file_info['name']} (temp: {tmp_file_path}). dt={st.session_state.dt}, trace_count={trace_count}")

                    # Load well data if provided
                    if st.session_state.use_wells and st.session_state.well_file_info:
                        # Use corrected function for well data, passing the buffer
                        well_df = load_excel_data(st.session_state.well_file_info['buffer'])
                        if well_df is not None:
                            st.session_state.well_df = well_df
                            st.success(f"Well data loaded from {st.session_state.well_file_info['name']}.")
                            logging.info(f"Well data loaded from {st.session_state.well_file_info['name']}.")
                        else:
                            st.warning("Well file was provided but failed to load. Proceeding without well data.")
                            st.session_state.well_df = None  # Ensure it's None if loading failed
                    else:
                        st.session_state.well_df = None  # Ensure it's None if not used/provided

                    st.session_state.current_step = "configure_display"
                    st.rerun()  # Move to the next step
                else:
                    # Error handled within cached function
                    pass

            except Exception as e:
                st.error(f"An unexpected error occurred during data loading: {e}")
                logging.error(f"Data loading failed: {e}", exc_info=True)
                # Clean up temp file if created but loading failed afterwards
                if tmp_file_path and os.path.exists(tmp_file_path):
                    try:
                        os.remove(tmp_file_path)
                        logging.info(f"Cleaned up temp file {tmp_file_path} after loading error.")
                        st.session_state.segy_temp_file_path = None
                    except OSError as clean_e:
                        logging.warning(f"Could not clean up temp file {tmp_file_path} after error: {clean_e}")
        
        # Handle NumPy data loading
        elif st.session_state.data_format_type == "NumPy" and st.session_state.numpy_file_info:
            try:
                sample_rate_seconds = custom_sample_rate / 1000.0  # Convert ms to seconds
                
                numpy_data, header_loader = load_numpy_data(
                    st.session_state.numpy_file_info['buffer'], 
                    st.session_state.numpy_data_type,
                    sample_rate_seconds
                )
                
                # Store in session state
                st.session_state.numpy_data_object = numpy_data
                st.session_state.header_loader = header_loader
                st.session_state.numpy_data_loaded = True
                
                # Set derived properties for compatibility
                st.session_state.dt = numpy_data.get_sample_rate()
                
                if st.session_state.numpy_data_type == "2D":
                    st.session_state.trace_count = numpy_data.get_n_xlines()
                    data_shape = f"{numpy_data.get_n_xlines()} traces × {numpy_data.get_n_zslices()} samples"
                else:  # 3D
                    # For 3D, trace count is inlines × crosslines
                    st.session_state.trace_count = numpy_data.get_n_ilines() * numpy_data.get_n_xlines()
                    data_shape = f"{numpy_data.get_n_ilines()} inlines × {numpy_data.get_n_xlines()} crosslines × {numpy_data.get_n_zslices()} samples"
                
                st.success(f"NumPy {st.session_state.numpy_data_type} data loaded successfully!")
                st.success(f"Shape: {data_shape}")
                st.success(f"Sampling Interval: {st.session_state.dt*1000:.2f} ms")
                logging.info(f"NumPy {st.session_state.numpy_data_type} data loaded from {st.session_state.numpy_file_info['name']}")
                
                # Load well data if provided (same as SEG-Y)
                if st.session_state.use_wells and st.session_state.well_file_info:
                    well_df = load_excel_data(st.session_state.well_file_info['buffer'])
                    if well_df is not None:
                        st.session_state.well_df = well_df
                        st.success(f"Well data loaded from {st.session_state.well_file_info['name']}.")
                        logging.info(f"Well data loaded from {st.session_state.well_file_info['name']}.")
                    else:
                        st.warning("Well file was provided but failed to load. Proceeding without well data.")
                        st.session_state.well_df = None
                else:
                    st.session_state.well_df = None
                
                st.session_state.current_step = "configure_display"
                st.rerun()  # Move to the next step
                
            except Exception as e:
                st.error(f"Error loading NumPy data: {e}")
                logging.error(f"NumPy data loading failed: {e}", exc_info=True)
        
        else:
            if st.session_state.data_format_type == "SEG-Y":
                st.warning("Please upload a SEG-Y file.")
            else:
                st.warning("Please upload a NumPy file.")

    # Display main content area
    st.write("## Welcome to the WOSS Seismic Analysis Tool")
    st.write("This tool allows you to analyze seismic data using spectral descriptors.")
    
    if st.session_state.data_format_type == "SEG-Y":
        st.write("To get started, please upload a SEG-Y file using the sidebar.")
    else:
        st.write("To get started, please upload a NumPy file (.npy) using the sidebar.")
    
    # Add instructions
    with st.expander("Instructions", expanded=False):
        if st.session_state.data_format_type == "SEG-Y":
            st.write("""
            ### SEG-Y Workflow:
            1. **Upload a SEG-Y file** using the file uploader in the sidebar.
            2. **Optionally upload well data** in Excel format if you want to analyze specific well markers.
            3. **Configure the SEG-Y header bytes** if your file uses non-standard byte positions.
            4. **Choose a coordinate scaler mode** - either use the scaler byte from the SEG-Y file or specify a custom value.
            5. **Click 'Load Data'** to process the files and proceed to the next step.
            """)
            
            st.write("""
            ### SEG-Y Header Bytes:
            - **Inline Byte**: Byte position for inline numbers (default: 189)
            - **Crossline Byte**: Byte position for crossline numbers (default: 193)
            - **X Coordinate Byte**: Byte position for X coordinates (default: 73)
            - **Y Coordinate Byte**: Byte position for Y coordinates (default: 77)
            - **Scaler Byte**: Byte position for coordinate scaler (default: 71)
            """)
        else:
            st.write("""
            ### NumPy Workflow:
            1. **Upload a NumPy file** (.npy format) using the file uploader in the sidebar.
            2. **Select data dimension** - choose '2D' for 2D seismic lines or '3D' for 3D seismic volumes.
            3. **Set sampling rate** - specify the time sampling interval in milliseconds.
            4. **Optionally upload well data** in Excel format if you want to analyze specific well markers.
            5. **Click 'Load Data'** to process the files and proceed to the next step.
            """)
            
            st.write("""
            ### NumPy Data Format Requirements:
            - **2D Data**: Shape should be (n_traces, n_samples)
            - **3D Data**: Shape should be (n_inlines, n_crosslines, n_samples)
            - **Data Type**: Preferably float32 or float64 for best compatibility
            - **File Format**: Standard NumPy .npy format (created with np.save())
            - **Coordinate System**: Synthetic coordinates will be generated (25m spacing default)
            """)
            
            st.write("""
            ### Performance Benefits of NumPy Format:
            - **Faster Loading**: NumPy arrays load significantly faster than SEG-Y parsing
            - **GPU Compatibility**: Native NumPy arrays work seamlessly with GPU acceleration
            - **Memory Efficiency**: Direct memory access without header processing overhead
            - **Preprocessing Support**: Ideal for loading preprocessed or filtered seismic data
            """)

    # Add a "Start New Analysis" button to the sidebar
    st.sidebar.markdown("---")
    if st.sidebar.button("🔄 Start New Analysis", use_container_width=True, key="load_data_page_start_new_analysis_button"): # Added unique key
        reset_state()
        st.success("Starting new analysis. All temporary data has been cleared.")
        st.rerun()
