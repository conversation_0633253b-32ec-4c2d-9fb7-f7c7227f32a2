"""
Integration test for AOI functionality without streamlit dependencies.

This test focuses on the core logic and validation functions that don't
require streamlit session state.
"""

import unittest
import sys
import os
import pandas as pd
import numpy as np

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules to test
from utils.aoi_validation import (
    validate_aoi_boundaries,
    validate_aoi_size_performance,
    get_aoi_boundary_suggestions
)

class TestAOIIntegration(unittest.TestCase):
    """Integration test for AOI functionality."""

    def setUp(self):
        """Set up test environment."""
        # Create test data
        self.headers_df = pd.DataFrame({
            'inline': np.concatenate([np.arange(1000, 1501) for _ in range(501)]),
            'crossline': np.repeat(np.arange(2000, 2501), 501),
            'trace_idx': np.arange(501 * 501)
        })
    
    def test_validate_aoi_boundaries_valid(self):
        """Test AOI boundary validation with valid boundaries."""
        result = validate_aoi_boundaries(1000, 1500, 2000, 2500)
        
        # Should be valid since we don't have headers_df in session state
        # This tests the boundary logic validation
        self.assertFalse(result['valid'])  # Expected to fail without headers_df
        self.assertIn('error', result)
    
    def test_validate_aoi_boundaries_invalid_logic(self):
        """Test AOI boundary validation with invalid logic."""
        # Test min > max for inline
        result = validate_aoi_boundaries(1500, 1000, 2000, 2500)
        self.assertFalse(result['valid'])
        self.assertIn('Inline minimum cannot be greater than maximum', result['error'])
        
        # Test min > max for crossline
        result = validate_aoi_boundaries(1000, 1500, 2500, 2000)
        self.assertFalse(result['valid'])
        self.assertIn('Crossline minimum cannot be greater than maximum', result['error'])
    
    def test_validate_aoi_size_performance(self):
        """Test AOI performance validation."""
        # Test small AOI
        result = validate_aoi_size_performance(1000)
        self.assertTrue(result['valid'])
        self.assertEqual(len(result['warnings']), 0)
        self.assertEqual(len(result['recommendations']), 0)
        
        # Test large AOI
        result = validate_aoi_size_performance(60000)
        self.assertTrue(result['valid'])
        self.assertGreater(len(result['warnings']), 0)
        self.assertGreater(len(result['recommendations']), 0)
        self.assertIn('Large AOI', result['warnings'][0])
        
        # Test very large AOI
        result = validate_aoi_size_performance(150000)
        self.assertTrue(result['valid'])
        self.assertGreater(len(result['warnings']), 0)
        self.assertGreater(len(result['recommendations']), 0)
        self.assertIn('Very large AOI', result['warnings'][0])
    
    def test_get_aoi_boundary_suggestions(self):
        """Test AOI boundary suggestions generation."""
        suggestions = get_aoi_boundary_suggestions(self.headers_df)
        
        # Check that all expected suggestions are present
        self.assertIn('full_survey', suggestions)
        self.assertIn('center_half', suggestions)
        self.assertIn('center_quarter', suggestions)
        
        # Check full survey suggestion
        full_survey = suggestions['full_survey']
        self.assertEqual(full_survey['inline_min'], 1000)
        self.assertEqual(full_survey['inline_max'], 1500)
        self.assertEqual(full_survey['xline_min'], 2000)
        self.assertEqual(full_survey['xline_max'], 2500)
        
        # Check center half suggestion
        center_half = suggestions['center_half']
        self.assertGreater(center_half['inline_min'], 1000)
        self.assertLess(center_half['inline_max'], 1500)
        self.assertGreater(center_half['xline_min'], 2000)
        self.assertLess(center_half['xline_max'], 2500)
        
        # Check center quarter suggestion
        center_quarter = suggestions['center_quarter']
        self.assertGreater(center_quarter['inline_min'], center_half['inline_min'])
        self.assertLess(center_quarter['inline_max'], center_half['inline_max'])
        self.assertGreater(center_quarter['xline_min'], center_half['xline_min'])
        self.assertLess(center_quarter['xline_max'], center_half['xline_max'])
    
    def test_aoi_boundary_calculations(self):
        """Test AOI boundary calculation logic."""
        # Test with the headers_df
        inline_min = self.headers_df['inline'].min()
        inline_max = self.headers_df['inline'].max()
        xline_min = self.headers_df['crossline'].min()
        xline_max = self.headers_df['crossline'].max()
        
        # Verify data ranges
        self.assertEqual(inline_min, 1000)
        self.assertEqual(inline_max, 1500)
        self.assertEqual(xline_min, 2000)
        self.assertEqual(xline_max, 2500)
        
        # Test AOI filtering logic
        aoi_inline_min = 1100
        aoi_inline_max = 1400
        aoi_xline_min = 2100
        aoi_xline_max = 2400
        
        # Filter traces within AOI
        aoi_df = self.headers_df[
            (self.headers_df['inline'] >= aoi_inline_min) &
            (self.headers_df['inline'] <= aoi_inline_max) &
            (self.headers_df['crossline'] >= aoi_xline_min) &
            (self.headers_df['crossline'] <= aoi_xline_max)
        ]
        
        # Verify filtering worked
        self.assertGreater(len(aoi_df), 0)
        self.assertLessEqual(len(aoi_df), len(self.headers_df))
        
        # Verify all traces in AOI are within bounds
        self.assertTrue(all(aoi_df['inline'] >= aoi_inline_min))
        self.assertTrue(all(aoi_df['inline'] <= aoi_inline_max))
        self.assertTrue(all(aoi_df['crossline'] >= aoi_xline_min))
        self.assertTrue(all(aoi_df['crossline'] <= aoi_xline_max))
    
    def test_performance_thresholds(self):
        """Test performance threshold logic."""
        # Test exact threshold values
        result_49999 = validate_aoi_size_performance(49999)
        self.assertEqual(len(result_49999['warnings']), 0)
        
        result_50000 = validate_aoi_size_performance(50000)
        self.assertEqual(len(result_50000['warnings']), 0)
        
        result_50001 = validate_aoi_size_performance(50001)
        self.assertGreater(len(result_50001['warnings']), 0)
        
        result_99999 = validate_aoi_size_performance(99999)
        self.assertGreater(len(result_99999['warnings']), 0)
        self.assertIn('Large AOI', result_99999['warnings'][0])
        
        result_100001 = validate_aoi_size_performance(100001)
        self.assertGreater(len(result_100001['warnings']), 0)
        self.assertIn('Very large AOI', result_100001['warnings'][0])

if __name__ == '__main__':
    unittest.main()
