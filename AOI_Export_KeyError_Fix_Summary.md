# AOI Export KeyError: 'WOSS' Fix Summary

## Issue Description

A `KeyError: 'WOSS'` was occurring in the AOI export functionality at line 200 of `pages/analyze_data_page.py` in the `render_aoi_export()` function. The error happened when trying to map display attribute names to internal attribute names using `ATTR_NAME_MAP[attr_name]`.

## Root Cause Analysis

The issue was caused by an inconsistency between two lists:

### 1. `ATTR_NAME_MAP` Dictionary (Correct)
```python
ATTR_NAME_MAP = {
    "Original Seismic Amplitude": "data",
    "Magnitude of the Voice Component": "voice_mag",
    "High-Frequency Content (HFC)": "hfc",
    "Normalized Dominant Frequency": "norm_fdom",
    "Magnitude of Voice Slope": "mag_voice_slope",
    "Weighted Optimum Spectral Shape": "WOSS"  # ✅ Correct key
}
```

### 2. `available_for_export_display` List (Problematic)
```python
# BEFORE (Problematic):
available_for_export_display = [
    "Original Seismic Amplitude",
    "Magnitude of the Voice Component", 
    "High-Frequency Content (HFC)",
    "Normalized Dominant Frequency",
    "Magnitude of Voice Slope",
    "WOSS"  # ❌ Wrong - this key doesn't exist in ATTR_NAME_MAP
]
```

### 3. The Problematic Code
```python
# Line 200 in pages/analyze_data_page.py
selected_attrs_internal = [ATTR_NAME_MAP[attr_name] for attr_name in selected_attrs_display]
```

When users selected "WOSS" from the export options, the code tried to look up `ATTR_NAME_MAP["WOSS"]`, but the correct key was `"Weighted Optimum Spectral Shape"`.

## Solution Implemented

### Fixed `available_for_export_display` List
```python
# AFTER (Fixed):
available_for_export_display = [
    "Original Seismic Amplitude",
    "Magnitude of the Voice Component",
    "High-Frequency Content (HFC)", 
    "Normalized Dominant Frequency",
    "Magnitude of Voice Slope",
    "Weighted Optimum Spectral Shape"  # ✅ Fixed - matches ATTR_NAME_MAP key
]
```

## File Modified

**File:** `pages/analyze_data_page.py`
**Lines:** 177-185
**Change:** Updated `available_for_export_display` list to use "Weighted Optimum Spectral Shape" instead of "WOSS"

## Verification

### 1. Mapping Test Results
```
✅ 'Original Seismic Amplitude' -> 'data'
✅ 'Magnitude of the Voice Component' -> 'voice_mag'
✅ 'High-Frequency Content (HFC)' -> 'hfc'
✅ 'Normalized Dominant Frequency' -> 'norm_fdom'
✅ 'Magnitude of Voice Slope' -> 'mag_voice_slope'
✅ 'Weighted Optimum Spectral Shape' -> 'WOSS'
```

### 2. Reverse Mapping Test Results
```
✅ 'data' -> 'Original Seismic Amplitude'
✅ 'voice_mag' -> 'Magnitude of the Voice Component'
✅ 'hfc' -> 'High-Frequency Content (HFC)'
✅ 'norm_fdom' -> 'Normalized Dominant Frequency'
✅ 'mag_voice_slope' -> 'Magnitude of Voice Slope'
✅ 'WOSS' -> 'Weighted Optimum Spectral Shape'
```

### 3. Consistency Check
- ✅ Constants use 'WOSS' in `AVAILABLE_OUTPUTS` (correct for display)
- ✅ Constants use 'Weighted Optimum Spectral Shape' in `EXPORTABLE_ATTR_DISPLAY_NAMES` (correct for export)
- ✅ Fixed `available_for_export_display` aligns with `EXPORTABLE_ATTR_DISPLAY_NAMES`

## Impact

### Before Fix
- ❌ Users selecting WOSS for export would encounter `KeyError: 'WOSS'`
- ❌ AOI export functionality was broken when WOSS was selected
- ❌ Application would crash during export configuration

### After Fix
- ✅ Users can successfully select "Weighted Optimum Spectral Shape" for export
- ✅ No KeyError occurs during attribute mapping
- ✅ AOI export functionality works correctly for all attributes
- ✅ Consistent naming throughout the application

## Testing

1. **Syntax Validation**: ✅ No Python syntax errors
2. **Mapping Consistency**: ✅ All display names have corresponding internal names
3. **Application Startup**: ✅ Streamlit application runs without errors
4. **Comprehensive Test**: ✅ Created and ran `test_aoi_export_mapping.py` - all tests pass

## Related Code Locations

The fix ensures consistency across these related code sections:

1. **Export Selection**: `pages/analyze_data_page.py` lines 177-185 (fixed)
2. **Attribute Mapping**: `pages/analyze_data_page.py` lines 113-120 (correct)
3. **Constants**: `common/constants.py` lines 49-58 (correct)
4. **Display Lists**: `common/constants.py` lines 10-34 (correct)

## Conclusion

The KeyError: 'WOSS' issue has been successfully resolved by ensuring consistent naming between the export selection list and the attribute mapping dictionary. The fix maintains backward compatibility and aligns with the existing naming conventions used throughout the application.
