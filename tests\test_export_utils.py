import unittest
import streamlit as st
from unittest.mock import MagicMock

# Add parent directory to path to import modules
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.export_utils import generate_aoi_export_filename

class TestExportUtils(unittest.TestCase):
    """Test case for export utility functions."""

    def setUp(self):
        """Set up test environment."""
        # Create a mock session state object that behaves like streamlit's
        self.mock_session_state = MagicMock()
        st.session_state = self.mock_session_state

    def test_generate_aoi_export_filename_with_aoi(self):
        """Test generate_aoi_export_filename with AOI parameters."""
        # Set up mock session state with AOI parameters
        self.mock_session_state.aoi_inline_min = 1000
        self.mock_session_state.aoi_inline_max = 1500
        self.mock_session_state.aoi_xline_min = 2000
        self.mock_session_state.aoi_xline_max = 2500

        # Call the function
        filename = generate_aoi_export_filename("test_export", "sgy")

        # Check the result
        expected_filename = "test_export_IL1000-1500_XL2000-2500.sgy"
        self.assertEqual(filename, expected_filename)

    def test_generate_aoi_export_filename_without_aoi(self):
        """Test generate_aoi_export_filename without AOI parameters."""
        # Set up mock session state without AOI parameters
        self.mock_session_state.aoi_inline_min = None

        # Call the function
        filename = generate_aoi_export_filename("test_export", "sgy")

        # Check the result
        expected_filename = "test_export.sgy"
        self.assertEqual(filename, expected_filename)

if __name__ == '__main__':
    unittest.main()