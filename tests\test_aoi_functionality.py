"""
Test script for AOI functionality in the WOSS Seismic Analysis Tool.

This script tests the enhanced AOI functionality, including:
1. Manual definition of inline and crossline ranges
2. Validation of AOI boundaries
3. Integration with export operations
4. Compatibility with existing processing pipeline

Usage:
    python -m tests.test_aoi_functionality
"""

import unittest
import sys
import os
import pandas as pd
import numpy as np
import streamlit as st
from unittest.mock import patch, MagicMock

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules to test
from utils.aoi_validation import (
    validate_aoi_selection,
    process_aoi_selection,
    initialize_aoi_session_state,
    clear_aoi_boundaries,
    initialize_suggested_aoi_boundaries,
    validate_aoi_size_performance,
    show_aoi_preview
)

class TestAOIFunctionality(unittest.TestCase):
    """Test case for AOI functionality."""

    def setUp(self):
        """Set up test environment."""
        # Create a mock session state object that behaves like streamlit's
        class MockSessionState:
            def __init__(self):
                self.aoi_inline_min = 1000
                self.aoi_inline_max = 1500
                self.aoi_xline_min = 2000
                self.aoi_xline_max = 2500
                self.aoi_definition_mode = 'manual'
                self.aoi_auto_initialized = False
                self.aoi_manual_input_enabled = True
                self.aoi_bounds = {
                    'inline_min': 1000,
                    'inline_max': 1500,
                    'xline_min': 2000,
                    'xline_max': 2500
                }
                self.headers_df = pd.DataFrame({
                    'inline': np.concatenate([np.arange(1000, 1501) for _ in range(501)]),
                    'crossline': np.repeat(np.arange(2000, 2501), 501),
                    'trace_idx': np.arange(501 * 501)
                })
                self.area_selected = False
                self.area_selected_mode = None
                self.selected_indices = []
                self.aoi_boundaries_confirmed = False
                self.aoi_selection_confirmed = False
                self.area_selected_details = None

            def get(self, key, default=None):
                return getattr(self, key, default)

        self.mock_session_state = MockSessionState()

        # Create patches for streamlit session state and UI functions
        self.session_state_patch = patch('streamlit.session_state', self.mock_session_state)
        self.success_patch = patch('streamlit.success')
        self.error_patch = patch('streamlit.error')

        self.mock_st = self.session_state_patch.start()
        self.mock_success = self.success_patch.start()
        self.mock_error = self.error_patch.start()
        
    def tearDown(self):
        """Clean up after tests."""
        self.session_state_patch.stop()
        self.success_patch.stop()
        self.error_patch.stop()
    
    def test_aoi_validation(self):
        """Test AOI validation functionality."""
        # Test valid AOI
        result = validate_aoi_selection()
        self.assertTrue(result['valid'])
        self.assertIn('trace_count', result)
        self.assertIn('aoi_df', result)
        
        # Test invalid AOI - min > max
        self.mock_session_state.aoi_inline_min = 1600
        result = validate_aoi_selection()
        self.assertFalse(result['valid'])
        self.assertIn('error', result)

        # Reset to valid values
        self.mock_session_state.aoi_inline_min = 1000
    
    def test_aoi_performance_validation(self):
        """Test AOI performance validation."""
        # Test small AOI
        result = validate_aoi_size_performance(1000)
        self.assertTrue(result['valid'])
        self.assertEqual(len(result['warnings']), 0)
        
        # Test large AOI
        result = validate_aoi_size_performance(60000)
        self.assertTrue(result['valid'])
        self.assertGreater(len(result['warnings']), 0)
        self.assertGreater(len(result['recommendations']), 0)
        
        # Test very large AOI
        result = validate_aoi_size_performance(150000)
        self.assertTrue(result['valid'])
        self.assertGreater(len(result['warnings']), 0)
        self.assertGreater(len(result['recommendations']), 0)
    
    def test_clear_aoi_boundaries(self):
        """Test clearing AOI boundaries."""
        with patch('utils.aoi_validation.logging') as mock_logging:
            clear_aoi_boundaries()

            # Check that boundaries were cleared
            self.assertIsNone(self.mock_session_state.aoi_inline_min)
            self.assertIsNone(self.mock_session_state.aoi_inline_max)
            self.assertIsNone(self.mock_session_state.aoi_xline_min)
            self.assertIsNone(self.mock_session_state.aoi_xline_max)
            self.assertIsNone(self.mock_session_state.aoi_bounds)
            self.assertFalse(self.mock_session_state.area_selected)

            # Check that logging was called
            mock_logging.info.assert_called_once()
    
    def test_initialize_suggested_boundaries(self):
        """Test initializing suggested AOI boundaries."""
        # Clear boundaries first
        clear_aoi_boundaries()

        # Initialize suggested boundaries
        with patch('utils.aoi_validation.logging') as mock_logging:
            initialize_suggested_aoi_boundaries(self.mock_session_state.headers_df)

            # Check that boundaries were set
            self.assertIsNotNone(self.mock_session_state.aoi_inline_min)
            self.assertIsNotNone(self.mock_session_state.aoi_inline_max)
            self.assertIsNotNone(self.mock_session_state.aoi_xline_min)
            self.assertIsNotNone(self.mock_session_state.aoi_xline_max)
            self.assertTrue(self.mock_session_state.aoi_auto_initialized)

            # Check that logging was called
            mock_logging.info.assert_called_once()
    
    def test_process_aoi_selection(self):
        """Test processing AOI selection."""
        # Set up valid AOI
        self.mock_session_state.aoi_inline_min = 1000
        self.mock_session_state.aoi_inline_max = 1500
        self.mock_session_state.aoi_xline_min = 2000
        self.mock_session_state.aoi_xline_max = 2500

        # Process AOI selection
        result = process_aoi_selection()

        # Check result
        self.assertTrue(result)
        self.assertTrue(self.mock_session_state.area_selected)
        self.assertEqual(self.mock_session_state.area_selected_mode, 'aoi')
        self.assertIsNotNone(self.mock_session_state.selected_indices)

        # Check that success message was shown
        self.mock_success.assert_called_once()
        self.mock_error.assert_not_called()

if __name__ == '__main__':
    unittest.main()
