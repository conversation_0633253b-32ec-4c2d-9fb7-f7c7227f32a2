# Archive folders and their contents
archive/
archive/check_syntax.py
archive/debug_descriptor_pipeline.py
archive/debug_descriptor_types.py
archive/debug_view_results.py
archive/final_syntax_check.py
archive/test_descriptor_fix.py
archive/test_export_hfc_fix.py
archive/test_hfc_fix.py
archive/test_normalization_fix.py
archive/test_simplified_normalization.py
archive/test_syntax.py
archive/test_syntax_simple.py

# Backup folders
backup_tk/
backup_tk/3D_WOSS_Main_Script_init.py
backup_tk/dlogst_spec_descriptor_gpu_init.py

# Initial app folders
initial_app/
initial_app/app_ref.py
initial_app/data_utils.py
initial_app/dlogst_spec_descriptor_cpu.py
initial_app/dlogst_spec_descriptor_gpu.py
initial_app/export_utils.py
initial_app/processing.py
initial_app/utils.py
initial_app/visualization.py
initial_app/Load/
initial_app/Load/0_💾Import_seismic.py
initial_app/Load/custom_blocks.py
initial_app/Load/data_classes.py
initial_app/Load/Hello.py

# Documentation folders
docs/
docs/fix/
docs/fix/1_refactoring_step2_plan.md
docs/fix/2_refactoring_plan.md
docs/fix/analysis_report.md
docs/fix/ARRAY_ORIENTATION_CONVENTIONS.md
docs/fix/ATTRIBUTEERROR_FIX_SUMMARY.md
docs/fix/clean_up.md
docs/fix/DESCRIPTOR_TYPE_FIX_COMPREHENSIVE.md
docs/fix/descriptor_type_fix_summary.md
docs/fix/descriptor_validation_fix.md
docs/fix/gpu_logging_fix_plan.md
docs/fix/gpu_only_processing_plan.md
docs/fix/guideline.md
docs/fix/HFC_EXPORT_FIX_SUMMARY.md
docs/fix/infinite_loop_fix_plan.md
docs/fix/inline_analysis_workflow.md
docs/fix/inline_xline_selection_fix_guide.md
docs/fix/multi_well_marker_modification_plan.md
docs/fix/NORMALIZATION_IMPLEMENTATION_SUMMARY.md
docs/fix/option2_implementation_guide.md
docs/fix/option4_aoi_export_documentation.md
docs/fix/opus_fix_inline.md
docs/fix/polyline_file_import_guide.md
docs/fix/refactoring_guideline_generals.md
docs/fix/reference_streamlit_option1.md
docs/fix/reference_streamlit_option2.md
docs/fix/reference_streamlit_option4.md
docs/fix/reference_streamlit_option5.md
docs/fix/reference_streamlit_step_2.md
docs/fix/rules.md
docs/fix/SHAPE_PARAMETER_FIX_SUMMARY.md
docs/fix/SIMPLIFIED_NORMALIZATION_SUMMARY.md
docs/fix/view_results_fix_summary.md
docs/fix/well_analysis_enhancement_plan.md
docs/fix/well_marker_multiselect_plan.md
docs/plot_fix/
docs/plot_fix/session_state_fix_plan.md

# Test files
test_polyline_fix.py
test_polyline_functionality.py
test_polyline.txt

# Additional test files
test_polyline_fix.py
test_polyline_functionality.py

# Python cache files
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Streamlit cache
.streamlit/