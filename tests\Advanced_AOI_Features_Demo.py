"""
Advanced AOI Features Demonstration Script

This script demonstrates all the advanced AOI functionality including:
1. AOI Import/Export
2. AOI Visualization
3. AOI Templates
4. AOI Comparison Tools
5. Performance Optimization
"""

import pandas as pd
import numpy as np
import json
import sys
import os
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import AOI functions
from utils.aoi_validation import (
    validate_aoi_size_performance,
    get_aoi_boundary_suggestions,
    get_aoi_templates,
    export_aoi_definition,
    import_aoi_definition
)

def create_comprehensive_sample_data():
    """Create comprehensive sample seismic data for demonstration."""
    print("Creating comprehensive sample seismic data...")
    
    # Create a realistic 3D seismic survey
    inline_range = range(2000, 3001)  # 1001 inlines
    xline_range = range(5000, 6001)   # 1001 crosslines
    
    # Create headers DataFrame with realistic distribution
    inlines = []
    crosslines = []
    trace_indices = []
    
    trace_idx = 0
    # Simulate some missing traces (realistic for marine surveys)
    missing_probability = 0.02  # 2% missing traces
    
    for inline in inline_range:
        for xline in xline_range:
            # Randomly skip some traces to simulate real survey conditions
            if np.random.random() > missing_probability:
                inlines.append(inline)
                crosslines.append(xline)
                trace_indices.append(trace_idx)
                trace_idx += 1
    
    headers_df = pd.DataFrame({
        'inline': inlines,
        'crossline': crosslines,
        'trace_idx': trace_indices
    })
    
    print(f"Created comprehensive sample data:")
    print(f"  Total traces: {len(headers_df):,}")
    print(f"  Inline range: {min(inlines)} - {max(inlines)}")
    print(f"  Crossline range: {min(crosslines)} - {max(crosslines)}")
    print(f"  Coverage: {len(headers_df)/(len(inline_range)*len(xline_range))*100:.1f}%")
    
    return headers_df

def demonstrate_aoi_templates(headers_df):
    """Demonstrate AOI templates for geological scenarios."""
    print("\n" + "="*80)
    print("ADVANCED AOI TEMPLATES DEMONSTRATION")
    print("="*80)
    
    templates = get_aoi_templates(headers_df)
    
    # Group by category
    categories = {}
    for template_key, template in templates.items():
        category = template['category']
        if category not in categories:
            categories[category] = []
        categories[category].append((template_key, template))
    
    for category, category_templates in categories.items():
        print(f"\n{category.upper()} TEMPLATES:")
        print("-" * 50)
        
        for template_key, template in category_templates:
            print(f"\n{template['name']}")
            print(f"  Description: {template['description']}")
            print(f"  Use case: {template['use_case']}")
            print(f"  Boundaries: IL {template['inline_min']:,}-{template['inline_max']:,}, "
                  f"XL {template['xline_min']:,}-{template['xline_max']:,}")
            
            # Calculate template statistics
            inline_span = template['inline_max'] - template['inline_min'] + 1
            xline_span = template['xline_max'] - template['xline_min'] + 1
            estimated_traces = inline_span * xline_span
            
            print(f"  Grid size: {inline_span:,} × {xline_span:,} = {estimated_traces:,} traces")
            
            # Performance assessment
            perf_result = validate_aoi_size_performance(estimated_traces)
            if perf_result['warnings']:
                print(f"  Performance: ⚠️  {perf_result['warnings'][0]}")
            else:
                print(f"  Performance: ✅ Optimal")

def demonstrate_aoi_import_export():
    """Demonstrate AOI import/export functionality."""
    print("\n" + "="*80)
    print("AOI IMPORT/EXPORT DEMONSTRATION")
    print("="*80)
    
    # Create sample AOI definitions
    sample_aois = [
        {
            'name': 'Reservoir Prospect Alpha',
            'inline_min': 2200, 'inline_max': 2400,
            'xline_min': 5200, 'xline_max': 5400,
            'definition_mode': 'manual'
        },
        {
            'name': 'Structural Analysis Beta',
            'inline_min': 2100, 'inline_max': 2600,
            'xline_min': 5100, 'xline_max': 5600,
            'definition_mode': 'suggested'
        },
        {
            'name': 'QC Corner Check',
            'inline_min': 2000, 'inline_max': 2100,
            'xline_min': 5000, 'xline_max': 5100,
            'definition_mode': 'manual'
        }
    ]
    
    print("Sample AOI Definitions for Export:")
    print("-" * 40)
    
    exported_files = []
    
    for aoi in sample_aois:
        print(f"\n{aoi['name']}:")
        print(f"  Boundaries: IL {aoi['inline_min']}-{aoi['inline_max']}, "
              f"XL {aoi['xline_min']}-{aoi['xline_max']}")
        print(f"  Mode: {aoi['definition_mode']}")
        
        # Create export format
        aoi_definition = {
            'version': '1.0',
            'created_at': datetime.now().isoformat(),
            'aoi_boundaries': {
                'inline_min': aoi['inline_min'],
                'inline_max': aoi['inline_max'],
                'xline_min': aoi['xline_min'],
                'xline_max': aoi['xline_max']
            },
            'aoi_metadata': {
                'definition_mode': aoi['definition_mode'],
                'auto_initialized': False,
                'description': aoi['name']
            }
        }
        
        # Export to JSON
        json_data = json.dumps(aoi_definition, indent=2)
        filename = f"aoi_{aoi['name'].lower().replace(' ', '_')}.json"
        
        try:
            with open(filename, 'w') as f:
                f.write(json_data)
            print(f"  ✅ Exported to: {filename}")
            exported_files.append(filename)
        except Exception as e:
            print(f"  ❌ Export failed: {str(e)}")
    
    # Demonstrate import
    print(f"\nImport Demonstration:")
    print("-" * 40)
    
    for filename in exported_files:
        try:
            with open(filename, 'r') as f:
                json_data = f.read()
            
            # Simulate import (without actual streamlit session state)
            aoi_definition = json.loads(json_data)
            boundaries = aoi_definition['aoi_boundaries']
            metadata = aoi_definition.get('aoi_metadata', {})
            
            print(f"\n✅ Successfully imported: {filename}")
            print(f"  Description: {metadata.get('description', 'N/A')}")
            print(f"  Boundaries: IL {boundaries['inline_min']}-{boundaries['inline_max']}, "
                  f"XL {boundaries['xline_min']}-{boundaries['xline_max']}")
            print(f"  Mode: {metadata.get('definition_mode', 'Unknown')}")
            print(f"  Created: {aoi_definition.get('created_at', 'Unknown')}")
            
        except Exception as e:
            print(f"❌ Import failed for {filename}: {str(e)}")
    
    # Cleanup
    for filename in exported_files:
        try:
            os.remove(filename)
            print(f"🗑️  Cleaned up: {filename}")
        except:
            pass

def demonstrate_aoi_comparison():
    """Demonstrate AOI comparison functionality."""
    print("\n" + "="*80)
    print("AOI COMPARISON TOOLS DEMONSTRATION")
    print("="*80)
    
    # Create sample AOI comparisons
    comparison_aois = [
        {
            'name': 'Small Prospect',
            'inline_min': 2200, 'inline_max': 2300,
            'xline_min': 5200, 'xline_max': 5300,
            'definition_mode': 'manual'
        },
        {
            'name': 'Medium Field',
            'inline_min': 2150, 'inline_max': 2350,
            'xline_min': 5150, 'xline_max': 5350,
            'definition_mode': 'suggested'
        },
        {
            'name': 'Large Regional',
            'inline_min': 2100, 'inline_max': 2500,
            'xline_min': 5100, 'xline_max': 5500,
            'definition_mode': 'manual'
        },
        {
            'name': 'Processing Test',
            'inline_min': 2250, 'inline_max': 2260,
            'xline_min': 5250, 'xline_max': 5260,
            'definition_mode': 'manual'
        }
    ]
    
    print("AOI Comparison Analysis:")
    print("-" * 40)
    
    comparison_data = []
    
    for aoi in comparison_aois:
        # Calculate statistics
        inline_span = aoi['inline_max'] - aoi['inline_min'] + 1
        xline_span = aoi['xline_max'] - aoi['xline_min'] + 1
        grid_size = inline_span * xline_span
        
        # Estimate trace count (assuming 98% coverage)
        estimated_traces = int(grid_size * 0.98)
        
        # Performance assessment
        perf_result = validate_aoi_size_performance(estimated_traces)
        performance_status = "Optimal" if not perf_result['warnings'] else "Large AOI"
        
        comparison_data.append({
            'name': aoi['name'],
            'inline_span': inline_span,
            'xline_span': xline_span,
            'grid_size': grid_size,
            'estimated_traces': estimated_traces,
            'performance': performance_status,
            'mode': aoi['definition_mode']
        })
        
        print(f"\n{aoi['name']}:")
        print(f"  Boundaries: IL {aoi['inline_min']}-{aoi['inline_max']}, "
              f"XL {aoi['xline_min']}-{aoi['xline_max']}")
        print(f"  Grid size: {inline_span:,} × {xline_span:,} = {grid_size:,}")
        print(f"  Estimated traces: {estimated_traces:,}")
        print(f"  Performance: {performance_status}")
        print(f"  Definition mode: {aoi['definition_mode']}")
    
    # Summary comparison
    print(f"\nComparison Summary:")
    print("-" * 40)
    
    # Sort by trace count
    comparison_data.sort(key=lambda x: x['estimated_traces'])
    
    print(f"{'AOI Name':<20} {'Traces':<12} {'Grid Size':<15} {'Performance':<12}")
    print("-" * 65)
    
    for data in comparison_data:
        print(f"{data['name']:<20} {data['estimated_traces']:<12,} "
              f"{data['grid_size']:<15,} {data['performance']:<12}")
    
    # Performance recommendations
    print(f"\nPerformance Recommendations:")
    print("-" * 40)
    
    for data in comparison_data:
        if data['estimated_traces'] > 100000:
            print(f"⚠️  {data['name']}: Consider batch processing for large AOI")
        elif data['estimated_traces'] > 50000:
            print(f"💡 {data['name']}: Monitor processing time for medium AOI")
        else:
            print(f"✅ {data['name']}: Optimal size for fast processing")

def main():
    """Main demonstration function for advanced AOI features."""
    print("ADVANCED AOI FEATURES DEMONSTRATION")
    print("="*80)
    print("This script demonstrates advanced AOI functionality including:")
    print("• AOI Templates for geological scenarios")
    print("• AOI Import/Export capabilities")
    print("• AOI Comparison tools")
    print("• Performance optimization features")
    
    # Create comprehensive sample data
    headers_df = create_comprehensive_sample_data()
    
    # Run advanced demonstrations
    demonstrate_aoi_templates(headers_df)
    demonstrate_aoi_import_export()
    demonstrate_aoi_comparison()
    
    print("\n" + "="*80)
    print("ADVANCED FEATURES DEMONSTRATION COMPLETE")
    print("="*80)
    print("\nAdvanced Features Summary:")
    print("✅ AOI Templates - Predefined scenarios for common geological analysis")
    print("✅ Import/Export - Save and share AOI definitions across sessions")
    print("✅ Comparison Tools - Compare multiple AOI selections side-by-side")
    print("✅ Visualization - Interactive maps and coverage heatmaps")
    print("✅ Performance Optimization - Smart warnings and recommendations")
    
    print("\nThese advanced features provide:")
    print("• Streamlined workflows for common geological scenarios")
    print("• Collaboration capabilities through AOI sharing")
    print("• Data-driven decision making with comparison tools")
    print("• Visual feedback for better spatial understanding")
    print("• Performance optimization for large-scale analysis")

if __name__ == '__main__':
    main()
