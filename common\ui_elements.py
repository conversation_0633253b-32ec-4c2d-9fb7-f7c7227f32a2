"""
Common UI elements for the WOSS Seismic Analysis Tool.
This module contains reusable UI components used throughout the application.
"""

import streamlit as st

def slider_with_number_input(label, min_value, max_value, value, step, key_base, format=None):
    """
    Creates a slider with a number input directly above it for precise control.
    
    Args:
        label (str): The label for the slider and number input
        min_value (float/int): Minimum allowed value
        max_value (float/int): Maximum allowed value
        value (float/int): Default value
        step (float/int): Step size for the slider and number input
        key_base (str): Base key to create unique keys for the slider and number input
        format (str, optional): Format string for the number input (e.g., "%.2f")
    
    Returns:
        float/int: The selected value
    """
    # Create a unique key for the number input
    number_input_key = f"{key_base}_number_input"
    slider_key = f"{key_base}_slider"

    # Create columns for the number input and label
    col1, col2 = st.columns([3, 1])

    # Display the label in the first column
    col1.write(label)

    # Create the number input in the second column
    if format:
        number_value = col2.number_input(
            "", 
            min_value=min_value, 
            max_value=max_value, 
            value=value, 
            step=step, 
            key=number_input_key, 
            format=format, 
            label_visibility="collapsed"
        )
    else:
        number_value = col2.number_input(
            "", 
            min_value=min_value, 
            max_value=max_value, 
            value=value, 
            step=step, 
            key=number_input_key, 
            label_visibility="collapsed"
        )

    # Create the slider with the same value
    slider_value = st.slider(
        "", 
        min_value=min_value, 
        max_value=max_value, 
        value=number_value, 
        step=step, 
        key=slider_key, 
        label_visibility="collapsed"
    )

    # Synchronize values if they differ (e.g., if one was changed by the user)
    if slider_value != number_value:
        # The slider was moved, update the number input on next rerun
        return slider_value

    return number_value

def get_suggested_batch_size():
    """
    Estimate a reasonable batch size for GPU processing based on available GPU memory.

    Returns:
        tuple: (suggested_batch_size, free_memory_mb)
    """
    try:
        import torch
        # Try to get GPU memory info from torch
        if torch.cuda.is_available():
            # Get total and allocated memory
            total_memory = torch.cuda.get_device_properties(0).total_memory
            allocated_memory = torch.cuda.memory_allocated(0)
            free_memory = total_memory - allocated_memory

            # Convert to MB for easier reading
            free_memory_mb = free_memory / (1024 * 1024)

            # Estimate batch size based on free memory
            # Assume each trace needs about 10MB of GPU memory (very rough estimate)
            # and leave 20% of free memory as buffer
            suggested_batch = int((free_memory_mb * 0.8) / 10)

            # Clamp to reasonable range
            suggested_batch = max(10, min(suggested_batch, 1000))

            return suggested_batch, free_memory_mb
        else:
            # No GPU available
            return 100, 0
    except Exception as e:
        # If anything goes wrong, return a conservative default
        import logging
        logging.warning(f"Error estimating GPU memory: {e}. Using default batch size.")
        return 100, 0

def get_suggested_batch_size_for_export(num_unique_groups):
    """
    Suggest a reasonable batch size for export based on the number of unique groups.

    Args:
        num_unique_groups: Number of unique groups (inlines, crosslines, etc.)

    Returns:
        int: Suggested batch size for export
    """
    # Simple heuristic: for small numbers, use the number itself
    if num_unique_groups <= 10:
        return num_unique_groups

    # For larger numbers, use a fraction to keep file sizes manageable
    elif num_unique_groups <= 100:
        return max(10, num_unique_groups // 5)
    elif num_unique_groups <= 1000:
        return max(20, num_unique_groups // 10)
    else:
        return max(50, num_unique_groups // 20)
