{"mcpServers": {"github.com/upstash/context7-mcp": {"autoApprove": ["resolve-library-id", "get-library-docs"], "disabled": false, "timeout": 60, "command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"], "transportType": "stdio"}, "github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-sequential-thinking"], "transportType": "stdio"}, "github.com/modelcontextprotocol/servers/tree/main/src/memory": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-memory"], "disabled": false, "autoApprove": [], "transportType": "stdio"}}}