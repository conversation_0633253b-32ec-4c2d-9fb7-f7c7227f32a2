"""
Phase 2 AOI Implementation Test Runner.

This script provides a simple way to test the Phase 2 AOI implementation
without requiring a full pytest setup. It performs basic validation of
the key AOI functions to ensure they work correctly.
"""

import sys
import os
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Mock streamlit for testing
class MockStreamlit:
    class SessionState:
        def __init__(self):
            self._state = {}
        
        def __getattr__(self, name):
            return self._state.get(name)
        
        def __setattr__(self, name, value):
            if name.startswith('_'):
                super().__setattr__(name, value)
            else:
                self._state[name] = value
        
        def __hasattr__(self, name):
            return name in self._state
        
        def clear(self):
            self._state.clear()
        
        def get(self, name, default=None):
            return self._state.get(name, default)
    
    def __init__(self):
        self.session_state = self.SessionState()
    
    def error(self, message):
        print(f"ERROR: {message}")
    
    def warning(self, message):
        print(f"WARNING: {message}")
    
    def success(self, message):
        print(f"SUCCESS: {message}")
    
    def info(self, message):
        print(f"INFO: {message}")

# Mock streamlit module
sys.modules['streamlit'] = MockStreamlit()
import streamlit as st

def create_test_data():
    """Create test data for AOI validation."""
    # Create mock headers DataFrame
    headers_df = pd.DataFrame({
        'trace_idx': range(100),
        'inline': np.repeat(range(1, 11), 10),
        'crossline': np.tile(range(1, 11), 10),
        'x_coord': np.random.rand(100) * 1000,
        'y_coord': np.random.rand(100) * 1000
    })
    
    return headers_df

def test_aoi_validation():
    """Test AOI validation functions."""
    print("\n=== Testing AOI Validation Functions ===")
    
    try:
        from utils.aoi_validation import (
            validate_aoi_selection,
            process_aoi_selection,
            initialize_aoi_session_state,
            validate_aoi_boundaries
        )
        
        # Initialize session state
        st.session_state.clear()
        initialize_aoi_session_state()
        
        # Setup test data
        headers_df = create_test_data()
        st.session_state.headers_df = headers_df
        
        # Test 1: Valid AOI boundaries
        print("\nTest 1: Valid AOI boundaries")
        st.session_state.aoi_inline_min = 2
        st.session_state.aoi_inline_max = 5
        st.session_state.aoi_xline_min = 3
        st.session_state.aoi_xline_max = 7
        
        result = validate_aoi_selection()
        if result['valid']:
            print(f"✅ Valid AOI: {result['trace_count']} traces selected")
        else:
            print(f"❌ Validation failed: {result['error']}")
        
        # Test 2: Invalid boundaries (min > max)
        print("\nTest 2: Invalid boundaries (min > max)")
        st.session_state.aoi_inline_min = 5
        st.session_state.aoi_inline_max = 2
        
        result = validate_aoi_selection()
        if not result['valid']:
            print(f"✅ Correctly detected invalid boundaries: {result['error']}")
        else:
            print("❌ Should have detected invalid boundaries")
        
        # Test 3: Boundaries outside data range
        print("\nTest 3: Boundaries outside data range")
        st.session_state.aoi_inline_min = 50
        st.session_state.aoi_inline_max = 60
        st.session_state.aoi_xline_min = 3
        st.session_state.aoi_xline_max = 7
        
        result = validate_aoi_selection()
        if not result['valid']:
            print(f"✅ Correctly detected out-of-range boundaries: {result['error']}")
        else:
            print("❌ Should have detected out-of-range boundaries")
        
        # Test 4: Process AOI selection
        print("\nTest 4: Process AOI selection")
        st.session_state.aoi_inline_min = 2
        st.session_state.aoi_inline_max = 5
        st.session_state.aoi_xline_min = 3
        st.session_state.aoi_xline_max = 7
        
        with patch('streamlit.success'):
            result = process_aoi_selection()
            if result:
                print("✅ AOI processing successful")
                print(f"   Selected indices: {len(st.session_state.selected_indices)}")
                print(f"   Area selected: {st.session_state.area_selected}")
                print(f"   Selection mode: {st.session_state.area_selected_mode}")
            else:
                print("❌ AOI processing failed")
        
        print("\n✅ AOI Validation tests completed")
        return True
        
    except Exception as e:
        print(f"❌ AOI Validation test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_aoi_processing():
    """Test AOI processing functions."""
    print("\n=== Testing AOI Processing Functions ===")
    
    try:
        from utils.aoi_processing import (
            validate_aoi_processing_parameters,
            get_aoi_processing_configuration,
            get_aoi_memory_requirements,
            optimize_aoi_batch_size
        )
        
        # Setup AOI selection
        st.session_state.area_selected = True
        st.session_state.area_selected_mode = 'aoi'
        st.session_state.selected_indices = list(range(50))
        st.session_state.aoi_bounds = {
            'inline_min': 2, 'inline_max': 5,
            'xline_min': 3, 'xline_max': 7
        }
        
        # Test 1: Validate processing parameters
        print("\nTest 1: Validate processing parameters")
        validation = validate_aoi_processing_parameters()
        if validation['valid']:
            print("✅ Processing parameters valid")
            if validation['warnings']:
                for warning in validation['warnings']:
                    print(f"   ⚠️ {warning}")
        else:
            print("❌ Processing parameters invalid:")
            for error in validation['errors']:
                print(f"   • {error}")
        
        # Test 2: Get processing configuration
        print("\nTest 2: Get processing configuration")
        config = get_aoi_processing_configuration()
        if 'error' not in config:
            print("✅ Processing configuration retrieved")
            print(f"   Processing mode: {config.get('processing_mode')}")
            print(f"   Trace count: {config.get('trace_count')}")
            print(f"   GPU available: {config.get('gpu_available')}")
        else:
            print(f"❌ Configuration error: {config['error']}")
        
        # Test 3: Memory requirements
        print("\nTest 3: Memory requirements estimation")
        memory_req = get_aoi_memory_requirements()
        if 'error' not in memory_req:
            print("✅ Memory requirements calculated")
            print(f"   Trace count: {memory_req.get('trace_count')}")
            print(f"   Estimated total MB: {memory_req.get('estimated_total_mb', 0):.1f}")
        else:
            print(f"❌ Memory estimation error: {memory_req['error']}")
        
        # Test 4: Batch size optimization
        print("\nTest 4: Batch size optimization")
        batch_size = optimize_aoi_batch_size()
        print(f"✅ Optimized batch size: {batch_size}")
        
        print("\n✅ AOI Processing tests completed")
        return True
        
    except Exception as e:
        print(f"❌ AOI Processing test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """Test error handling utilities."""
    print("\n=== Testing Error Handling Functions ===")
    
    try:
        from utils.error_handling import (
            validate_session_state_prerequisites,
            validate_data_integrity,
            WOSSError,
            AOIError
        )
        
        # Test 1: Session state prerequisites
        print("\nTest 1: Session state prerequisites validation")
        required_vars = ['aoi_inline_min', 'aoi_inline_max', 'headers_df']
        validation = validate_session_state_prerequisites(required_vars)
        
        if validation['valid']:
            print("✅ All required session state variables present")
        else:
            print("❌ Missing session state variables:")
            for var in validation.get('missing_vars', []):
                print(f"   • {var}")
        
        # Test 2: Data integrity validation
        print("\nTest 2: Data integrity validation")
        test_data = [1, 2, 3, 4, 5]
        validation = validate_data_integrity(test_data, "test_data", min_size=3, max_size=10)
        
        if validation['valid']:
            print("✅ Data integrity validation passed")
        else:
            print("❌ Data integrity validation failed:")
            for error in validation['errors']:
                print(f"   • {error}")
        
        # Test 3: Custom exceptions
        print("\nTest 3: Custom exception classes")
        try:
            raise AOIError("Test AOI error", suggestions=["Check boundaries", "Reload data"])
        except AOIError as e:
            print("✅ AOIError exception works correctly")
            print(f"   Message: {e.message}")
            print(f"   Suggestions: {e.suggestions}")
        
        print("\n✅ Error Handling tests completed")
        return True
        
    except Exception as e:
        print(f"❌ Error Handling test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all Phase 2 tests."""
    print("🧪 Phase 2 AOI Implementation Test Suite")
    print("=" * 50)
    
    test_results = []
    
    # Run all tests
    test_results.append(("AOI Validation", test_aoi_validation()))
    test_results.append(("AOI Processing", test_aoi_processing()))
    test_results.append(("Error Handling", test_error_handling()))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} test suites passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Phase 2 implementation is working correctly.")
        return True
    else:
        print(f"\n⚠️ {total - passed} test suite(s) failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
