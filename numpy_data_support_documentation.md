# NumPy Data Format Support Documentation

## Overview

The original WOSS Seismic Analysis Tool (`initial_app/`) supported both loading and exporting seismic data in NumPy format (.npy files) alongside SEG-Y format. This comprehensive feature allowed users to work with pre-processed seismic data stored as NumPy arrays, providing faster loading times, more flexibility, and seamless integration with Python scientific workflows.

**Status**: This NumPy format support is **NOT currently implemented** in the main application codebase and represents a significant missing capability that could enhance the tool's versatility and performance.

## Data Loading Implementation

### Supported Data Format Options

The original application supported four data format options:
- **Segy2D**: 2D seismic data in SEG-Y format
- **Segy3D**: 3D seismic data in SEG-Y format  
- **Numpy2D**: 2D seismic data as NumPy arrays
- **Numpy3D**: 3D seismic data as NumPy arrays

### Data Classes Architecture

Located in `initial_app/Load/data_classes.py`:

#### 1. Numpy2D Class (Lines 261-312)
```python
class Numpy2D(SeismicData):
    def __init__(self, data):
        # Accepts both file paths (string) and NumPy arrays directly
        if isinstance(data, str):
            self._data = np.load(data)
        elif isinstance(data, np.ndarray):
            self._data = data
```

**Key Features:**
- **Flexible Input**: Accepts file paths or direct NumPy arrays
- **Data Processing**: `make_axis_devisable_by(factor)` for GPU optimization
- **Visualization Support**: Calculates 95th percentile for display scaling
- **Metadata**: Returns format="NUMPY", dimension="2D"
- **Fixed Sample Rate**: Uses default 1000 µs (1 ms) sampling

**Methods:**
- `get_iline()`: Returns full 2D data array
- `get_n_xlines()`: Returns first dimension size
- `get_n_zslices()`: Returns second dimension size
- `make_axis_devisable_by(factor)`: Crops array to be divisible by factor

#### 2. Numpy3D Class (Lines 313-369)
```python
class Numpy3D(SeismicData):
    def __init__(self, data):
        # Statistical analysis with random sampling
        n_slices = 10
        seis = [self.get_iline(random.randint(0, self.get_n_ilines()-1)) for i in range(n_slices)]
        self.vm = np.percentile(seis, 95)
```

**Key Features:**
- **Full 3D Support**: Complete inline, crossline, and timeslice access
- **Statistical Analysis**: Random sampling of 10 slices for visualization scaling
- **Memory Optimization**: Axis divisibility adjustment for GPU processing
- **Cube Access**: `get_cube()` method returns full 3D array

**Array Operations:**
- `get_iline(indx)`: Returns `data[indx,:,:]` (inline slice)
- `get_xline(indx)`: Returns `data[:,indx,:]` (crossline slice)
- `get_zslice(indx)`: Returns `data[:,:,indx]` (time/depth slice)
- `get_cube()`: Returns complete 3D array

### Expected File Structure

#### NumPy File Specifications

**2D Files (.npy)**:
```python
# Shape: (n_traces, n_samples)
# Example: (1000, 2000) = 1000 traces with 2000 time samples each
data_2d = np.array([[amplitude_values...], [...], ...])
np.save('seismic_2d.npy', data_2d)
```

**3D Files (.npy)**:
```python
# Shape: (n_inlines, n_crosslines, n_samples)  
# Example: (100, 200, 1500) = 100 inlines × 200 crosslines × 1500 time samples
data_3d = np.array([[[amplitude_values...], [...]], [...]])
np.save('seismic_3d.npy', data_3d)
```

**Data Requirements:**
- Data type: `float32` or `float64` (recommended: `float32` for GPU compatibility)
- Axis order: Inline → Crossline → Time/Depth
- Units: Seismic amplitude values (typically normalized)
- Memory: Ensure arrays fit in available RAM

## Export Utilities Implementation

### Export Architecture

Located in `initial_app/export_utils.py`, the export system provided sophisticated attribute selection and export capabilities.

#### 1. AttributeSelector Class (Lines 5-36)
```python
class AttributeSelector:
    def __init__(self, root, exportable_attrs):
        self.root = root
        self.exportable_attrs = exportable_attrs
        self.selected_attrs = []
    
    def show_dialog(self):
        # Creates tkinter dialog for attribute selection
        # Returns list of selected attributes
```

**Features:**
- **GUI Dialog**: User-friendly attribute selection interface
- **Multi-selection**: Support for selecting multiple spectral attributes
- **Scrollable List**: Handles large numbers of attributes
- **Modal Dialog**: Prevents user interaction with main window during selection

#### 2. Export Attribute Filtering (Lines 38-83)
```python
def select_export_attributes(root, desired_attrs, first_descriptor, sample_length):
    # Filter exportable attributes matching logic from processing pipeline
    exportable_attrs = [
        key for key in desired_attrs
        if key in first_descriptor and 
           isinstance(first_descriptor[key], np.ndarray) and 
           len(first_descriptor[key]) == sample_length
    ]
    
    # Optionally add WOSS if possible
    if ('hfc' in first_descriptor and
        'norm_fdom' in first_descriptor and
        'mag_voice_slope' in first_descriptor):
        exportable_attrs.append('WOSS')
```

**Export Logic:**
- **Validation**: Ensures attributes exist and have correct dimensions
- **Type Checking**: Verifies attributes are NumPy arrays
- **Length Verification**: Confirms arrays match expected sample length
- **WOSS Calculation**: Adds composite WOSS descriptor when components available

### Current Export Implementation Comparison

The current main application's export utilities (`utils/export_utils.py`) provide:

#### Enhanced Sorting Functions
```python
def sort_trace_indices_by_header_loader(header_loader, indices):
    # Sort indices by inline then crossline using SegyHeaderLoader
    mask = np.isin(header_loader.unique_indices, indices)
    inl = header_loader.inlines[mask]
    xl = header_loader.crosslines[mask]
    order = np.lexsort((xl, inl))
    return indices[order]
```

#### Streamlined Attribute Selection
```python
def select_export_attributes(exportable_attrs, first_descriptor, sample_length):
    # Simplified attribute filtering without GUI
    valid_attrs = [
        key for key in exportable_attrs
        if key in first_descriptor and 
           isinstance(first_descriptor[key], np.ndarray) and 
           len(first_descriptor[key]) == sample_length
    ]
```

## Integration Benefits

### Performance Advantages
1. **Loading Speed**: NumPy arrays load significantly faster than SEG-Y parsing
2. **Memory Efficiency**: Direct memory mapping possible with NumPy format
3. **Processing Speed**: No header processing overhead
4. **GPU Compatibility**: Native NumPy arrays work seamlessly with CuPy acceleration

### Workflow Flexibility
1. **Preprocessing Integration**: Support for custom data preprocessing pipelines
2. **Scientific Ecosystem**: Direct compatibility with scipy, pandas, matplotlib
3. **Data Manipulation**: Easy filtering, resampling, and transformation
4. **Format Conversion**: Bidirectional conversion between SEG-Y and NumPy

### Use Cases
- **Preprocessed Data**: Loading data that has been processed or filtered
- **Synthetic Seismic**: Computer-generated seismic models and simulations
- **Research Workflows**: Academic and experimental data analysis
- **Performance Critical**: Applications requiring maximum loading speed
- **Large Datasets**: Efficient handling of massive seismic volumes
- **Machine Learning**: Direct integration with ML preprocessing pipelines

## Implementation Requirements

### Missing Components in Main Application

To restore NumPy support, the following components need implementation:

#### 1. Data Classes Enhancement
```python
# Add to utils/data_utils.py
class Numpy2D(SeismicData):
    # Port from initial_app/Load/data_classes.py
    
class Numpy3D(SeismicData):
    # Port from initial_app/Load/data_classes.py
```

#### 2. UI Integration
```python
# Modify pages/load_data_page.py
data_format = st.radio(
    "Select Data Format:",
    ('SEG-Y Upload', 'NumPy Upload'),
    horizontal=True
)

if data_format == 'NumPy Upload':
    uploaded_file = st.file_uploader("Upload NumPy file", type=['npy'])
    dimension_type = st.radio("Data Dimension:", ('2D', '3D'))
```

#### 3. Session State Management
```python
# Update common/session_state.py
'numpy_data_loaded': False,
'numpy_data_type': None,  # '2D' or '3D'
'numpy_file_info': {},
```

#### 4. Export Enhancement
```python
# Enhance utils/export_utils.py
def export_to_numpy(data, output_path, format_type='3D'):
    """Export processed seismic data to NumPy format"""
    if format_type == '3D':
        # Save as (inline, crossline, time) array
        np.save(output_path, data)
    elif format_type == '2D':
        # Save as (trace, time) array
        np.save(output_path, data)
```

### Technical Considerations

#### Memory Management
- **File Size Limits**: NumPy files can be very large; may need chunked loading
- **Upload Limits**: Streamlit default 200MB upload limit may need adjustment
- **RAM Requirements**: Full arrays loaded into memory simultaneously

#### Data Validation
```python
def validate_numpy_data(data, expected_dimensions):
    """Validate NumPy array structure and content"""
    if not isinstance(data, np.ndarray):
        raise ValueError("Data must be NumPy array")
    
    if len(data.shape) != expected_dimensions:
        raise ValueError(f"Expected {expected_dimensions}D array")
    
    if data.dtype not in [np.float32, np.float64]:
        print(f"Warning: Converting {data.dtype} to float32")
        data = data.astype(np.float32)
    
    return data
```

#### Metadata Handling
Since NumPy arrays lack embedded metadata:
- **Coordinate Information**: Need separate coordinate files or user input
- **Sampling Rates**: Default to standard values or allow user specification
- **Units**: Document expected amplitude units and scaling
- **Geometry**: Store inline/crossline numbering schemes separately

## Export Capabilities

### Original Export Features

The original implementation supported:

1. **Attribute Selection Dialog**: Interactive GUI for choosing spectral descriptors
2. **Multi-format Export**: Both SEG-Y and NumPy output formats
3. **Validation Pipeline**: Comprehensive checking of exportable attributes
4. **WOSS Composite**: Automatic calculation of composite WOSS descriptor

### Recommended Export Enhancements

#### 1. Multiple Format Support
```python
def export_analysis_results(results, format_type='numpy', output_dir='./'):
    """Export spectral analysis results in multiple formats"""
    if format_type == 'numpy':
        # Export each attribute as separate .npy files
        for attr_name, attr_data in results.items():
            np.save(f"{output_dir}/{attr_name}.npy", attr_data)
    
    elif format_type == 'hdf5':
        # Export all attributes in single HDF5 file
        import h5py
        with h5py.File(f"{output_dir}/spectral_analysis.h5", 'w') as f:
            for attr_name, attr_data in results.items():
                f.create_dataset(attr_name, data=attr_data)
```

#### 2. Batch Export Operations
```python
def batch_export_attributes(trace_indices, descriptors, selected_attrs, output_format):
    """Export multiple attributes for selected traces"""
    for attr in selected_attrs:
        attr_data = extract_attribute_data(trace_indices, descriptors, attr)
        export_single_attribute(attr_data, attr, output_format)
```

## Implementation Roadmap

### Phase 1: Core NumPy Loading
1. Port `Numpy2D` and `Numpy3D` classes from original implementation
2. Add NumPy format selection to load data page
3. Implement file upload handling for `.npy` files
4. Add data validation and error handling

### Phase 2: UI Integration
1. Update session state management for NumPy data
2. Modify visualization components to handle NumPy data
3. Ensure compatibility with existing processing pipeline
4. Add progress indicators for large file loading

### Phase 3: Export Capabilities  
1. Implement NumPy export functionality in results pages
2. Add format selection options (SEG-Y, NumPy, HDF5)
3. Create attribute selection interface (Streamlit-based)
4. Add batch export capabilities for multiple attributes

### Phase 4: Advanced Features
1. Add coordinate system support for NumPy data
2. Implement metadata preservation and restoration
3. Add data format conversion utilities
4. Create comprehensive documentation and examples

## Conclusion

The NumPy data format support represents a valuable missing feature that would significantly enhance the WOSS Seismic Analysis Tool's capabilities. The original implementation provides a solid foundation for both loading and exporting NumPy-format seismic data, offering substantial performance and flexibility benefits for users working with preprocessed data, synthetic models, or research workflows.

Restoring this functionality would position the tool as a more comprehensive solution for modern seismic data analysis, bridging traditional SEG-Y workflows with contemporary Python-based scientific computing approaches.