#!/usr/bin/env python3
"""
Session state handler for WOSS Seismic Analysis Tool.
Provides robust handling of numpy arrays and complex data structures in Streamlit session state.
"""

import streamlit as st
import numpy as np
import logging
import pickle
import base64
from typing import Any, Dict, List, Union
import json

def safe_store_descriptors(descriptors: List[Dict[str, Any]], key: str = 'calculated_descriptors'):
    """
    Safely store descriptors in session state with proper numpy array handling.
    
    Args:
        descriptors: List of descriptor dictionaries
        key: Session state key to store under
    """
    logging.info(f"Storing {len(descriptors)} descriptors in session state under key '{key}'")
    
    # Validate descriptors before storing
    validated_descriptors = []
    for i, desc in enumerate(descriptors):
        if isinstance(desc, dict):
            # Ensure all numpy arrays are properly typed
            validated_desc = {}
            for desc_key, desc_value in desc.items():
                if isinstance(desc_value, np.ndarray):
                    # Ensure proper dtype
                    if desc_value.dtype.kind not in ['f', 'i', 'u']:
                        logging.warning(f"Descriptor {i}, key '{desc_key}' has non-numeric dtype {desc_value.dtype}, converting to float32")
                        validated_desc[desc_key] = desc_value.astype(np.float32)
                    else:
                        validated_desc[desc_key] = desc_value
                elif isinstance(desc_value, (int, float, np.number)):
                    # Convert scalar numbers to numpy arrays for consistency
                    validated_desc[desc_key] = np.array([desc_value], dtype=np.float32)
                elif isinstance(desc_value, str):
                    # Keep strings as-is (for error messages, etc.)
                    validated_desc[desc_key] = desc_value
                else:
                    # For other types, try to convert to numpy array
                    try:
                        validated_desc[desc_key] = np.asarray(desc_value, dtype=np.float32)
                    except:
                        # If conversion fails, keep original value
                        validated_desc[desc_key] = desc_value
                        logging.warning(f"Could not convert descriptor {i}, key '{desc_key}' to numpy array: {type(desc_value)}")
            
            validated_descriptors.append(validated_desc)
        else:
            # Non-dict descriptors (errors, etc.)
            validated_descriptors.append(desc)
            if not isinstance(desc, str):
                logging.warning(f"Descriptor {i} is not a dictionary: {type(desc)}")
    
    # Store in session state
    st.session_state[key] = validated_descriptors
    logging.info(f"Successfully stored {len(validated_descriptors)} validated descriptors")

def safe_retrieve_descriptors(key: str = 'calculated_descriptors') -> List[Dict[str, Any]]:
    """
    Safely retrieve descriptors from session state with type validation and recovery.
    
    Args:
        key: Session state key to retrieve from
        
    Returns:
        List of descriptor dictionaries
    """
    if key not in st.session_state:
        logging.warning(f"Key '{key}' not found in session state")
        return []
    
    descriptors = st.session_state[key]
    logging.info(f"Retrieved {len(descriptors) if hasattr(descriptors, '__len__') else 'unknown'} descriptors from session state")
    
    # Validate and recover descriptors
    validated_descriptors = []
    recovery_count = 0
    
    for i, desc in enumerate(descriptors):
        if isinstance(desc, dict):
            # Dictionary descriptor - validate numpy arrays
            validated_desc = {}
            for desc_key, desc_value in desc.items():
                if isinstance(desc_value, np.ndarray):
                    validated_desc[desc_key] = desc_value
                elif isinstance(desc_value, str) and desc_key in ['data', 'peak_freq', 'hfc', 'spec_centroid', 'fdom', 'norm_fdom']:
                    # Try to recover numpy arrays that might have been serialized as strings
                    try:
                        # Check if it's a string representation of a numpy array
                        if desc_value.startswith('[') and desc_value.endswith(']'):
                            # Try to parse as JSON array
                            array_data = json.loads(desc_value)
                            recovered_array = np.asarray(array_data, dtype=np.float32)
                            validated_desc[desc_key] = recovered_array
                            recovery_count += 1
                            logging.info(f"Recovered numpy array for descriptor {i}, key '{desc_key}' from string")
                        else:
                            # Keep as string (might be error message)
                            validated_desc[desc_key] = desc_value
                    except:
                        # Recovery failed, keep as string
                        validated_desc[desc_key] = desc_value
                        logging.warning(f"Failed to recover numpy array for descriptor {i}, key '{desc_key}' from string")
                else:
                    # Keep other types as-is
                    validated_desc[desc_key] = desc_value
            
            validated_descriptors.append(validated_desc)
            
        elif isinstance(desc, str):
            # String descriptor - try to recover if it's a serialized dictionary
            try:
                if desc.strip().startswith('{') and desc.strip().endswith('}'):
                    import ast
                    recovered_dict = ast.literal_eval(desc)
                    if isinstance(recovered_dict, dict):
                        validated_descriptors.append(recovered_dict)
                        recovery_count += 1
                        logging.info(f"Recovered descriptor {i} from string representation")
                    else:
                        # Not a valid dictionary, treat as error message
                        validated_descriptors.append({'error': desc, 'trace_idx': i})
                else:
                    # Regular string, treat as error message
                    validated_descriptors.append({'error': desc, 'trace_idx': i})
            except:
                # Recovery failed, treat as error message
                validated_descriptors.append({'error': desc, 'trace_idx': i})
                logging.warning(f"Failed to recover descriptor {i} from string: {desc[:100]}...")
        else:
            # Other types
            validated_descriptors.append(desc)
            logging.warning(f"Descriptor {i} has unexpected type: {type(desc)}")
    
    if recovery_count > 0:
        logging.info(f"Successfully recovered {recovery_count} descriptors from string representations")
    
    return validated_descriptors

def validate_descriptor_types(descriptors: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Validate descriptor types and return analysis report.
    
    Args:
        descriptors: List of descriptor dictionaries
        
    Returns:
        Dictionary with validation analysis
    """
    analysis = {
        'total_count': len(descriptors),
        'valid_dict_count': 0,
        'error_dict_count': 0,
        'string_count': 0,
        'other_type_count': 0,
        'numpy_array_keys': set(),
        'string_value_keys': set(),
        'missing_keys': set(),
        'type_issues': []
    }
    
    expected_keys = ['data', 'peak_freq', 'spec_centroid', 'fdom', 'norm_fdom', 'hfc']
    
    for i, desc in enumerate(descriptors):
        if isinstance(desc, dict):
            if 'error' in desc:
                analysis['error_dict_count'] += 1
            else:
                analysis['valid_dict_count'] += 1
                
                # Check for expected keys and their types
                for key in expected_keys:
                    if key in desc:
                        value = desc[key]
                        if isinstance(value, np.ndarray):
                            analysis['numpy_array_keys'].add(key)
                        elif isinstance(value, str):
                            analysis['string_value_keys'].add(key)
                            analysis['type_issues'].append(f"Descriptor {i}: {key} is string instead of numpy array")
                        else:
                            analysis['type_issues'].append(f"Descriptor {i}: {key} has unexpected type {type(value)}")
                    else:
                        analysis['missing_keys'].add(key)
        elif isinstance(desc, str):
            analysis['string_count'] += 1
        else:
            analysis['other_type_count'] += 1
    
    return analysis

def debug_session_state_descriptors():
    """Debug function to analyze descriptor types in session state."""
    print("=== SESSION STATE DESCRIPTOR DEBUG ===")
    
    descriptor_keys = ['calculated_descriptors', 'individual_well_analysis_results']
    
    for key in descriptor_keys:
        if key in st.session_state:
            descriptors = st.session_state[key]
            print(f"\n{key}: {len(descriptors) if hasattr(descriptors, '__len__') else 'N/A'} items")
            
            if isinstance(descriptors, list) and descriptors:
                analysis = validate_descriptor_types(descriptors)
                print(f"  Valid dicts: {analysis['valid_dict_count']}")
                print(f"  Error dicts: {analysis['error_dict_count']}")
                print(f"  Strings: {analysis['string_count']}")
                print(f"  Other types: {analysis['other_type_count']}")
                print(f"  NumPy array keys: {analysis['numpy_array_keys']}")
                print(f"  String value keys: {analysis['string_value_keys']}")
                
                if analysis['type_issues']:
                    print(f"  Type issues: {len(analysis['type_issues'])}")
                    for issue in analysis['type_issues'][:5]:  # Show first 5
                        print(f"    {issue}")
        else:
            print(f"\n{key}: NOT FOUND")

def fix_descriptor_types_in_session_state():
    """Fix descriptor types in session state by re-storing with validation."""
    descriptor_keys = ['calculated_descriptors', 'individual_well_analysis_results']
    
    for key in descriptor_keys:
        if key in st.session_state:
            descriptors = safe_retrieve_descriptors(key)
            safe_store_descriptors(descriptors, key)
            logging.info(f"Fixed descriptor types for session state key: {key}")
